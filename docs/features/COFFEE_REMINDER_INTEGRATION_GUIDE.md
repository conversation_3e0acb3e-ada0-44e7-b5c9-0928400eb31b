# ☕ Coffee Reminder System - Integration Guide

## 🎯 **What We Built**

A smart, tasteful coffee reminder system that tracks when users are getting lots of value from AI-powered features and gently suggests supporting the platform. This maintains the FAAFO brand personality - authentic, humorous, and emphasizing that donations are completely optional.

## 🧠 **How It Works**

### **Smart Tracking**
- Tracks actual API usage, not just page visits
- Different features have different "cost" weights based on AI intensity
- Accumulates usage over time and triggers reminders at meaningful thresholds

### **Tasteful Reminders**
- Only shows after significant usage (25+ cost points)
- 24-hour cooldown between reminders
- Auto-dismisses after 15 seconds
- Emphasizes donations are 100% optional

### **Authentic Messaging**
- Matches FAAFO's humorous, self-deprecating tone
- Personalizes messages based on usage patterns
- Shows which features were used most

## 📁 **Files Created**

### **1. Core Tracking System**
- `src/lib/services/api-usage-tracker.ts` - Main tracking logic
- `src/hooks/useAPIUsageTracker.ts` - React hooks for easy integration
- `src/components/ui/CoffeeReminderToast.tsx` - The reminder component

### **2. Integration**
- Updated `src/app/layout.tsx` to include the reminder toast globally

## 🎮 **Feature Cost Weights**

```typescript
const API_INTENSIVE_FEATURES = {
  'interview-practice': { cost: 4, name: 'Interview Practice' },      // High AI usage
  'skill-gap-analyzer': { cost: 3, name: 'Skill Gap Analyzer' },     // Medium-high
  'career-assessment': { cost: 2, name: 'Career Assessment' },        // Medium
  'ai-insights': { cost: 3, name: 'AI Career Insights' },            // Medium-high
  'resume-analysis': { cost: 2, name: 'Resume Analysis' },           // Medium
  'personalized-recommendations': { cost: 2, name: 'Personalized Recommendations' },
  'career-path-analysis': { cost: 3, name: 'Career Path Analysis' }  // Medium-high
}
```

## 🔧 **How to Integrate Into Existing Features**

### **Method 1: Simple Tracking**
```typescript
import { useInterviewPracticeTracker } from '@/hooks/useAPIUsageTracker';

function InterviewPracticeComponent() {
  const { trackQuestion } = useInterviewPracticeTracker();

  const handleAskQuestion = async () => {
    // Track the API usage
    trackQuestion();
    
    // Make your API call
    const response = await fetch('/api/interview/question', {
      method: 'POST',
      body: JSON.stringify({ /* your data */ })
    });
    
    // Handle response...
  };

  return (
    <button onClick={handleAskQuestion}>
      Ask Interview Question
    </button>
  );
}
```

### **Method 2: Timed Tracking (Recommended)**
```typescript
import { useInterviewPracticeTracker } from '@/hooks/useAPIUsageTracker';

function InterviewPracticeComponent() {
  const { trackTimedUsage } = useAPIUsageTracker();

  const handleAskQuestion = async () => {
    try {
      // This automatically tracks usage after successful completion
      const response = await trackTimedUsage('interview-practice', async () => {
        return fetch('/api/interview/question', {
          method: 'POST',
          body: JSON.stringify({ /* your data */ })
        });
      });
      
      // Handle successful response...
    } catch (error) {
      // Usage is still tracked even if API fails
      console.error('Interview question failed:', error);
    }
  };

  return (
    <button onClick={handleAskQuestion}>
      Ask Interview Question
    </button>
  );
}
```

### **Method 3: Batch Tracking**
```typescript
import { useInterviewPracticeTracker } from '@/hooks/useAPIUsageTracker';

function InterviewSessionComponent() {
  const { trackSession } = useInterviewPracticeTracker();

  const handleCompleteSession = async (questions: Question[]) => {
    // Process all questions...
    const results = await processInterviewSession(questions);
    
    // Track the entire session at once
    trackSession(questions.length);
    
    return results;
  };
}
```

## 🎨 **Reminder Messages**

The system generates personalized messages based on usage patterns:

### **First-time High Usage**
> "Wow! You've used Interview Practice 8 times today. You're really getting value from this! ☕"

### **Regular Power User**
> "You're becoming a power user! 12 AI calls today. The servers are working hard for you 🔥"

### **Frequent User**
> "12 AI calls today! You're really making progress on your career journey 🚀"

### **Heavy User**
> "Holy coffee! 25 AI calls today. You're absolutely crushing it! The AI is probably tired 😅"

## 🎯 **Thresholds & Timing**

### **Trigger Conditions**
- **Coffee Reminder Threshold**: 25 cost points in a session
- **High Usage Threshold**: 15 cost points (for internal tracking)
- **Reminder Cooldown**: 24 hours between reminders
- **Auto-dismiss**: 15 seconds

### **Example Scenarios**
- **Interview Practice**: 6-7 questions = reminder
- **Skill Gap Analysis**: 8-9 analyses = reminder  
- **Mixed Usage**: 3 interviews + 4 assessments + 2 analyses = reminder

## 🔧 **Integration Checklist**

### **For New Features**
1. ✅ Add feature to `API_INTENSIVE_FEATURES` with appropriate cost
2. ✅ Import the appropriate hook (`useAPIUsageTracker`, `useInterviewPracticeTracker`, etc.)
3. ✅ Call tracking function when API is used
4. ✅ Test that reminders appear after threshold usage

### **For Existing Features**
1. ✅ Identify API-heavy operations
2. ✅ Choose appropriate tracking method (simple, timed, or batch)
3. ✅ Add tracking calls to API operations
4. ✅ Test integration with realistic usage patterns

## 🧪 **Testing the System**

### **Manual Testing**
```typescript
// In browser console:
import { APIUsageTracker } from '@/lib/services/api-usage-tracker';

// Reset stats for testing
APIUsageTracker.resetStats();

// Simulate high usage
for (let i = 0; i < 7; i++) {
  APIUsageTracker.trackUsage('interview-practice');
}

// Check if reminder should show
console.log(APIUsageTracker.shouldShowReminder()); // Should be true
```

### **Component Testing**
```typescript
// Test component with usage tracking
const { trackQuestion, usageSummary } = useInterviewPracticeTracker();

// Simulate multiple uses
for (let i = 0; i < 7; i++) {
  trackQuestion();
}

console.log(usageSummary); // Check usage stats
```

## 🎉 **Benefits**

### **For Users**
- ✅ **Non-intrusive**: Only appears when getting real value
- ✅ **Authentic**: Matches FAAFO's personality perfectly
- ✅ **Optional**: Clear emphasis that donations aren't required
- ✅ **Informative**: Shows their usage patterns

### **For Platform**
- ✅ **Value-based**: Asks for support when users are clearly benefiting
- ✅ **Smart timing**: 24-hour cooldown prevents annoyance
- ✅ **Data-driven**: Based on actual API usage, not arbitrary triggers
- ✅ **Brand-consistent**: Maintains authentic, humorous voice

## 🚀 **Next Steps**

1. **Integrate into existing features** using the examples above
2. **Monitor usage patterns** to adjust thresholds if needed
3. **Add new features** to the tracking system as they're built
4. **Customize messages** based on user feedback

The coffee reminder system is now ready to help users support the platform while maintaining the authentic, non-pushy FAAFO experience! ☕🚀
