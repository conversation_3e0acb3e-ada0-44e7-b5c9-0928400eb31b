{"summary": {"totalResourcesProcessed": 25, "totalResourcesRemoved": 23, "totalResourcesKept": 2, "improvementsByCareerPath": {"UX/UI Designer": {"removed": 12, "kept": 0, "total": 12}, "Freelance Web Developer": {"removed": 9, "kept": 2, "total": 11}, "Simple Online Business Owner": {"removed": 2, "kept": 0, "total": 2}}, "improvementsByCategory": {"WEB_DEVELOPMENT": 5, "PROJECT_MANAGEMENT": 2, "Unknown": 5, "LANGUAGE_LEARNING": 1, "BLOCKCHAIN": 4, "MOBILE_DEVELOPMENT": 8}, "improvementsByReason": {"Web development resource not relevant to UX/UI design": 12, "Blockchain technology not core requirement for this career path": 4, "Generic corporate project management not relevant to freelance web development - freelancers need client management and project delivery skills instead": 1, "Native iOS development requires specialized skills not core to web development": 2, "Native Android development requires specialized skills not core to web development": 2, "React Native allows web developers to leverage React skills for mobile development": 1, "Flutter uses Dart language and different paradigms from web development": 1, "Mobile UI/UX principles are essential for responsive web design": 1, "App store optimization not relevant to web developers": 1}, "generatedAt": "2025-08-12T11:08:33.017Z"}, "relevanceImprovements": [{"resourceId": "188413a1-cd56-43b5-bb04-51f6d3582b75", "resourceTitle": "freeCodeCamp Full Stack Development", "careerPath": "UX/UI Designer", "category": "WEB_DEVELOPMENT", "author": "freeCodeCamp", "url": "https://www.freecodecamp.org/", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": ["UX/UI specific design tools and methodologies", "User research and usability testing resources", "Design system and component library guides"], "removedAt": "2025-08-12T10:53:13.760Z", "source": "ux-irrelevant-removal-log", "status": "SUCCESS"}, {"resourceId": "4e23577f-cdbd-459f-b5f4-cdca8bac0cd9", "resourceTitle": "React Official Tutorial", "careerPath": "UX/UI Designer", "category": "WEB_DEVELOPMENT", "author": "Meta", "url": "https://react.dev/learn", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": ["UX/UI specific design tools and methodologies", "User research and usability testing resources", "Design system and component library guides"], "removedAt": "2025-08-12T10:53:14.804Z", "source": "ux-irrelevant-removal-log", "status": "SUCCESS"}, {"resourceId": "7f834836-f4e4-4c2e-a295-6f4532b9512a", "resourceTitle": "The Odin Project", "careerPath": "UX/UI Designer", "category": "WEB_DEVELOPMENT", "author": "The Odin Project", "url": "https://www.theodinproject.com/", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": ["UX/UI specific design tools and methodologies", "User research and usability testing resources", "Design system and component library guides"], "removedAt": "2025-08-12T10:53:15.866Z", "source": "ux-irrelevant-removal-log", "status": "SUCCESS"}, {"resourceId": "16dd0ef7-737a-4e56-8323-e362e856450a", "resourceTitle": "MDN Web Docs", "careerPath": "UX/UI Designer", "category": "WEB_DEVELOPMENT", "author": "Mozilla", "url": "https://developer.mozilla.org/en-US/docs/Learn", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": ["UX/UI specific design tools and methodologies", "User research and usability testing resources", "Design system and component library guides"], "removedAt": "2025-08-12T10:53:16.898Z", "source": "ux-irrelevant-removal-log", "status": "SUCCESS"}, {"resourceId": "2486e3ff-237c-4467-98b1-076312d844af", "resourceTitle": "Node.js Developer Roadmap", "careerPath": "UX/UI Designer", "category": "WEB_DEVELOPMENT", "author": "Node.js Foundation", "url": "https://nodejs.org/en/learn", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": ["UX/UI specific design tools and methodologies", "User research and usability testing resources", "Design system and component library guides"], "removedAt": "2025-08-12T10:53:17.977Z", "source": "ux-irrelevant-removal-log", "status": "SUCCESS"}, {"resourceId": "aff9d475-38a0-44e1-b4b1-9a5fcd67ebdf", "resourceTitle": "Project Management Foundations", "careerPath": "UX/UI Designer", "category": "PROJECT_MANAGEMENT", "author": "LinkedIn Learning", "url": "https://www.linkedin.com/learning/project-management-foundations-2019", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": [], "removedAt": "2025-08-12T10:53:19.024Z", "source": "ux-irrelevant-removal-log", "status": "SUCCESS"}, {"resourceId": null, "resourceTitle": "Introduction to Project Management", "careerPath": "UX/UI Designer", "category": "Unknown", "author": "Unknown", "url": "Unknown", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": [], "removedAt": "2025-08-12T10:53:19.024Z", "source": "ux-irrelevant-removal-log", "status": "NOT_FOUND"}, {"resourceId": null, "resourceTitle": "Business English for International Careers", "careerPath": "UX/UI Designer", "category": "Unknown", "author": "Unknown", "url": "Unknown", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": [], "removedAt": "2025-08-12T10:53:19.024Z", "source": "ux-irrelevant-removal-log", "status": "NOT_FOUND"}, {"resourceId": null, "resourceTitle": "Technical Communication Skills", "careerPath": "UX/UI Designer", "category": "Unknown", "author": "Unknown", "url": "Unknown", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": [], "removedAt": "2025-08-12T10:53:19.024Z", "source": "ux-irrelevant-removal-log", "status": "NOT_FOUND"}, {"resourceId": null, "resourceTitle": "Cross-Cultural Communication", "careerPath": "UX/UI Designer", "category": "Unknown", "author": "Unknown", "url": "Unknown", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": [], "removedAt": "2025-08-12T10:53:19.025Z", "source": "ux-irrelevant-removal-log", "status": "NOT_FOUND"}, {"resourceId": "9a77cea6-eae2-438f-86fc-1090dd196e30", "resourceTitle": "Professional Presentation Skills", "careerPath": "UX/UI Designer", "category": "LANGUAGE_LEARNING", "author": "Toastmasters International", "url": "https://www.toastmasters.org/education/pathways-learning-experience", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": [], "removedAt": "2025-08-12T10:53:20.060Z", "source": "ux-irrelevant-removal-log", "status": "SUCCESS"}, {"resourceId": null, "resourceTitle": "Test Project Resource", "careerPath": "UX/UI Designer", "category": "Unknown", "author": "Unknown", "url": "Unknown", "action": "DISCONNECTED", "reason": "Web development resource not relevant to UX/UI design", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": [], "removedAt": "2025-08-12T10:53:20.060Z", "source": "ux-irrelevant-removal-log", "status": "NOT_FOUND"}, {"resourceId": "08f4f905-48a7-4ccd-8446-5d9a8cb97959", "resourceTitle": "Blockchain Basics", "careerPath": "Freelance Web Developer", "category": "BLOCKCHAIN", "author": "University at Buffalo", "url": "https://www.coursera.org/learn/blockchain-basics", "action": "DISCONNECTED", "reason": "Blockchain technology not core requirement for this career path", "priority": "MEDIUM", "relevanceScore": 15, "alternativeSuggestions": [], "removedAt": "2025-08-12T11:05:09.829Z", "source": "blockchain-removal-log", "status": "SUCCESS"}, {"resourceId": "190093e4-f1fc-4847-b821-9e70f05ccd12", "resourceTitle": "Blockchain Fundamentals", "careerPath": "Freelance Web Developer", "category": "BLOCKCHAIN", "author": "UC Berkeley", "url": "https://www.edx.org/professional-certificate/uc-berkeleyx-blockchain-fundamentals", "action": "DISCONNECTED", "reason": "Blockchain technology not core requirement for this career path", "priority": "MEDIUM", "relevanceScore": 15, "alternativeSuggestions": [], "removedAt": "2025-08-12T11:05:10.886Z", "source": "blockchain-removal-log", "status": "SUCCESS"}, {"resourceId": "08f4f905-48a7-4ccd-8446-5d9a8cb97959", "resourceTitle": "Blockchain Basics", "careerPath": "Simple Online Business Owner", "category": "BLOCKCHAIN", "author": "University at Buffalo", "url": "https://www.coursera.org/learn/blockchain-basics", "action": "DISCONNECTED", "reason": "Blockchain technology not core requirement for this career path", "priority": "MEDIUM", "relevanceScore": 15, "alternativeSuggestions": [], "removedAt": "2025-08-12T11:05:13.107Z", "source": "blockchain-removal-log", "status": "SUCCESS"}, {"resourceId": "190093e4-f1fc-4847-b821-9e70f05ccd12", "resourceTitle": "Blockchain Fundamentals", "careerPath": "Simple Online Business Owner", "category": "BLOCKCHAIN", "author": "UC Berkeley", "url": "https://www.edx.org/professional-certificate/uc-berkeleyx-blockchain-fundamentals", "action": "DISCONNECTED", "reason": "Blockchain technology not core requirement for this career path", "priority": "MEDIUM", "relevanceScore": 15, "alternativeSuggestions": [], "removedAt": "2025-08-12T11:05:14.169Z", "source": "blockchain-removal-log", "status": "SUCCESS"}, {"resourceId": "aff9d475-38a0-44e1-b4b1-9a5fcd67ebdf", "resourceTitle": "Project Management Foundations", "careerPath": "Freelance Web Developer", "category": "PROJECT_MANAGEMENT", "author": "LinkedIn Learning", "url": "https://www.linkedin.com/learning/project-management-foundations-2019", "action": "DISCONNECTED", "reason": "Generic corporate project management not relevant to freelance web development - freelancers need client management and project delivery skills instead", "priority": "MEDIUM", "relevanceScore": 30, "alternativeSuggestions": ["Client communication and project delivery for freelancers", "Time tracking and invoicing tools", "Freelance business management"], "removedAt": "2025-08-12T11:06:14.670Z", "source": "project-management-removal-log", "status": "SUCCESS"}, {"resourceId": "2a31dc8c-63c2-473c-a0ba-79d0980be296", "resourceTitle": "iOS App Development for Beginners", "careerPath": "Freelance Web Developer", "category": "MOBILE_DEVELOPMENT", "author": "Apple Developer", "url": "https://developer.apple.com/tutorials/swiftui", "action": "REMOVED", "reason": "Native iOS development requires specialized skills not core to web development", "priority": "LOW", "relevanceScore": 15, "alternativeSuggestions": ["Progressive Web App (PWA) development", "Responsive web design techniques", "Web performance optimization"], "removedAt": "2025-08-12T11:07:19.260Z", "source": "mobile-resources-audit-log", "status": "SUCCESS"}, {"resourceId": "61ba2e43-6c34-4431-a282-c1f76711be37", "resourceTitle": "Android Development Fundamentals", "careerPath": "Freelance Web Developer", "category": "MOBILE_DEVELOPMENT", "author": "Google Android", "url": "https://developer.android.com/courses/android-basics-kotlin/course", "action": "REMOVED", "reason": "Native Android development requires specialized skills not core to web development", "priority": "LOW", "relevanceScore": 15, "alternativeSuggestions": ["Progressive Web App (PWA) development", "Responsive web design techniques", "Web performance optimization"], "removedAt": "2025-08-12T11:07:20.313Z", "source": "mobile-resources-audit-log", "status": "SUCCESS"}, {"resourceId": "18eec276-69de-41b1-9135-d92e6d0ecff2", "resourceTitle": "React Native Complete Guide", "careerPath": "Freelance Web Developer", "category": "MOBILE_DEVELOPMENT", "author": "Meta", "url": "https://reactnative.dev/docs/tutorial", "action": "KEPT", "reason": "React Native allows web developers to leverage React skills for mobile development", "priority": "HIGH", "relevanceScore": 95, "alternativeSuggestions": [], "removedAt": "2025-08-12T11:07:20.313Z", "source": "mobile-resources-audit-log", "status": "KEPT"}, {"resourceId": "8b286ed6-2616-47d7-ac3d-b4a79d2646ef", "resourceTitle": "Flutter Development Bootcamp", "careerPath": "Freelance Web Developer", "category": "MOBILE_DEVELOPMENT", "author": "Google Flutter", "url": "https://flutter.dev/docs/get-started/codelab", "action": "REMOVED", "reason": "Flutter uses Dart language and different paradigms from web development", "priority": "LOW", "relevanceScore": 30, "alternativeSuggestions": ["Progressive Web App (PWA) development", "Responsive web design techniques", "Web performance optimization"], "removedAt": "2025-08-12T11:07:21.388Z", "source": "mobile-resources-audit-log", "status": "SUCCESS"}, {"resourceId": "62ed3496-a3d4-4271-aac4-2fed077e1a4a", "resourceTitle": "Mobile UI/UX Design Principles", "careerPath": "Freelance Web Developer", "category": "MOBILE_DEVELOPMENT", "author": "Google Material Design", "url": "https://material.io/design/introduction", "action": "KEPT", "reason": "Mobile UI/UX principles are essential for responsive web design", "priority": "MEDIUM", "relevanceScore": 95, "alternativeSuggestions": [], "removedAt": "2025-08-12T11:07:21.388Z", "source": "mobile-resources-audit-log", "status": "KEPT"}, {"resourceId": "934df586-4fe3-42ad-a244-fc5dbe1ab857", "resourceTitle": "Advanced iOS Development", "careerPath": "Freelance Web Developer", "category": "MOBILE_DEVELOPMENT", "author": "Apple Developer", "url": "https://developer.apple.com/documentation/technologies", "action": "REMOVED", "reason": "Native iOS development requires specialized skills not core to web development", "priority": "LOW", "relevanceScore": 15, "alternativeSuggestions": ["Progressive Web App (PWA) development", "Responsive web design techniques", "Web performance optimization"], "removedAt": "2025-08-12T11:07:22.431Z", "source": "mobile-resources-audit-log", "status": "SUCCESS"}, {"resourceId": "0d4af495-f555-483f-8005-12bc8b3851c0", "resourceTitle": "Android Architecture Components", "careerPath": "Freelance Web Developer", "category": "MOBILE_DEVELOPMENT", "author": "Google Android", "url": "https://developer.android.com/topic/architecture", "action": "REMOVED", "reason": "Native Android development requires specialized skills not core to web development", "priority": "LOW", "relevanceScore": 15, "alternativeSuggestions": ["Progressive Web App (PWA) development", "Responsive web design techniques", "Web performance optimization"], "removedAt": "2025-08-12T11:07:23.508Z", "source": "mobile-resources-audit-log", "status": "SUCCESS"}, {"resourceId": "877e1441-3ee3-4859-81e2-980b55cb0852", "resourceTitle": "App Store Optimization (ASO)", "careerPath": "Freelance Web Developer", "category": "MOBILE_DEVELOPMENT", "author": "Apple Developer", "url": "https://developer.apple.com/app-store/product-page/", "action": "REMOVED", "reason": "App store optimization not relevant to web developers", "priority": "LOW", "relevanceScore": 30, "alternativeSuggestions": ["Progressive Web App (PWA) development", "Responsive web design techniques", "Web performance optimization"], "removedAt": "2025-08-12T11:07:24.550Z", "source": "mobile-resources-audit-log", "status": "SUCCESS"}]}