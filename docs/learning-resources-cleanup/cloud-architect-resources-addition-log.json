[{"resourceId": "ba2c1a60-2bda-4b80-bab7-126a8738f94e", "resourceTitle": "AWS Well-Architected Framework", "author": "Amazon Web Services", "url": "https://aws.amazon.com/architecture/well-architected/", "category": "DEVOPS", "skillLevel": "INTERMEDIATE", "cost": "FREE", "careerPath": "Cloud Solutions Architect", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:12:44.261Z", "status": "SUCCESS"}, {"resourceId": "5c65d986-a2fc-4610-983c-a27fcc5d27b2", "resourceTitle": "Microsoft Azure Architecture Center", "author": "Microsoft", "url": "https://docs.microsoft.com/en-us/azure/architecture/", "category": "DEVOPS", "skillLevel": "INTERMEDIATE", "cost": "FREE", "careerPath": "Cloud Solutions Architect", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:12:45.380Z", "status": "SUCCESS"}, {"resourceId": "854501ad-dcb9-46ae-8175-2a830718421b", "resourceTitle": "Google Cloud Architecture Framework", "author": "Google Cloud", "url": "https://cloud.google.com/architecture/framework", "category": "DEVOPS", "skillLevel": "INTERMEDIATE", "cost": "FREE", "careerPath": "Cloud Solutions Architect", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:12:46.307Z", "status": "SUCCESS"}, {"resourceId": "78da5e16-e85d-439b-9ca0-97e1944c7448", "resourceTitle": "AWS Solutions Architect Associate Certification", "author": "Amazon Web Services", "url": "https://aws.amazon.com/certification/certified-solutions-architect-associate/", "category": "DEVOPS", "skillLevel": "INTERMEDIATE", "cost": "PAID", "careerPath": "Cloud Solutions Architect", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:12:47.247Z", "status": "SUCCESS"}, {"resourceId": "196f11ca-b0b1-4964-90a3-f3056bd56198", "resourceTitle": "Cloud Security Best Practices", "author": "Google Cloud", "url": "https://cloud.google.com/security/best-practices", "category": "CYBERSECURITY", "skillLevel": "INTERMEDIATE", "cost": "FREE", "careerPath": "Cloud Solutions Architect", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:12:48.160Z", "status": "SUCCESS"}, {"resourceId": "81e24017-831a-44a7-9f07-49f3cec463c8", "resourceTitle": "Multi-Cloud Architecture Patterns", "author": "Red Hat", "url": "https://www.redhat.com/en/topics/cloud-computing/what-is-multicloud", "category": "DEVOPS", "skillLevel": "ADVANCED", "cost": "FREE", "careerPath": "Cloud Solutions Architect", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:12:49.080Z", "status": "SUCCESS"}, {"resourceId": "f0052540-b541-45de-a415-d01e19d81bc6", "resourceTitle": "Cloud Cost Optimization Strategies", "author": "Amazon Web Services", "url": "https://aws.amazon.com/aws-cost-management/", "category": "DEVOPS", "skillLevel": "INTERMEDIATE", "cost": "FREE", "careerPath": "Cloud Solutions Architect", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:12:50.102Z", "status": "SUCCESS"}, {"resourceId": "69f3fb2f-3daa-4cea-9070-0c8f3f313782", "resourceTitle": "Serverless Architecture Patterns", "author": "Amazon Web Services", "url": "https://aws.amazon.com/serverless/", "category": "DEVOPS", "skillLevel": "INTERMEDIATE", "cost": "FREE", "careerPath": "Cloud Solutions Architect", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:12:51.009Z", "status": "SUCCESS"}, {"resourceId": "e3cb3d88-feec-4e46-b0f7-72bf3e8f26be", "resourceTitle": "Cloud Migration Strategies", "author": "Microsoft", "url": "https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/migrate/", "category": "DEVOPS", "skillLevel": "ADVANCED", "cost": "FREE", "careerPath": "Cloud Solutions Architect", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:12:51.920Z", "status": "SUCCESS"}, {"resourceId": "254afb4d-3156-4434-8743-57692ad58ca2", "resourceTitle": "Infrastructure as Code with Terraform", "author": "HashiCorp", "url": "https://learn.hashicorp.com/terraform", "category": "DEVOPS", "skillLevel": "INTERMEDIATE", "cost": "FREE", "careerPath": "Cloud Solutions Architect", "action": "CONNECTED_EXISTING", "addedAt": "2025-08-12T11:12:53.379Z", "status": "SUCCESS"}]