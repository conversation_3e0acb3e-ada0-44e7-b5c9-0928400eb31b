[{"resourceId": "2a31dc8c-63c2-473c-a0ba-79d0980be296", "resourceTitle": "iOS App Development for Beginners", "category": "MOBILE_DEVELOPMENT", "author": "Apple Developer", "url": "https://developer.apple.com/tutorials/swiftui", "description": "Complete introduction to iOS development with Swift and SwiftUI", "careerPath": "Freelance Web Developer", "action": "REMOVED", "reason": "Native iOS development requires specialized skills not core to web development", "priority": "LOW", "removedAt": "2025-08-12T11:07:19.260Z", "status": "SUCCESS"}, {"resourceId": "61ba2e43-6c34-4431-a282-c1f76711be37", "resourceTitle": "Android Development Fundamentals", "category": "MOBILE_DEVELOPMENT", "author": "Google Android", "url": "https://developer.android.com/courses/android-basics-kotlin/course", "description": "Learn Android app development with Kotlin and Android Studio", "careerPath": "Freelance Web Developer", "action": "REMOVED", "reason": "Native Android development requires specialized skills not core to web development", "priority": "LOW", "removedAt": "2025-08-12T11:07:20.313Z", "status": "SUCCESS"}, {"resourceId": "18eec276-69de-41b1-9135-d92e6d0ecff2", "resourceTitle": "React Native Complete Guide", "category": "MOBILE_DEVELOPMENT", "author": "Meta", "url": "https://reactnative.dev/docs/tutorial", "description": "Build cross-platform mobile apps with React Native", "careerPath": "Freelance Web Developer", "action": "KEPT", "reason": "React Native allows web developers to leverage React skills for mobile development", "priority": "HIGH", "removedAt": "2025-08-12T11:07:20.313Z", "status": "KEPT"}, {"resourceId": "8b286ed6-2616-47d7-ac3d-b4a79d2646ef", "resourceTitle": "Flutter Development Bootcamp", "category": "MOBILE_DEVELOPMENT", "author": "Google Flutter", "url": "https://flutter.dev/docs/get-started/codelab", "description": "Complete Flutter and Dart development course for cross-platform apps", "careerPath": "Freelance Web Developer", "action": "REMOVED", "reason": "Flutter uses Dart language and different paradigms from web development", "priority": "LOW", "removedAt": "2025-08-12T11:07:21.388Z", "status": "SUCCESS"}, {"resourceId": "62ed3496-a3d4-4271-aac4-2fed077e1a4a", "resourceTitle": "Mobile UI/UX Design Principles", "category": "MOBILE_DEVELOPMENT", "author": "Google Material Design", "url": "https://material.io/design/introduction", "description": "Design principles and best practices for mobile user interfaces", "careerPath": "Freelance Web Developer", "action": "KEPT", "reason": "Mobile UI/UX principles are essential for responsive web design", "priority": "MEDIUM", "removedAt": "2025-08-12T11:07:21.388Z", "status": "KEPT"}, {"resourceId": "934df586-4fe3-42ad-a244-fc5dbe1ab857", "resourceTitle": "Advanced iOS Development", "category": "MOBILE_DEVELOPMENT", "author": "Apple Developer", "url": "https://developer.apple.com/documentation/technologies", "description": "Advanced iOS concepts including Core Data, networking, and app architecture", "careerPath": "Freelance Web Developer", "action": "REMOVED", "reason": "Native iOS development requires specialized skills not core to web development", "priority": "LOW", "removedAt": "2025-08-12T11:07:22.431Z", "status": "SUCCESS"}, {"resourceId": "0d4af495-f555-483f-8005-12bc8b3851c0", "resourceTitle": "Android Architecture Components", "category": "MOBILE_DEVELOPMENT", "author": "Google Android", "url": "https://developer.android.com/topic/architecture", "description": "Learn MVVM, Room database, and modern Android architecture patterns", "careerPath": "Freelance Web Developer", "action": "REMOVED", "reason": "Native Android development requires specialized skills not core to web development", "priority": "LOW", "removedAt": "2025-08-12T11:07:23.508Z", "status": "SUCCESS"}, {"resourceId": "877e1441-3ee3-4859-81e2-980b55cb0852", "resourceTitle": "App Store Optimization (ASO)", "category": "MOBILE_DEVELOPMENT", "author": "Apple Developer", "url": "https://developer.apple.com/app-store/product-page/", "description": "Learn how to optimize mobile apps for app store discovery and downloads", "careerPath": "Freelance Web Developer", "action": "REMOVED", "reason": "App store optimization not relevant to web developers", "priority": "LOW", "removedAt": "2025-08-12T11:07:24.550Z", "status": "SUCCESS"}]