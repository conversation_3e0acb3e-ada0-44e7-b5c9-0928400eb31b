[{"resourceId": "80c77d4d-6d4b-468f-a37b-14fdb81a4ced", "resourceTitle": "Ethical Hacking Essentials (E|HE)", "careerPaths": "Cybersecurity Specialist", "url": "https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/", "author": "EC-Council", "category": "CYBERSECURITY", "reason": "REVIEW - Assess if legitimate resource with minor issues", "indicators": ["Test/placeholder in description"], "deletedAt": "2025-08-12T10:45:47.440Z", "status": "FAILED", "error": "\nInvalid `prisma.learningResource.delete()` invocation in\n/Users/<USER>/faafo/faafo/faafo-career-platform/scripts/cleanup/remove-placeholder-resources.js:26:69\n\n  23 \n  24 try {\n  25   // Remove the resource (Prisma will handle the many-to-many relationship cleanup automatically)\n→ 26   const deletedResource = await prisma.learningResource.delete(\nForeign key constraint violated on the constraint: `UserLearningProgress_resourceId_fkey`"}, {"resourceId": "6e9f2f0e-f0f3-42c6-82dd-fa3579957fee", "resourceTitle": "Mobile App Testing and Deployment", "careerPaths": "Freelance Web Developer", "url": "https://firebase.google.com/docs/app-distribution", "author": "Google Firebase", "category": "MOBILE_DEVELOPMENT", "reason": "IMMEDIATE REMOVAL - Obviously test data", "indicators": ["Test/placeholder in title", "Test/placeholder in description"], "deletedAt": "2025-08-12T10:45:47.600Z", "status": "SUCCESS"}, {"resourceId": "2e2088b2-ac2e-455a-89bc-f6faea662884", "resourceTitle": "Test Project Resource", "careerPaths": "Freelance Web Developer, UX/UI Designer", "url": "https://example.com/test-project-1753655074370", "author": null, "category": "WEB_DEVELOPMENT", "reason": "IMMEDIATE REMOVAL - Placeholder URL cannot be accessed", "indicators": ["Placeholder URL domain", "Test/placeholder URL pattern", "Test/placeholder in title", "Test/placeholder in description", "Missing or placeholder author", "Suspicious URL pattern with numbers"], "deletedAt": "2025-08-12T10:45:47.747Z", "status": "SUCCESS"}]