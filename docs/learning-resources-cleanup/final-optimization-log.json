[{"resourceTitle": "Docker Getting Started", "targetCareerPath": "DevOps Engineer", "reason": "Essential Docker fundamentals for DevOps engineers", "status": "SUCCESS", "timestamp": "2025-08-13T08:31:29.860Z"}, {"resourceTitle": "AWS Cloud Practitioner Essentials", "targetCareerPath": "Cloud Solutions Architect", "reason": "Foundational AWS knowledge for cloud architects", "status": "SUCCESS", "timestamp": "2025-08-13T08:31:31.047Z"}, {"resourceTitle": "Microsoft Azure Fundamentals", "targetCareerPath": "Cloud Solutions Architect", "reason": "Azure basics essential for multi-cloud architects", "status": "SUCCESS", "timestamp": "2025-08-13T08:31:32.230Z"}, {"resourceTitle": "Kubernetes Basics", "targetCareerPath": "DevOps Engineer", "reason": "Kubernetes fundamentals for container orchestration", "status": "SUCCESS", "timestamp": "2025-08-13T08:31:33.401Z"}, {"resourceTitle": "AWS Solutions Architect Associate", "targetCareerPath": "Cloud Solutions Architect", "reason": "Advanced AWS certification for cloud architects", "status": "SUCCESS", "timestamp": "2025-08-13T08:31:34.586Z"}, {"resourceTitle": "AWS DevOps Engineer Professional", "targetCareerPath": "DevOps Engineer", "reason": "Professional-level AWS DevOps certification", "status": "SUCCESS", "timestamp": "2025-08-13T08:31:35.805Z"}, {"resourceTitle": "Agile Development Specialization", "targetCareerPath": "Product Manager", "reason": "Agile methodologies essential for product managers", "status": "SUCCESS", "timestamp": "2025-08-13T08:31:36.978Z"}, {"resourceTitle": "React Native Complete Guide", "targetCareerPath": "Freelance Web Developer", "reason": "React Native allows web developers to expand to mobile", "status": "ALREADY_CONNECTED", "timestamp": "2025-08-13T08:31:37.359Z"}, {"resourceTitle": "Overcoming Six Fears of Midlife Career Change", "targetCareerPath": "Simple Online Business Owner", "reason": "Career transition guidance for entrepreneurs", "status": "SUCCESS", "timestamp": "2025-08-13T08:31:38.588Z"}, {"resourceTitle": "Google Analytics Academy", "targetCareerPath": "Digital Marketing Specialist", "reason": "Additional high-quality resource to strengthen career path", "status": "CREATED_NEW", "timestamp": "2025-08-13T08:31:40.310Z"}, {"resourceTitle": "Facebook Blueprint", "targetCareerPath": "Digital Marketing Specialist", "reason": "Additional high-quality resource to strengthen career path", "status": "CREATED_NEW", "timestamp": "2025-08-13T08:31:41.358Z"}, {"resourceTitle": "Material Design Guidelines", "targetCareerPath": "UX/UI Designer", "reason": "Additional high-quality resource to strengthen career path", "status": "CREATED_NEW", "timestamp": "2025-08-13T08:31:42.428Z"}]