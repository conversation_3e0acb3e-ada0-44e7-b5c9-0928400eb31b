PRAGMA foreign_keys=OFF;
BEGIN TRANSACTION;
CREATE TABLE IF NOT EXISTS "_prisma_migrations" (
    "id"                    TEXT PRIMARY KEY NOT NULL,
    "checksum"              TEXT NOT NULL,
    "finished_at"           DATETIME,
    "migration_name"        TEXT NOT NULL,
    "logs"                  TEXT,
    "rolled_back_at"        DATETIME,
    "started_at"            DATETIME NOT NULL DEFAULT current_timestamp,
    "applied_steps_count"   INTEGER UNSIGNED NOT NULL DEFAULT 0
);
INSERT INTO _prisma_migrations VALUES('7b9120ed-7840-4547-89a6-165eff44ffef','f36432fc519ad8f56b9f521c0303de9b92db3eff1eb589b552f1f65efaeb826b',1749303666632,'20250607134106_init',NULL,NULL,1749303666623,1);
CREATE TABLE IF NOT EXISTS "User" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT,
    "email" TEXT NOT NULL,
    "emailVerified" DATETIME,
    "image" TEXT,
    "password" TEXT NOT NULL,
    "passwordResetToken" TEXT,
    "passwordResetExpires" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO User VALUES('cmbmab0ps0000sbyna04koci0','Test User','<EMAIL>',1749303781983,NULL,'$2b$12$Yz8QShIYzjG1s44XuYX4Zeh3x1XAfPHKCU46oaronPc3OzuaNR4ge',NULL,NULL,1749303781985,1749303781985);
INSERT INTO User VALUES('test-user-123','Test User','<EMAIL>',NULL,NULL,'hashedpassword',NULL,NULL,'2025-07-25 17:58:18','2025-07-25 17:58:18');
CREATE TABLE IF NOT EXISTS "Profile" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "bio" TEXT,
    "profilePictureUrl" TEXT,
    "socialMediaLinks" JSONB,
    CONSTRAINT "Profile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE TABLE IF NOT EXISTS "Assessment" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'IN_PROGRESS',
    "currentStep" INTEGER NOT NULL DEFAULT 0,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "completedAt" DATETIME,
    CONSTRAINT "Assessment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE TABLE IF NOT EXISTS "AssessmentResponse" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "assessmentId" TEXT NOT NULL,
    "questionKey" TEXT NOT NULL,
    "answerValue" JSONB NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "AssessmentResponse_assessmentId_fkey" FOREIGN KEY ("assessmentId") REFERENCES "Assessment" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE TABLE IF NOT EXISTS "CareerPath" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "overview" TEXT NOT NULL,
    "pros" TEXT NOT NULL,
    "cons" TEXT NOT NULL,
    "actionableSteps" JSONB NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO CareerPath VALUES('bdb91877-d89f-4d37-833c-0f606e8783c2','Freelance Web Developer','freelance-web-developer','Build websites and web applications for clients on a project basis. Enjoy flexibility and a variety of work.','["High flexibility in work hours and location","Direct control over projects and clients","Potential for high income based on skill and marketing","Continuous learning and skill development"]','["Income can be unstable, especially initially","Need to handle all aspects of business (marketing, contracts, invoicing)","Can be isolating if not proactive in networking","Requires self-discipline and time management"]','["Define your niche and services offered.","Build a strong portfolio showcasing your best work.","Set up a professional website or online presence.","Network actively online and offline to find clients.","Learn about contract negotiation and project pricing.","Establish a system for managing finances and taxes.","Continuously update your skills and stay current with web technologies."]',1,1749303680966,1749303680966);
INSERT INTO CareerPath VALUES('f38d816d-3766-42c4-9bd2-296a99c8f98d','Simple Online Business Owner','simple-online-business','Create and manage a small online business, such as an e-commerce store, blog, or niche service.','["Low startup costs compared to brick-and-mortar","Global reach and customer base","Scalability potential","Work from anywhere with an internet connection"]','["Requires diverse skills (marketing, sales, customer service)","Can be very competitive","Time-consuming to build traction and revenue","Reliant on online platforms and tools"]','["Identify a niche market or product idea.","Conduct market research to validate your idea.","Choose an e-commerce platform or website builder.","Develop a basic business plan and financial projections.","Create compelling content and product listings.","Implement a digital marketing strategy (SEO, social media).","Focus on providing excellent customer service."]',1,1749303680971,1749303680971);
CREATE TABLE IF NOT EXISTS "Skill" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
CREATE TABLE IF NOT EXISTS "Industry" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
CREATE TABLE IF NOT EXISTS "SuggestionRule" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "careerPathId" TEXT NOT NULL,
    "questionKey" TEXT NOT NULL,
    "answerValue" JSONB NOT NULL,
    "weight" REAL NOT NULL DEFAULT 1.0,
    "notes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "SuggestionRule_careerPathId_fkey" FOREIGN KEY ("careerPathId") REFERENCES "CareerPath" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO SuggestionRule VALUES('8b867f7b-12ba-48a6-a113-70826c5fe019','bdb91877-d89f-4d37-833c-0f606e8783c2','desired_outcomes_work_life','"critical"',3.0,'Critical need for work-life balance strongly suggests freelancing.',1749303680973,1749303680973);
INSERT INTO SuggestionRule VALUES('6df03918-f969-4cf0-afb8-6b90a348e896','bdb91877-d89f-4d37-833c-0f606e8783c2','financial_comfort',5,1.5,'High financial comfort makes freelancing transition easier.',1749303680974,1749303680974);
INSERT INTO SuggestionRule VALUES('5c32ac39-6e30-4a30-9a44-edaa6a6b2e58','f38d816d-3766-42c4-9bd2-296a99c8f98d','dissatisfaction_triggers','"lack_of_growth"',2.0,'Lack of growth opportunities can be addressed by starting an online business.',1749303680975,1749303680975);
CREATE TABLE IF NOT EXISTS "ForumPost" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "authorId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "ForumPost_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE TABLE IF NOT EXISTS "ForumReply" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "content" TEXT NOT NULL,
    "authorId" TEXT NOT NULL,
    "postId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "ForumReply_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "ForumReply_postId_fkey" FOREIGN KEY ("postId") REFERENCES "ForumPost" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE TABLE IF NOT EXISTS "FreedomFund" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "monthlyExpenses" REAL NOT NULL,
    "coverageMonths" INTEGER NOT NULL,
    "targetSavings" REAL NOT NULL,
    "currentSavingsAmount" REAL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "FreedomFund_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE TABLE IF NOT EXISTS "Account" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,
    CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE TABLE IF NOT EXISTS "Session" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" DATETIME NOT NULL,
    CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE TABLE IF NOT EXISTS "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" DATETIME NOT NULL
);
INSERT INTO VerificationToken VALUES('<EMAIL>','18589ccadacda06e2dff25dba803c6b42cb6722d27ca13316de5d52bf65465ff','2025-07-26T17:57:58.617Z');
CREATE TABLE IF NOT EXISTS "_CareerPathToSkill" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,
    CONSTRAINT "_CareerPathToSkill_A_fkey" FOREIGN KEY ("A") REFERENCES "CareerPath" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "_CareerPathToSkill_B_fkey" FOREIGN KEY ("B") REFERENCES "Skill" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE TABLE IF NOT EXISTS "_CareerPathToIndustry" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,
    CONSTRAINT "_CareerPathToIndustry_A_fkey" FOREIGN KEY ("A") REFERENCES "CareerPath" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "_CareerPathToIndustry_B_fkey" FOREIGN KEY ("B") REFERENCES "Industry" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");
CREATE UNIQUE INDEX "User_passwordResetToken_key" ON "User"("passwordResetToken");
CREATE UNIQUE INDEX "Profile_userId_key" ON "Profile"("userId");
CREATE INDEX "AssessmentResponse_assessmentId_idx" ON "AssessmentResponse"("assessmentId");
CREATE UNIQUE INDEX "CareerPath_name_key" ON "CareerPath"("name");
CREATE UNIQUE INDEX "CareerPath_slug_key" ON "CareerPath"("slug");
CREATE UNIQUE INDEX "Skill_name_key" ON "Skill"("name");
CREATE UNIQUE INDEX "Industry_name_key" ON "Industry"("name");
CREATE INDEX "SuggestionRule_careerPathId_idx" ON "SuggestionRule"("careerPathId");
CREATE UNIQUE INDEX "FreedomFund_userId_key" ON "FreedomFund"("userId");
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken");
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");
CREATE UNIQUE INDEX "_CareerPathToSkill_AB_unique" ON "_CareerPathToSkill"("A", "B");
CREATE INDEX "_CareerPathToSkill_B_index" ON "_CareerPathToSkill"("B");
CREATE UNIQUE INDEX "_CareerPathToIndustry_AB_unique" ON "_CareerPathToIndustry"("A", "B");
CREATE INDEX "_CareerPathToIndustry_B_index" ON "_CareerPathToIndustry"("B");
COMMIT;
