[{"resourceId": "be031740-73b6-4ee9-bb7b-9f6684b3b14d", "resourceTitle": "Docker Official Documentation", "author": "Docker Inc.", "url": "https://docs.docker.com/", "category": "DEVOPS", "skillLevel": "BEGINNER", "cost": "FREE", "careerPath": "DevOps Engineer", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:10:54.859Z", "status": "SUCCESS"}, {"resourceId": "fc388d80-68e9-4dd6-82be-2ab4495dac53", "resourceTitle": "Kubernetes Official Tutorials", "author": "Kubernetes Community", "url": "https://kubernetes.io/docs/tutorials/", "category": "DEVOPS", "skillLevel": "INTERMEDIATE", "cost": "FREE", "careerPath": "DevOps Engineer", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:10:55.810Z", "status": "SUCCESS"}, {"resourceId": "6d3a62af-cd34-4653-bf45-577422f3ce12", "resourceTitle": "AWS DevOps Learning Path", "author": "Amazon Web Services", "url": "https://aws.amazon.com/training/learning-paths/devops/", "category": "DEVOPS", "skillLevel": "INTERMEDIATE", "cost": "FREE", "careerPath": "DevOps Engineer", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:10:56.758Z", "status": "SUCCESS"}, {"resourceId": "bc7adfe1-6269-4a04-9ca2-1c9cb7fa1a05", "resourceTitle": "Terraform Documentation", "author": "HashiCorp", "url": "https://developer.hashicorp.com/terraform/docs", "category": "DEVOPS", "skillLevel": "INTERMEDIATE", "cost": "FREE", "careerPath": "DevOps Engineer", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:10:57.660Z", "status": "SUCCESS"}, {"resourceId": "bec97e93-eb4e-4593-93ef-dec5aa819a15", "resourceTitle": "Jenkins User Documentation", "author": "Jenkins Community", "url": "https://www.jenkins.io/doc/", "category": "DEVOPS", "skillLevel": "BEGINNER", "cost": "FREE", "careerPath": "DevOps Engineer", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:10:58.889Z", "status": "SUCCESS"}, {"resourceId": "c66f6bd3-9ff4-46af-b710-537f9e4bce09", "resourceTitle": "Prometheus Monitoring Guide", "author": "Prometheus Community", "url": "https://prometheus.io/docs/introduction/overview/", "category": "DEVOPS", "skillLevel": "INTERMEDIATE", "cost": "FREE", "careerPath": "DevOps Engineer", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:10:59.889Z", "status": "SUCCESS"}, {"resourceId": "4de82bbf-b91b-43c5-bb7b-a1cb259a674b", "resourceTitle": "GitLab CI/CD Documentation", "author": "GitLab", "url": "https://docs.gitlab.com/ee/ci/", "category": "DEVOPS", "skillLevel": "BEGINNER", "cost": "FREE", "careerPath": "DevOps Engineer", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:11:00.808Z", "status": "SUCCESS"}, {"resourceId": "978ac364-d31a-4235-8928-70703e551447", "resourceTitle": "Linux System Administration", "author": "Linux Foundation", "url": "https://www.edx.org/course/introduction-to-linux", "category": "DEVOPS", "skillLevel": "BEGINNER", "cost": "FREE", "careerPath": "DevOps Engineer", "action": "CREATED_NEW", "addedAt": "2025-08-12T11:11:01.740Z", "status": "SUCCESS"}]