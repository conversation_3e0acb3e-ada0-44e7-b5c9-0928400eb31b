{"timestamp": "2025-08-13T08:52:43.357Z", "databaseQueries": {"careerPathsWithResources": {"queryTime": 1955, "recordCount": 10, "totalResources": 89, "status": "GOOD"}, "singleCareerPath": {"queryTime": 795, "resourceCount": 13, "status": "GOOD"}, "allLearningResources": {"queryTime": 857, "recordCount": 89, "status": "EXCELLENT"}}, "apiSimulation": {"careerPathsList": {"responseTime": 370, "recordCount": 10, "status": "EXCELLENT"}, "filteredResources": {"responseTime": 278, "recordCount": 20, "status": "EXCELLENT"}, "complexDashboardQuery": {"responseTime": 807, "pathsReturned": 5, "totalResources": 15, "status": "GOOD"}}, "resourceCounts": {"Freelance Web Developer": {"resourceCount": 7, "categories": {"WEB_DEVELOPMENT": 5, "MOBILE_DEVELOPMENT": 2}, "skillLevels": {"BEGINNER": 4, "INTERMEDIATE": 3}}, "Simple Online Business Owner": {"resourceCount": 13, "categories": {"PROJECT_MANAGEMENT": 1, "FINANCIAL_LITERACY": 6, "ENTREPRENEURSHIP": 5, "LANGUAGE_LEARNING": 1}, "skillLevels": {"BEGINNER": 9, "INTERMEDIATE": 3, "ADVANCED": 1}}, "AI/Machine Learning Engineer": {"resourceCount": 7, "categories": {"ARTIFICIAL_INTELLIGENCE": 6, "DATA_SCIENCE": 1}, "skillLevels": {"BEGINNER": 5, "INTERMEDIATE": 1, "ADVANCED": 1}}, "Data Scientist": {"resourceCount": 9, "categories": {"ARTIFICIAL_INTELLIGENCE": 6, "DATA_SCIENCE": 3}, "skillLevels": {"BEGINNER": 7, "INTERMEDIATE": 1, "ADVANCED": 1}}, "Cybersecurity Specialist": {"resourceCount": 7, "categories": {"CYBERSECURITY": 7}, "skillLevels": {"INTERMEDIATE": 3, "BEGINNER": 4}}, "UX/UI Designer": {"resourceCount": 5, "categories": {"UX_UI_DESIGN": 5}, "skillLevels": {"BEGINNER": 3, "INTERMEDIATE": 2}}, "Digital Marketing Specialist": {"resourceCount": 5, "categories": {"DIGITAL_MARKETING": 4, "LANGUAGE_LEARNING": 1}, "skillLevels": {"BEGINNER": 4, "INTERMEDIATE": 1}}, "Product Manager": {"resourceCount": 12, "categories": {"PRODUCT_MANAGEMENT": 7, "UX_UI_DESIGN": 2, "DATA_SCIENCE": 1, "DIGITAL_MARKETING": 1, "PROJECT_MANAGEMENT": 1}, "skillLevels": {"BEGINNER": 3, "INTERMEDIATE": 7, "ADVANCED": 2}}, "Cloud Solutions Architect": {"resourceCount": 13, "categories": {"DEVOPS": 12, "CYBERSECURITY": 1}, "skillLevels": {"BEGINNER": 2, "INTERMEDIATE": 9, "ADVANCED": 2}}, "DevOps Engineer": {"resourceCount": 11, "categories": {"DEVOPS": 11}, "skillLevels": {"BEGINNER": 5, "INTERMEDIATE": 5, "ADVANCED": 1}}}, "summary": {"overallStatus": "GOOD", "recommendations": ["Consider implementing query caching for frequently accessed data"], "averageQueryTime": 843.6666666666666, "maxQueryTime": 1955}}