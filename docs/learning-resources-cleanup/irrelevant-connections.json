[{"resourceId": "c7c2e379-f95f-4d52-b3b4-3a3e00a6efa7", "resourceTitle": "Elements of AI", "careerPath": "AI/Machine Learning Engineer", "careerPathId": "d4e5c8f6-4802-41d6-9bf6-2dbd759c5aeb", "category": "ARTIFICIAL_INTELLIGENCE", "author": "University of Helsinki", "url": "https://www.elementsofai.com/", "relevanceScore": 10, "keywordMatches": 1, "matchedKeywords": ["ai"], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "7524893c-0044-4f79-aa0c-bcca39ed71d8", "resourceTitle": "AI For Everyone", "careerPath": "AI/Machine Learning Engineer", "careerPathId": "d4e5c8f6-4802-41d6-9bf6-2dbd759c5aeb", "category": "ARTIFICIAL_INTELLIGENCE", "author": "DeepLearning.AI", "url": "https://www.coursera.org/learn/ai-for-everyone", "relevanceScore": 10, "keywordMatches": 1, "matchedKeywords": ["ai"], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "c7c2e379-f95f-4d52-b3b4-3a3e00a6efa7", "resourceTitle": "Elements of AI", "careerPath": "Data Scientist", "careerPathId": "1b6d43c1-e377-4892-8f86-5<PERSON><PERSON><PERSON>1686d", "category": "ARTIFICIAL_INTELLIGENCE", "author": "University of Helsinki", "url": "https://www.elementsofai.com/", "relevanceScore": 10, "keywordMatches": 1, "matchedKeywords": ["r"], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "7524893c-0044-4f79-aa0c-bcca39ed71d8", "resourceTitle": "AI For Everyone", "careerPath": "Data Scientist", "careerPathId": "1b6d43c1-e377-4892-8f86-5<PERSON><PERSON><PERSON>1686d", "category": "ARTIFICIAL_INTELLIGENCE", "author": "DeepLearning.AI", "url": "https://www.coursera.org/learn/ai-for-everyone", "relevanceScore": 10, "keywordMatches": 1, "matchedKeywords": ["r"], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "016f0659-36e0-4754-a80e-887f60b584e7", "resourceTitle": "Business English for International Careers", "careerPath": "Digital Marketing Specialist", "careerPathId": "7914b767-0ec3-4867-8f58-8d06676f2dbf", "category": "LANGUAGE_LEARNING", "author": "University of London", "url": "https://www.futurelearn.com/courses/business-english-for-cross-cultural-communication", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Generic language course not specific to technical roles", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "489d0bd3-e10c-447b-b988-04ef8958b92f", "resourceTitle": "Technical Communication Skills", "careerPath": "Digital Marketing Specialist", "careerPathId": "7914b767-0ec3-4867-8f58-8d06676f2dbf", "category": "LANGUAGE_LEARNING", "author": "Moscow Institute of Physics and Technology", "url": "https://www.coursera.org/learn/technical-writing", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Generic communication course not specific to technical roles", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "38f7cfb5-3751-4428-96fe-18031b007f47", "resourceTitle": "Cross-Cultural Communication", "careerPath": "Digital Marketing Specialist", "careerPathId": "7914b767-0ec3-4867-8f58-8d06676f2dbf", "category": "LANGUAGE_LEARNING", "author": "University of California San Diego", "url": "https://www.edx.org/course/intercultural-communication", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Generic communication course not specific to technical roles", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "9a77cea6-eae2-438f-86fc-1090dd196e30", "resourceTitle": "Professional Presentation Skills", "careerPath": "Digital Marketing Specialist", "careerPathId": "7914b767-0ec3-4867-8f58-8d06676f2dbf", "category": "LANGUAGE_LEARNING", "author": "Toastmasters International", "url": "https://www.toastmasters.org/education/pathways-learning-experience", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "2a31dc8c-63c2-473c-a0ba-79d0980be296", "resourceTitle": "iOS App Development for Beginners", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "MOBILE_DEVELOPMENT", "author": "Apple Developer", "url": "https://developer.apple.com/tutorials/swiftui", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "08f4f905-48a7-4ccd-8446-5d9a8cb97959", "resourceTitle": "Blockchain Basics", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "BLOCKCHAIN", "author": "University at Buffalo", "url": "https://www.coursera.org/learn/blockchain-basics", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Blockchain technology not core to web development or business", "recommendation": "REMOVE - Not relevant to career path"}, {"resourceId": "190093e4-f1fc-4847-b821-9e70f05ccd12", "resourceTitle": "Blockchain Fundamentals", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "BLOCKCHAIN", "author": "UC Berkeley", "url": "https://www.edx.org/professional-certificate/uc-berkeleyx-blockchain-fundamentals", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Blockchain technology not core to web development or business", "recommendation": "REMOVE - Not relevant to career path"}, {"resourceId": "aff9d475-38a0-44e1-b4b1-9a5fcd67ebdf", "resourceTitle": "Project Management Foundations", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "PROJECT_MANAGEMENT", "author": "LinkedIn Learning", "url": "https://www.linkedin.com/learning/project-management-foundations-2019", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Generic project management not core to UX/UI design", "recommendation": "REVIEW - Consider if project management is core requirement"}, {"resourceId": "1fb7cd5f-55f0-43fc-96d8-abac809b376b", "resourceTitle": "Introduction to Project Management", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "PROJECT_MANAGEMENT", "author": "Open University", "url": "https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Generic project management not core to web development", "recommendation": "REVIEW - Consider if project management is core requirement"}, {"resourceId": "188413a1-cd56-43b5-bb04-51f6d3582b75", "resourceTitle": "freeCodeCamp Full Stack Development", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "WEB_DEVELOPMENT", "author": "freeCodeCamp", "url": "https://www.freecodecamp.org/", "relevanceScore": 9, "keywordMatches": 1, "matchedKeywords": ["web"], "reason": "Web development course not relevant to UX/UI design", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "61ba2e43-6c34-4431-a282-c1f76711be37", "resourceTitle": "Android Development Fundamentals", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "MOBILE_DEVELOPMENT", "author": "Google Android", "url": "https://developer.android.com/courses/android-basics-kotlin/course", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "4e23577f-cdbd-459f-b5f4-cdca8bac0cd9", "resourceTitle": "React Official Tutorial", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "WEB_DEVELOPMENT", "author": "Meta", "url": "https://react.dev/learn", "relevanceScore": 18, "keywordMatches": 2, "matchedKeywords": ["web", "react"], "reason": "Technical programming tutorial not core to UX/UI design", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "7f834836-f4e4-4c2e-a295-6f4532b9512a", "resourceTitle": "The Odin Project", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "WEB_DEVELOPMENT", "author": "The Odin Project", "url": "https://www.theodinproject.com/", "relevanceScore": 9, "keywordMatches": 1, "matchedKeywords": ["web"], "reason": "Full-stack web development not relevant to UX/UI design", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "16dd0ef7-737a-4e56-8323-e362e856450a", "resourceTitle": "MDN Web Docs", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "WEB_DEVELOPMENT", "author": "Mozilla", "url": "https://developer.mozilla.org/en-US/docs/Learn", "relevanceScore": 9, "keywordMatches": 1, "matchedKeywords": ["web"], "reason": "Technical web documentation not relevant to UX/UI design", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "2486e3ff-237c-4467-98b1-076312d844af", "resourceTitle": "Node.js Developer Roadmap", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "WEB_DEVELOPMENT", "author": "Node.js Foundation", "url": "https://nodejs.org/en/learn", "relevanceScore": 18, "keywordMatches": 2, "matchedKeywords": ["web", "node"], "reason": "Backend development not relevant to UX/UI design", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "18eec276-69de-41b1-9135-d92e6d0ecff2", "resourceTitle": "React Native Complete Guide", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "MOBILE_DEVELOPMENT", "author": "Meta", "url": "https://reactnative.dev/docs/tutorial", "relevanceScore": 9, "keywordMatches": 1, "matchedKeywords": ["react"], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "8b286ed6-2616-47d7-ac3d-b4a79d2646ef", "resourceTitle": "Flutter Development Bootcamp", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "MOBILE_DEVELOPMENT", "author": "Google Flutter", "url": "https://flutter.dev/docs/get-started/codelab", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "62ed3496-a3d4-4271-aac4-2fed077e1a4a", "resourceTitle": "Mobile UI/UX Design Principles", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "MOBILE_DEVELOPMENT", "author": "Google Material Design", "url": "https://material.io/design/introduction", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "934df586-4fe3-42ad-a244-fc5dbe1ab857", "resourceTitle": "Advanced iOS Development", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "MOBILE_DEVELOPMENT", "author": "Apple Developer", "url": "https://developer.apple.com/documentation/technologies", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "0d4af495-f555-483f-8005-12bc8b3851c0", "resourceTitle": "Android Architecture Components", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "MOBILE_DEVELOPMENT", "author": "Google Android", "url": "https://developer.android.com/topic/architecture", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "877e1441-3ee3-4859-81e2-980b55cb0852", "resourceTitle": "App Store Optimization (ASO)", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "MOBILE_DEVELOPMENT", "author": "Apple Developer", "url": "https://developer.apple.com/app-store/product-page/", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "6e9f2f0e-f0f3-42c6-82dd-fa3579957fee", "resourceTitle": "Mobile App Testing and Deployment", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "MOBILE_DEVELOPMENT", "author": "Google Firebase", "url": "https://firebase.google.com/docs/app-distribution", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "2e2088b2-ac2e-455a-89bc-f6faea662884", "resourceTitle": "Test Project Resource", "careerPath": "Freelance Web Developer", "careerPathId": "0953df85-5d72-48c9-a944-7fcccaefcb55", "category": "WEB_DEVELOPMENT", "author": null, "url": "https://example.com/test-project-1753655074370", "relevanceScore": 9, "keywordMatches": 1, "matchedKeywords": ["web"], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "08f4f905-48a7-4ccd-8446-5d9a8cb97959", "resourceTitle": "Blockchain Basics", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "BLOCKCHAIN", "author": "University at Buffalo", "url": "https://www.coursera.org/learn/blockchain-basics", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Blockchain technology not core to web development or business", "recommendation": "REMOVE - Not relevant to career path"}, {"resourceId": "190093e4-f1fc-4847-b821-9e70f05ccd12", "resourceTitle": "Blockchain Fundamentals", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "BLOCKCHAIN", "author": "UC Berkeley", "url": "https://www.edx.org/professional-certificate/uc-berkeleyx-blockchain-fundamentals", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Blockchain technology not core to web development or business", "recommendation": "REMOVE - Not relevant to career path"}, {"resourceId": "aff9d475-38a0-44e1-b4b1-9a5fcd67ebdf", "resourceTitle": "Project Management Foundations", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "PROJECT_MANAGEMENT", "author": "LinkedIn Learning", "url": "https://www.linkedin.com/learning/project-management-foundations-2019", "relevanceScore": 13, "keywordMatches": 1, "matchedKeywords": ["management"], "reason": "Generic project management not core to UX/UI design", "recommendation": "REVIEW - Consider if project management is core requirement"}, {"resourceId": "1fb7cd5f-55f0-43fc-96d8-abac809b376b", "resourceTitle": "Introduction to Project Management", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "PROJECT_MANAGEMENT", "author": "Open University", "url": "https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0", "relevanceScore": 13, "keywordMatches": 1, "matchedKeywords": ["management"], "reason": "Generic project management not core to web development", "recommendation": "REVIEW - Consider if project management is core requirement"}, {"resourceId": "ec4d4215-f2c1-4c2a-94a4-8efe0b2e3a66", "resourceTitle": "Personal Finance for Career Changers", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "FINANCIAL_LITERACY", "author": "NerdWallet", "url": "https://www.nerdwallet.com/article/finance/financial-planning-career-change", "relevanceScore": 13, "keywordMatches": 1, "matchedKeywords": ["finance"], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "f43ca536-5f9f-4f58-a33d-f3d52933376c", "resourceTitle": "Emergency Fund Building Guide", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "FINANCIAL_LITERACY", "author": "Khan Academy", "url": "https://www.khanacademy.org/economics-finance-domain/core-finance/investment-vehicles-tutorial/ira-401ks/v/emergency-fund", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "33a2ca3b-5979-49c9-9ab2-ed22108e5e3e", "resourceTitle": "Budgeting for Freelancers and Contractors", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "FINANCIAL_LITERACY", "author": "FreshBooks", "url": "https://www.freshbooks.com/hub/accounting/budgeting-for-freelancers", "relevanceScore": 13, "keywordMatches": 1, "matchedKeywords": ["management"], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "aeb3ac37-9568-40d9-a602-be5cfd95407c", "resourceTitle": "Salary Negotiation Masterclass", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "FINANCIAL_LITERACY", "author": "LinkedIn Learning", "url": "https://www.linkedin.com/learning/salary-negotiation", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "0811983e-3bce-4dc4-b678-1f2868a64709", "resourceTitle": "Investment Basics for Beginners", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "FINANCIAL_LITERACY", "author": "Investopedia", "url": "https://www.investopedia.com/university/beginner/", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "e81ee9a1-71ac-42a9-a7b6-9774beb5014f", "resourceTitle": "Tax Planning for Career Transitions", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "FINANCIAL_LITERACY", "author": "IRS", "url": "https://www.irs.gov/businesses/small-businesses-self-employed/self-employed-individuals-tax-center", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "337fb4c8-2389-4877-8af6-dc93238d2e68", "resourceTitle": "Personal Finance Course", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "FINANCIAL_LITERACY", "author": "University of Illinois", "url": "https://www.coursera.org/learn/personal-finance", "relevanceScore": 13, "keywordMatches": 1, "matchedKeywords": ["finance"], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "eb7a50e7-0d1c-4bd9-978a-8d880bab3a4f", "resourceTitle": "Advanced Investment Strategies", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "FINANCIAL_LITERACY", "author": "Indian Institute of Management", "url": "https://www.edx.org/course/introduction-to-investments", "relevanceScore": 13, "keywordMatches": 1, "matchedKeywords": ["management"], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "ade671c8-65d8-4f0a-a1be-c74020064f67", "resourceTitle": "Building and Leading Teams", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "ENTREPRENEURSHIP", "author": "LinkedIn Learning", "url": "https://www.linkedin.com/learning/building-and-leading-teams", "relevanceScore": 13, "keywordMatches": 1, "matchedKeywords": ["entrepreneur"], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "016f0659-36e0-4754-a80e-887f60b584e7", "resourceTitle": "Business English for International Careers", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "LANGUAGE_LEARNING", "author": "University of London", "url": "https://www.futurelearn.com/courses/business-english-for-cross-cultural-communication", "relevanceScore": 13, "keywordMatches": 1, "matchedKeywords": ["business"], "reason": "Generic language course not specific to technical roles", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "489d0bd3-e10c-447b-b988-04ef8958b92f", "resourceTitle": "Technical Communication Skills", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "LANGUAGE_LEARNING", "author": "Moscow Institute of Physics and Technology", "url": "https://www.coursera.org/learn/technical-writing", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Generic communication course not specific to technical roles", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "38f7cfb5-3751-4428-96fe-18031b007f47", "resourceTitle": "Cross-Cultural Communication", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "LANGUAGE_LEARNING", "author": "University of California San Diego", "url": "https://www.edx.org/course/intercultural-communication", "relevanceScore": 13, "keywordMatches": 1, "matchedKeywords": ["business"], "reason": "Generic communication course not specific to technical roles", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "9a77cea6-eae2-438f-86fc-1090dd196e30", "resourceTitle": "Professional Presentation Skills", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "LANGUAGE_LEARNING", "author": "Toastmasters International", "url": "https://www.toastmasters.org/education/pathways-learning-experience", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "47077119-d222-46c3-ac3a-4af4f9dbef87", "resourceTitle": "Retirement Planning During Career Changes", "careerPath": "Simple Online Business Owner", "careerPathId": "0b123c9f-9006-4e89-a9c9-5ac75646a2d3", "category": "FINANCIAL_LITERACY", "author": "Fidelity", "url": "https://www.fidelity.com/learning-center/personal-finance/retirement/career-change-retirement", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "aff9d475-38a0-44e1-b4b1-9a5fcd67ebdf", "resourceTitle": "Project Management Foundations", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "PROJECT_MANAGEMENT", "author": "LinkedIn Learning", "url": "https://www.linkedin.com/learning/project-management-foundations-2019", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Generic project management not core to UX/UI design", "recommendation": "REVIEW - Consider if project management is core requirement"}, {"resourceId": "1fb7cd5f-55f0-43fc-96d8-abac809b376b", "resourceTitle": "Introduction to Project Management", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "PROJECT_MANAGEMENT", "author": "Open University", "url": "https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Generic project management not core to web development", "recommendation": "REVIEW - Consider if project management is core requirement"}, {"resourceId": "188413a1-cd56-43b5-bb04-51f6d3582b75", "resourceTitle": "freeCodeCamp Full Stack Development", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "WEB_DEVELOPMENT", "author": "freeCodeCamp", "url": "https://www.freecodecamp.org/", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Web development course not relevant to UX/UI design", "recommendation": "REMOVE - Replace with UX/UI specific resources"}, {"resourceId": "4e23577f-cdbd-459f-b5f4-cdca8bac0cd9", "resourceTitle": "React Official Tutorial", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "WEB_DEVELOPMENT", "author": "Meta", "url": "https://react.dev/learn", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Technical programming tutorial not core to UX/UI design", "recommendation": "REMOVE - Replace with UX/UI specific resources"}, {"resourceId": "7f834836-f4e4-4c2e-a295-6f4532b9512a", "resourceTitle": "The Odin Project", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "WEB_DEVELOPMENT", "author": "The Odin Project", "url": "https://www.theodinproject.com/", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Full-stack web development not relevant to UX/UI design", "recommendation": "REMOVE - Replace with UX/UI specific resources"}, {"resourceId": "16dd0ef7-737a-4e56-8323-e362e856450a", "resourceTitle": "MDN Web Docs", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "WEB_DEVELOPMENT", "author": "Mozilla", "url": "https://developer.mozilla.org/en-US/docs/Learn", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Technical web documentation not relevant to UX/UI design", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "2486e3ff-237c-4467-98b1-076312d844af", "resourceTitle": "Node.js Developer Roadmap", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "WEB_DEVELOPMENT", "author": "Node.js Foundation", "url": "https://nodejs.org/en/learn", "relevanceScore": 8, "keywordMatches": 1, "matchedKeywords": ["ui"], "reason": "Backend development not relevant to UX/UI design", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "016f0659-36e0-4754-a80e-887f60b584e7", "resourceTitle": "Business English for International Careers", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "LANGUAGE_LEARNING", "author": "University of London", "url": "https://www.futurelearn.com/courses/business-english-for-cross-cultural-communication", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Generic language course not specific to technical roles", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "489d0bd3-e10c-447b-b988-04ef8958b92f", "resourceTitle": "Technical Communication Skills", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "LANGUAGE_LEARNING", "author": "Moscow Institute of Physics and Technology", "url": "https://www.coursera.org/learn/technical-writing", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Generic communication course not specific to technical roles", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "38f7cfb5-3751-4428-96fe-18031b007f47", "resourceTitle": "Cross-Cultural Communication", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "LANGUAGE_LEARNING", "author": "University of California San Diego", "url": "https://www.edx.org/course/intercultural-communication", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Generic communication course not specific to technical roles", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "9a77cea6-eae2-438f-86fc-1090dd196e30", "resourceTitle": "Professional Presentation Skills", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "LANGUAGE_LEARNING", "author": "Toastmasters International", "url": "https://www.toastmasters.org/education/pathways-learning-experience", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}, {"resourceId": "2e2088b2-ac2e-455a-89bc-f6faea662884", "resourceTitle": "Test Project Resource", "careerPath": "UX/UI Designer", "careerPathId": "8139e720-d52c-488b-9e06-20e5f6fd0750", "category": "WEB_DEVELOPMENT", "author": null, "url": "https://example.com/test-project-1753655074370", "relevanceScore": 0, "keywordMatches": 0, "matchedKeywords": [], "reason": "Low keyword relevance to career path requirements", "recommendation": "REVIEW - Assess relevance and consider removal"}]