[{"resourceId": "80c77d4d-6d4b-468f-a37b-14fdb81a4ced", "resourceTitle": "Ethical Hacking Essentials (E|HE)", "careerPaths": "Cybersecurity Specialist", "url": "https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/", "author": "EC-Council", "category": "CYBERSECURITY", "description": "Strong foundations in ethical hacking and penetration testing for entry-level careers", "placeholderIndicators": ["Test/placeholder in description"], "priority": "LOW", "recommendation": "REVIEW - Assess if legitimate resource with minor issues"}, {"resourceId": "6e9f2f0e-f0f3-42c6-82dd-fa3579957fee", "resourceTitle": "Mobile App Testing and Deployment", "careerPaths": "Freelance Web Developer", "url": "https://firebase.google.com/docs/app-distribution", "author": "Google Firebase", "category": "MOBILE_DEVELOPMENT", "description": "Testing strategies and deployment processes for mobile applications", "placeholderIndicators": ["Test/placeholder in title", "Test/placeholder in description"], "priority": "HIGH", "recommendation": "IMMEDIATE REMOVAL - Obviously test data"}, {"resourceId": "2e2088b2-ac2e-455a-89bc-f6faea662884", "resourceTitle": "Test Project Resource", "careerPaths": "Freelance Web Developer, UX/UI Designer", "url": "https://example.com/test-project-1753655074370", "author": null, "category": "WEB_DEVELOPMENT", "description": "A test project to verify enum fix", "placeholderIndicators": ["Placeholder URL domain", "Test/placeholder URL pattern", "Test/placeholder in title", "Test/placeholder in description", "Missing or placeholder author", "Suspicious URL pattern with numbers"], "priority": "HIGH", "recommendation": "IMMEDIATE REMOVAL - Placeholder URL cannot be accessed"}]