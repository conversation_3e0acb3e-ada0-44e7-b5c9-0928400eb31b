[{"resourceId": "33a2ca3b-5979-49c9-9ab2-ed22108e5e3e", "resourceTitle": "Budgeting for Freelancers and Contractors", "careerPaths": "Simple Online Business Owner", "url": "https://www.freshbooks.com/hub/accounting/budgeting-for-freelancers", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "FINANCIAL_LITERACY", "author": "FreshBooks"}, {"resourceId": "ade671c8-65d8-4f0a-a1be-c74020064f67", "resourceTitle": "Building and Leading Teams", "careerPaths": "Simple Online Business Owner", "url": "https://www.linkedin.com/learning/building-and-leading-teams", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "ENTREPRENEURSHIP", "author": "LinkedIn Learning"}, {"resourceId": "016f0659-36e0-4754-a80e-887f60b584e7", "resourceTitle": "Business English for International Careers", "careerPaths": "Simple Online Business Owner, UX/UI Designer, Digital Marketing Specialist", "url": "https://www.futurelearn.com/courses/business-english-for-cross-cultural-communication", "errorStatus": "403", "errorType": "FORBIDDEN", "category": "LANGUAGE_LEARNING", "author": "University of London"}, {"resourceId": "38f7cfb5-3751-4428-96fe-18031b007f47", "resourceTitle": "Cross-Cultural Communication", "careerPaths": "Simple Online Business Owner, UX/UI Designer, Digital Marketing Specialist", "url": "https://www.edx.org/course/intercultural-communication", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "LANGUAGE_LEARNING", "author": "University of California San Diego"}, {"resourceId": "0e908156-7adc-47d0-a8ed-f73239d1db03", "resourceTitle": "Figma Academy", "careerPaths": "UX/UI Designer", "url": "https://www.figma.com/academy/", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "UX_UI_DESIGN", "author": "Figma"}, {"resourceId": "ab3634a1-482c-4876-81c2-7c5122e46732", "resourceTitle": "Google Cloud Fundamentals", "careerPaths": "None", "url": "https://cloud.google.com/training/courses/gcp-fundamentals", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "DEVOPS", "author": "Google Cloud"}, {"resourceId": "1fb7cd5f-55f0-43fc-96d8-abac809b376b", "resourceTitle": "Introduction to Project Management", "careerPaths": "Freelance Web Developer, Simple Online Business Owner, UX/UI Designer", "url": "https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "PROJECT_MANAGEMENT", "author": "Open University"}, {"resourceId": "ee92ff90-24af-487a-a8e7-e7083534e14c", "resourceTitle": "<PERSON><PERSON>", "careerPaths": "AI/Machine Learning Engineer, Data Scientist", "url": "https://www.kaggle.com/learn", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "ARTIFICIAL_INTELLIGENCE", "author": "<PERSON><PERSON>"}, {"resourceId": "12fe4f35-7e49-449f-8e6d-b3ec7a862e46", "resourceTitle": "Lean Startup Methodology", "careerPaths": "Simple Online Business Owner", "url": "https://www.edx.org/course/entrepreneurship-micromaster", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "ENTREPRENEURSHIP", "author": "Babson College"}, {"resourceId": "f6b21dad-87e7-4a18-b0b9-68525cee3f4b", "resourceTitle": "Legal Basics for Startups", "careerPaths": "Simple Online Business Owner", "url": "https://www.nolo.com/legal-encyclopedia/small-business-startup", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "ENTREPRENEURSHIP", "author": "<PERSON><PERSON>"}, {"resourceId": "337fb4c8-2389-4877-8af6-dc93238d2e68", "resourceTitle": "Personal Finance Course", "careerPaths": "Simple Online Business Owner", "url": "https://www.coursera.org/learn/personal-finance", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "FINANCIAL_LITERACY", "author": "University of Illinois"}, {"resourceId": "ec4d4215-f2c1-4c2a-94a4-8efe0b2e3a66", "resourceTitle": "Personal Finance for Career Changers", "careerPaths": "Simple Online Business Owner", "url": "https://www.nerdwallet.com/article/finance/financial-planning-career-change", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "FINANCIAL_LITERACY", "author": "NerdWallet"}, {"resourceId": "67c5819c-8341-46e7-85ee-989ad9ce6883", "resourceTitle": "Product Analytics Fundamentals", "careerPaths": "None", "url": "https://amplitude.com/academy", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "PRODUCT_MANAGEMENT", "author": "Amplitude"}, {"resourceId": "83d0ba54-234b-489b-a7fa-993dce8f7723", "resourceTitle": "Product School Free Course", "careerPaths": "None", "url": "https://productschool.com/free-product-management-course/", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "PRODUCT_MANAGEMENT", "author": "Product School"}, {"resourceId": "47077119-d222-46c3-ac3a-4af4f9dbef87", "resourceTitle": "Retirement Planning During Career Changes", "careerPaths": "Simple Online Business Owner", "url": "https://www.fidelity.com/learning-center/personal-finance/retirement/career-change-retirement", "errorStatus": "403", "errorType": "FORBIDDEN", "category": "FINANCIAL_LITERACY", "author": "Fidelity"}, {"resourceId": "aeb3ac37-9568-40d9-a602-be5cfd95407c", "resourceTitle": "Salary Negotiation Masterclass", "careerPaths": "Simple Online Business Owner", "url": "https://www.linkedin.com/learning/salary-negotiation", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "FINANCIAL_LITERACY", "author": "LinkedIn Learning"}, {"resourceId": "91a3c703-663b-4e26-bda6-d406b97e07e6", "resourceTitle": "Startup Funding and Investment", "careerPaths": "Simple Online Business Owner", "url": "https://www.coursera.org/learn/venture-capital", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "ENTREPRENEURSHIP", "author": "University of Pennsylvania"}, {"resourceId": "489d0bd3-e10c-447b-b988-04ef8958b92f", "resourceTitle": "Technical Communication Skills", "careerPaths": "Simple Online Business Owner, UX/UI Designer, Digital Marketing Specialist", "url": "https://www.coursera.org/learn/technical-writing", "errorStatus": "404", "errorType": "NOT_FOUND", "category": "LANGUAGE_LEARNING", "author": "Moscow Institute of Physics and Technology"}, {"resourceId": "7f834836-f4e4-4c2e-a295-6f4532b9512a", "resourceTitle": "The Odin Project", "careerPaths": "Freelance Web Developer, UX/UI Designer", "url": "https://www.theodinproject.com/", "errorStatus": "403", "errorType": "FORBIDDEN", "category": "WEB_DEVELOPMENT", "author": "The Odin Project"}, {"resourceId": "e24d996d-f818-463a-a5bd-2516ea249910", "resourceTitle": "Adobe XD Tutorials", "careerPaths": "UX/UI Designer", "url": "https://helpx.adobe.com/xd/tutorials.html", "errorStatus": "TIMEOUT", "errorType": "TIMEOUT", "category": "UX_UI_DESIGN", "author": "Adobe"}, {"resourceId": "2e2088b2-ac2e-455a-89bc-f6faea662884", "resourceTitle": "Test Project Resource", "careerPaths": "Freelance Web Developer, UX/UI Designer", "url": "https://example.com/test-project-1753655074370", "errorStatus": "TIMEOUT", "errorType": "PLACEHOLDER", "category": "WEB_DEVELOPMENT", "author": null}]