{"summary": {"totalResourcesDeleted": 21, "totalResourcesMarkedInactive": 1, "deletionsByType": {"CYBERSECURITY": 1, "MOBILE_DEVELOPMENT": 1, "WEB_DEVELOPMENT": 3, "FINANCIAL_LITERACY": 5, "ENTREPRENEURSHIP": 4, "LANGUAGE_LEARNING": 3, "UX_UI_DESIGN": 2, "DEVOPS": 1, "PROJECT_MANAGEMENT": 1, "ARTIFICIAL_INTELLIGENCE": 1, "PRODUCT_MANAGEMENT": 2}, "deletionsByReason": {"REVIEW - Assess if legitimate resource with minor issues": 1, "IMMEDIATE REMOVAL - Obviously test data": 1, "IMMEDIATE REMOVAL - Placeholder URL cannot be accessed": 1, "404 Not Found": 16, "403 Forbidden - permanent block": 2, "403 Forbidden - temporary block, marked inactive for review": 1, "Timeout/Connection Error - consistent failure": 1, "Timeout/Connection Error": 1}, "deletionsByCareerPath": {"Cybersecurity Specialist": 1, "Freelance Web Developer": 5, "UX/UI Designer": 9, "Simple Online Business Owner": 13, "Digital Marketing Specialist": 3, "None": 3, "AI/Machine Learning Engineer": 1, "Data Scientist": 1}, "generatedAt": "2025-08-12T10:52:07.842Z"}, "deletions": [{"resourceId": "80c77d4d-6d4b-468f-a37b-14fdb81a4ced", "resourceTitle": "Ethical Hacking Essentials (E|HE)", "careerPaths": "Cybersecurity Specialist", "url": "https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/", "author": "EC-Council", "category": "CYBERSECURITY", "errorStatus": "REVIEW - Assess if legitimate resource with minor issues", "reason": "REVIEW - Assess if legitimate resource with minor issues", "deletedAt": "2025-08-12T10:45:47.440Z", "status": "FAILED", "source": "placeholder-deletion-log", "userProgressCount": 0, "error": "\nInvalid `prisma.learningResource.delete()` invocation in\n/Users/<USER>/faafo/faafo/faafo-career-platform/scripts/cleanup/remove-placeholder-resources.js:26:69\n\n  23 \n  24 try {\n  25   // Remove the resource (Prisma will handle the many-to-many relationship cleanup automatically)\n→ 26   const deletedResource = await prisma.learningResource.delete(\nForeign key constraint violated on the constraint: `UserLearningProgress_resourceId_fkey`"}, {"resourceId": "6e9f2f0e-f0f3-42c6-82dd-fa3579957fee", "resourceTitle": "Mobile App Testing and Deployment", "careerPaths": "Freelance Web Developer", "url": "https://firebase.google.com/docs/app-distribution", "author": "Google Firebase", "category": "MOBILE_DEVELOPMENT", "errorStatus": "IMMEDIATE REMOVAL - Obviously test data", "reason": "IMMEDIATE REMOVAL - Obviously test data", "deletedAt": "2025-08-12T10:45:47.600Z", "status": "SUCCESS", "source": "placeholder-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "2e2088b2-ac2e-455a-89bc-f6faea662884", "resourceTitle": "Test Project Resource", "careerPaths": "Freelance Web Developer, UX/UI Designer", "url": "https://example.com/test-project-1753655074370", "author": null, "category": "WEB_DEVELOPMENT", "errorStatus": "IMMEDIATE REMOVAL - Placeholder URL cannot be accessed", "reason": "IMMEDIATE REMOVAL - Placeholder URL cannot be accessed", "deletedAt": "2025-08-12T10:45:47.747Z", "status": "SUCCESS", "source": "placeholder-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "33a2ca3b-5979-49c9-9ab2-ed22108e5e3e", "resourceTitle": "Budgeting for Freelancers and Contractors", "careerPaths": "Simple Online Business Owner", "url": "https://www.freshbooks.com/hub/accounting/budgeting-for-freelancers", "author": "FreshBooks", "category": "FINANCIAL_LITERACY", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:41.249Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "ade671c8-65d8-4f0a-a1be-c74020064f67", "resourceTitle": "Building and Leading Teams", "careerPaths": "Simple Online Business Owner", "url": "https://www.linkedin.com/learning/building-and-leading-teams", "author": "LinkedIn Learning", "category": "ENTREPRENEURSHIP", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:41.678Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "38f7cfb5-3751-4428-96fe-18031b007f47", "resourceTitle": "Cross-Cultural Communication", "careerPaths": "Simple Online Business Owner, UX/UI Designer, Digital Marketing Specialist", "url": "https://www.edx.org/course/intercultural-communication", "author": "University of California San Diego", "category": "LANGUAGE_LEARNING", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:42.075Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "0e908156-7adc-47d0-a8ed-f73239d1db03", "resourceTitle": "Figma Academy", "careerPaths": "UX/UI Designer", "url": "https://www.figma.com/academy/", "author": "Figma", "category": "UX_UI_DESIGN", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:42.475Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "ab3634a1-482c-4876-81c2-7c5122e46732", "resourceTitle": "Google Cloud Fundamentals", "careerPaths": "None", "url": "https://cloud.google.com/training/courses/gcp-fundamentals", "author": "Google Cloud", "category": "DEVOPS", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:42.869Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "1fb7cd5f-55f0-43fc-96d8-abac809b376b", "resourceTitle": "Introduction to Project Management", "careerPaths": "Freelance Web Developer, Simple Online Business Owner, UX/UI Designer", "url": "https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0", "author": "Open University", "category": "PROJECT_MANAGEMENT", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:43.356Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "ee92ff90-24af-487a-a8e7-e7083534e14c", "resourceTitle": "<PERSON><PERSON>", "careerPaths": "AI/Machine Learning Engineer, Data Scientist", "url": "https://www.kaggle.com/learn", "author": "<PERSON><PERSON>", "category": "ARTIFICIAL_INTELLIGENCE", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:43.765Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "12fe4f35-7e49-449f-8e6d-b3ec7a862e46", "resourceTitle": "Lean Startup Methodology", "careerPaths": "Simple Online Business Owner", "url": "https://www.edx.org/course/entrepreneurship-micromaster", "author": "Babson College", "category": "ENTREPRENEURSHIP", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:44.163Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "f6b21dad-87e7-4a18-b0b9-68525cee3f4b", "resourceTitle": "Legal Basics for Startups", "careerPaths": "Simple Online Business Owner", "url": "https://www.nolo.com/legal-encyclopedia/small-business-startup", "author": "<PERSON><PERSON>", "category": "ENTREPRENEURSHIP", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:44.565Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "337fb4c8-2389-4877-8af6-dc93238d2e68", "resourceTitle": "Personal Finance Course", "careerPaths": "Simple Online Business Owner", "url": "https://www.coursera.org/learn/personal-finance", "author": "University of Illinois", "category": "FINANCIAL_LITERACY", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:44.984Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "ec4d4215-f2c1-4c2a-94a4-8efe0b2e3a66", "resourceTitle": "Personal Finance for Career Changers", "careerPaths": "Simple Online Business Owner", "url": "https://www.nerdwallet.com/article/finance/financial-planning-career-change", "author": "NerdWallet", "category": "FINANCIAL_LITERACY", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:45.389Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "67c5819c-8341-46e7-85ee-989ad9ce6883", "resourceTitle": "Product Analytics Fundamentals", "careerPaths": "None", "url": "https://amplitude.com/academy", "author": "Amplitude", "category": "PRODUCT_MANAGEMENT", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:45.785Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "83d0ba54-234b-489b-a7fa-993dce8f7723", "resourceTitle": "Product School Free Course", "careerPaths": "None", "url": "https://productschool.com/free-product-management-course/", "author": "Product School", "category": "PRODUCT_MANAGEMENT", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:46.186Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "aeb3ac37-9568-40d9-a602-be5cfd95407c", "resourceTitle": "Salary Negotiation Masterclass", "careerPaths": "Simple Online Business Owner", "url": "https://www.linkedin.com/learning/salary-negotiation", "author": "LinkedIn Learning", "category": "FINANCIAL_LITERACY", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:46.588Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "91a3c703-663b-4e26-bda6-d406b97e07e6", "resourceTitle": "Startup Funding and Investment", "careerPaths": "Simple Online Business Owner", "url": "https://www.coursera.org/learn/venture-capital", "author": "University of Pennsylvania", "category": "ENTREPRENEURSHIP", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:46.990Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "489d0bd3-e10c-447b-b988-04ef8958b92f", "resourceTitle": "Technical Communication Skills", "careerPaths": "Simple Online Business Owner, UX/UI Designer, Digital Marketing Specialist", "url": "https://www.coursera.org/learn/technical-writing", "author": "Moscow Institute of Physics and Technology", "category": "LANGUAGE_LEARNING", "errorStatus": "404", "reason": "404 Not Found", "deletedAt": "2025-08-12T10:46:47.405Z", "status": "DELETED", "source": "404-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "016f0659-36e0-4754-a80e-887f60b584e7", "resourceTitle": "Business English for International Careers", "careerPaths": "Simple Online Business Owner, UX/UI Designer, Digital Marketing Specialist", "url": "https://www.futurelearn.com/courses/business-english-for-cross-cultural-communication", "author": "University of London", "category": "LANGUAGE_LEARNING", "errorStatus": "403", "reason": "403 Forbidden - permanent block", "deletedAt": "2025-08-12T10:47:36.718Z", "status": "DELETED", "source": "403-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "47077119-d222-46c3-ac3a-4af4f9dbef87", "resourceTitle": "Retirement Planning During Career Changes", "careerPaths": "Simple Online Business Owner", "url": "https://www.fidelity.com/learning-center/personal-finance/retirement/career-change-retirement", "author": "Fidelity", "category": "FINANCIAL_LITERACY", "errorStatus": "403", "reason": "403 Forbidden - permanent block", "deletedAt": "2025-08-12T10:47:37.133Z", "status": "DELETED", "source": "403-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "7f834836-f4e4-4c2e-a295-6f4532b9512a", "resourceTitle": "The Odin Project", "careerPaths": "Freelance Web Developer, UX/UI Designer", "url": "https://www.theodinproject.com/", "author": "The Odin Project", "category": "WEB_DEVELOPMENT", "errorStatus": "403", "reason": "403 Forbidden - temporary block, marked inactive for review", "deletedAt": "2025-08-12T10:47:37.682Z", "status": "MARKED_INACTIVE_REVIEW", "source": "403-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "e24d996d-f818-463a-a5bd-2516ea249910", "resourceTitle": "Adobe XD Tutorials", "careerPaths": "UX/UI Designer", "url": "https://helpx.adobe.com/xd/tutorials.html", "author": "Adobe", "category": "UX_UI_DESIGN", "errorStatus": "TIMEOUT", "reason": "Timeout/Connection Error - consistent failure", "deletedAt": "2025-08-12T10:48:35.645Z", "status": "DELETED", "source": "timeout-deletion-log", "userProgressCount": 0, "error": null}, {"resourceId": "2e2088b2-ac2e-455a-89bc-f6faea662884", "resourceTitle": "Test Project Resource", "careerPaths": "Freelance Web Developer, UX/UI Designer", "url": "https://example.com/test-project-1753655074370", "author": null, "category": "WEB_DEVELOPMENT", "errorStatus": "TIMEOUT", "reason": "Timeout/Connection Error", "deletedAt": "2025-08-12T10:48:35.784Z", "status": "ALREADY_DELETED", "source": "timeout-deletion-log", "userProgressCount": 0, "error": null}]}