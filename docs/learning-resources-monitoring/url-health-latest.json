{"timestamp": "2025-08-13T09:04:40.361Z", "summary": {"totalResources": 89, "workingUrls": 80, "brokenUrls": 9, "timeoutUrls": 0, "redirectUrls": 0, "successRate": "89.89", "averageResponseTime": 739}, "details": [{"resourceId": "6d3a62af-cd34-4653-bf45-577422f3ce12", "title": "AWS DevOps Learning Path", "url": "https://aws.amazon.com/training/learning-paths/devops/", "author": "Amazon Web Services", "category": "DEVOPS", "careerPaths": ["DevOps Engineer"], "status": 404, "statusText": "", "responseTime": 627, "isWorking": false, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:43.924Z"}, {"resourceId": "ef309181-ddd8-4733-a1a0-99398234b924", "title": "AWS DevOps Engineer Professional", "url": "https://aws.amazon.com/certification/certified-devops-engineer-professional/", "author": "Amazon Web Services", "category": "DEVOPS", "careerPaths": ["DevOps Engineer"], "status": 200, "statusText": "", "responseTime": 629, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:43.925Z"}, {"resourceId": "1fd97127-c82f-444a-83a7-065ff3bf9dc8", "title": "AWS Solutions Architect Associate", "url": "https://aws.amazon.com/training/classroom/architecting-on-aws/", "author": "Amazon Web Services", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "", "responseTime": 628, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:43.926Z"}, {"resourceId": "00c091d8-a060-44cd-a50f-3572b4ac0fa9", "title": "AWS Cloud Practitioner Essentials", "url": "https://aws.amazon.com/training/digital/aws-cloud-practitioner-essentials/", "author": "Amazon Web Services", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "", "responseTime": 906, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:44.201Z"}, {"resourceId": "7524893c-0044-4f79-aa0c-bcca39ed71d8", "title": "AI For Everyone", "url": "https://www.coursera.org/learn/ai-for-everyone", "author": "DeepLearning.AI", "category": "ARTIFICIAL_INTELLIGENCE", "careerPaths": ["AI/Machine Learning Engineer", "Data Scientist"], "status": 200, "statusText": "OK", "responseTime": 2011, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:45.237Z"}, {"resourceId": "78da5e16-e85d-439b-9ca0-97e1944c7448", "title": "AWS Solutions Architect Associate Certification", "url": "https://aws.amazon.com/certification/certified-solutions-architect-associate/", "author": "Amazon Web Services", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "", "responseTime": 268, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:46.507Z"}, {"resourceId": "ba2c1a60-2bda-4b80-bab7-126a8738f94e", "title": "AWS Well-Architected Framework", "url": "https://aws.amazon.com/architecture/well-architected/", "author": "Amazon Web Services", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "", "responseTime": 304, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:46.546Z"}, {"resourceId": "8b80c23a-ff58-4c91-8f70-7a409fa391ca", "title": "Adobe XD User Guide", "url": "https://helpx.adobe.com/xd/user-guide.html", "author": "Adobe", "category": "UX_UI_DESIGN", "careerPaths": ["UX/UI Designer"], "status": 200, "statusText": "OK", "responseTime": 322, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:46.566Z"}, {"resourceId": "eb7a50e7-0d1c-4bd9-978a-8d880bab3a4f", "title": "Advanced Investment Strategies", "url": "https://www.edx.org/course/introduction-to-investments", "author": "Indian Institute of Management", "category": "FINANCIAL_LITERACY", "careerPaths": ["Simple Online Business Owner"], "status": 200, "statusText": "OK", "responseTime": 554, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:46.803Z"}, {"resourceId": "934df586-4fe3-42ad-a244-fc5dbe1ab857", "title": "Advanced iOS Development", "url": "https://developer.apple.com/documentation/technologies", "author": "Apple Developer", "category": "MOBILE_DEVELOPMENT", "careerPaths": [], "status": 200, "statusText": "OK", "responseTime": 968, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:47.220Z"}, {"resourceId": "877e1441-3ee3-4859-81e2-980b55cb0852", "title": "App Store Optimization (ASO)", "url": "https://developer.apple.com/app-store/product-page/", "author": "Apple Developer", "category": "MOBILE_DEVELOPMENT", "careerPaths": [], "status": 200, "statusText": "OK", "responseTime": 383, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:48.614Z"}, {"resourceId": "a65894ae-ac79-4310-b4dd-527d3a5c1ab2", "title": "Agile Product Management with Scrum", "url": "https://www.scrum.org/resources/what-is-a-product-owner", "author": "Scrum.org", "category": "PRODUCT_MANAGEMENT", "careerPaths": ["Product Manager"], "status": 200, "statusText": "OK", "responseTime": 659, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:48.885Z"}, {"resourceId": "bf9abeb8-7cce-4cf4-9f7a-36354d7f8666", "title": "Agile Development Specialization", "url": "https://www.coursera.org/specializations/agile-development", "author": "University of Virginia", "category": "PRODUCT_MANAGEMENT", "careerPaths": ["Product Manager"], "status": 200, "statusText": "OK", "responseTime": 802, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:49.023Z"}, {"resourceId": "0d4af495-f555-483f-8005-12bc8b3851c0", "title": "Android Architecture Components", "url": "https://developer.android.com/topic/architecture", "author": "Google Android", "category": "MOBILE_DEVELOPMENT", "careerPaths": [], "status": 200, "statusText": "OK", "responseTime": 934, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:49.162Z"}, {"resourceId": "61ba2e43-6c34-4431-a282-c1f76711be37", "title": "Android Development Fundamentals", "url": "https://developer.android.com/courses/android-basics-kotlin/course", "author": "Google Android", "category": "MOBILE_DEVELOPMENT", "careerPaths": [], "status": 200, "statusText": "OK", "responseTime": 1245, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:49.475Z"}, {"resourceId": "a48f7a7e-6c9f-4267-9520-87ee0ebbe95b", "title": "CISA Learning", "url": "https://www.cisa.gov/cybersecurity-training-exercises", "author": "CISA", "category": "CYBERSECURITY", "careerPaths": ["Cybersecurity Specialist"], "status": 200, "statusText": "OK", "responseTime": 229, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:50.708Z"}, {"resourceId": "190093e4-f1fc-4847-b821-9e70f05ccd12", "title": "Blockchain Fundamentals", "url": "https://www.edx.org/professional-certificate/uc-berkeleyx-blockchain-fundamentals", "author": "UC Berkeley", "category": "BLOCKCHAIN", "careerPaths": [], "status": 200, "statusText": "OK", "responseTime": 343, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:50.820Z"}, {"resourceId": "c7a94dc0-bdde-46db-9c96-e64cca0cb023", "title": "Certified in Cybersecurity (CC)", "url": "https://www.isc2.org/landing/1mcc", "author": "ISC2", "category": "CYBERSECURITY", "careerPaths": ["Cybersecurity Specialist"], "status": 200, "statusText": "OK", "responseTime": 1113, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:51.593Z"}, {"resourceId": "08f4f905-48a7-4ccd-8446-5d9a8cb97959", "title": "Blockchain Basics", "url": "https://www.coursera.org/learn/blockchain-basics", "author": "University at Buffalo", "category": "BLOCKCHAIN", "careerPaths": [], "status": 200, "statusText": "OK", "responseTime": 1442, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:51.918Z"}, {"resourceId": "a4746f2e-09f6-4f07-b3a7-dffa2f103c7c", "title": "Business Model Canvas", "url": "https://www.strategyzer.com/canvas/business-model-canvas", "author": "Strategyzer", "category": "ENTREPRENEURSHIP", "careerPaths": ["Simple Online Business Owner"], "status": 200, "statusText": "OK", "responseTime": 1621, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:52.099Z"}, {"resourceId": "f0052540-b541-45de-a415-d01e19d81bc6", "title": "Cloud Cost Optimization Strategies", "url": "https://aws.amazon.com/aws-cost-management/", "author": "Amazon Web Services", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "", "responseTime": 294, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:53.394Z"}, {"resourceId": "196f11ca-b0b1-4964-90a3-f3056bd56198", "title": "Cloud Security Best Practices", "url": "https://cloud.google.com/security/best-practices", "author": "Google Cloud", "category": "CYBERSECURITY", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "OK", "responseTime": 480, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:53.582Z"}, {"resourceId": "27418518-6ecd-4815-bb31-fcf69c27780c", "title": "Coursera Personal Finance Courses", "url": "https://www.coursera.org/browse/personal-development/personal-finance", "author": "Coursera", "category": "FINANCIAL_LITERACY", "careerPaths": ["Simple Online Business Owner"], "status": 404, "statusText": "Not Found", "responseTime": 484, "isWorking": false, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:53.588Z"}, {"resourceId": "e3cb3d88-feec-4e46-b0f7-72bf3e8f26be", "title": "Cloud Migration Strategies", "url": "https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/migrate/", "author": "Microsoft", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "OK", "responseTime": 559, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:53.662Z"}, {"resourceId": "b5576b70-bf0e-42c3-8047-cdfdc32e968a", "title": "Cloud Security Essentials (C|SE)", "url": "https://www.eccouncil.org/train-certify/cloud-security-essentials-cse/", "author": "EC-Council", "category": "CYBERSECURITY", "careerPaths": ["Cybersecurity Specialist"], "status": 200, "statusText": "OK", "responseTime": 740, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:53.843Z"}, {"resourceId": "58daae84-925a-4454-9c6b-bfa788b9a3f4", "title": "Data Science: Machine Learning", "url": "https://pll.harvard.edu/course/data-science-machine-learning", "author": "Harvard University", "category": "DATA_SCIENCE", "careerPaths": ["Data Scientist"], "status": 200, "statusText": "OK", "responseTime": 303, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:55.150Z"}, {"resourceId": "f15d34d1-13a8-4369-8f77-7534494cc9cd", "title": "Design Systems with Figma", "url": "https://www.figma.com/resources/learn-design-systems/", "author": "Figma", "category": "UX_UI_DESIGN", "careerPaths": ["UX/UI Designer"], "status": 200, "statusText": "OK", "responseTime": 718, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:55.569Z"}, {"resourceId": "49bf30f7-f299-49c0-89e8-4ff421b6cd5b", "title": "Cybersecurity Fundamentals", "url": "https://skillsbuild.org/students/course-catalog/cybersecurity", "author": "IBM SkillsBuild", "category": "CYBERSECURITY", "careerPaths": ["Cybersecurity Specialist"], "status": 200, "statusText": "OK", "responseTime": 750, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:55.596Z"}, {"resourceId": "baf21149-f8dc-4bf3-8ad1-f52d42442408", "title": "Customer Development and Validation", "url": "https://steveblank.com/category/customer-development/", "author": "<PERSON>", "category": "ENTREPRENEURSHIP", "careerPaths": ["Simple Online Business Owner"], "status": 200, "statusText": "OK", "responseTime": 835, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:55.679Z"}, {"resourceId": "600bfe87-abbe-4c46-ac49-424c84b4a926", "title": "Deep Learning Specialization", "url": "https://www.coursera.org/specializations/deep-learning", "author": "DeepLearning.AI", "category": "ARTIFICIAL_INTELLIGENCE", "careerPaths": ["AI/Machine Learning Engineer", "Data Scientist"], "status": 200, "statusText": "OK", "responseTime": 922, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:55.770Z"}, {"resourceId": "be031740-73b6-4ee9-bb7b-9f6684b3b14d", "title": "Docker Official Documentation", "url": "https://docs.docker.com/", "author": "Docker Inc.", "category": "DEVOPS", "careerPaths": ["DevOps Engineer"], "status": 200, "statusText": "OK", "responseTime": 217, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:56.992Z"}, {"resourceId": "1f5defab-b504-4db5-9377-5135534076f1", "title": "Docker Getting Started", "url": "https://docs.docker.com/get-started/", "author": "<PERSON>er", "category": "DEVOPS", "careerPaths": ["DevOps Engineer"], "status": 200, "statusText": "OK", "responseTime": 223, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:56.997Z"}, {"resourceId": "f43ca536-5f9f-4f58-a33d-f3d52933376c", "title": "Emergency Fund Building Guide", "url": "https://www.khanacademy.org/economics-finance-domain/core-finance/investment-vehicles-tutorial/ira-401ks/v/emergency-fund", "author": "Khan Academy", "category": "FINANCIAL_LITERACY", "careerPaths": ["Simple Online Business Owner"], "status": 200, "statusText": "OK", "responseTime": 442, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:57.219Z"}, {"resourceId": "77eb08b5-7843-43a8-b312-d36ef63adca5", "title": "Digital Forensics Essentials (D|FE)", "url": "https://www.eccouncil.org/train-certify/digital-forensics-essentials-dfe/", "author": "EC-Council", "category": "CYBERSECURITY", "careerPaths": ["Cybersecurity Specialist"], "status": 200, "statusText": "OK", "responseTime": 562, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:57.334Z"}, {"resourceId": "c7c2e379-f95f-4d52-b3b4-3a3e00a6efa7", "title": "Elements of AI", "url": "https://www.elementsofai.com/", "author": "University of Helsinki", "category": "ARTIFICIAL_INTELLIGENCE", "careerPaths": ["AI/Machine Learning Engineer", "Data Scientist"], "status": 200, "statusText": "OK", "responseTime": 659, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:57.435Z"}, {"resourceId": "6f66e93d-3001-438f-aac2-ddadb<PERSON>cfc7", "title": "Facebook Blueprint", "url": "https://www.facebook.com/business/learn", "author": "Meta", "category": "DIGITAL_MARKETING", "careerPaths": ["Digital Marketing Specialist"], "status": 400, "statusText": "Bad Request", "responseTime": 335, "isWorking": false, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:58.772Z"}, {"resourceId": "8b286ed6-2616-47d7-ac3d-b4a79d2646ef", "title": "Flutter Development Bootcamp", "url": "https://flutter.dev/docs/get-started/codelab", "author": "Google Flutter", "category": "MOBILE_DEVELOPMENT", "careerPaths": [], "status": 200, "statusText": "OK", "responseTime": 522, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:58.960Z"}, {"resourceId": "80c77d4d-6d4b-468f-a37b-14fdb81a4ced", "title": "Ethical Hacking Essentials (E|HE)", "url": "https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/", "author": "EC-Council", "category": "CYBERSECURITY", "careerPaths": ["Cybersecurity Specialist"], "status": 200, "statusText": "OK", "responseTime": 603, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:59.039Z"}, {"resourceId": "651a3f11-c4fe-45b9-9204-c136da7529d1", "title": "Financial Planning for Entrepreneurs", "url": "https://www.sba.gov/business-guide/plan-your-business/calculate-your-startup-costs", "author": "Small Business Administration", "category": "FINANCIAL_LITERACY", "careerPaths": ["Simple Online Business Owner"], "status": 200, "statusText": "OK", "responseTime": 693, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:04:59.130Z"}, {"resourceId": "7be6dacc-49d0-4b17-b1a4-b6c57a2c2edc", "title": "Fundamentals of Digital Marketing", "url": "https://learndigital.withgoogle.com/digitalgarage/course/digital-marketing", "author": "Google Digital Garage", "category": "DIGITAL_MARKETING", "careerPaths": ["Digital Marketing Specialist"], "status": 200, "statusText": "OK", "responseTime": 2336, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:00.774Z"}, {"resourceId": "4de82bbf-b91b-43c5-bb7b-a1cb259a674b", "title": "GitLab CI/CD Documentation", "url": "https://docs.gitlab.com/ee/ci/", "author": "GitLab", "category": "DEVOPS", "careerPaths": ["DevOps Engineer"], "status": 200, "statusText": "OK", "responseTime": 474, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:02.249Z"}, {"resourceId": "21af6095-0536-48ea-b885-20e947c741bb", "title": "Google UX Design Certificate", "url": "https://grow.google/certificates/ux-design/", "author": "Google", "category": "UX_UI_DESIGN", "careerPaths": ["UX/UI Designer"], "status": 200, "statusText": "OK", "responseTime": 627, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:02.405Z"}, {"resourceId": "455fbd68-0b8b-49bc-9ab9-20acad1085f0", "title": "Google Product Management Certificate", "url": "https://grow.google/certificates/product-management/", "author": "Google", "category": "PRODUCT_MANAGEMENT", "careerPaths": ["Product Manager"], "status": 200, "statusText": "OK", "responseTime": 634, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:02.411Z"}, {"resourceId": "d9042ed3-5474-4f51-9d2b-f0d75be90753", "title": "Google Analytics Academy", "url": "https://analytics.google.com/analytics/academy/", "author": "Google", "category": "DIGITAL_MARKETING", "careerPaths": ["Digital Marketing Specialist"], "status": 404, "statusText": "Not Found", "responseTime": 710, "isWorking": false, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:02.486Z"}, {"resourceId": "854501ad-dcb9-46ae-8175-2a830718421b", "title": "Google Cloud Architecture Framework", "url": "https://cloud.google.com/architecture/framework", "author": "Google Cloud", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "OK", "responseTime": 830, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:02.606Z"}, {"resourceId": "bec97e93-eb4e-4593-93ef-dec5aa819a15", "title": "Jenkins User Documentation", "url": "https://www.jenkins.io/doc/", "author": "Jenkins Community", "category": "DEVOPS", "careerPaths": ["DevOps Engineer"], "status": 200, "statusText": "OK", "responseTime": 230, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:03.839Z"}, {"resourceId": "ad3efa98-67e7-4097-8ab7-fffa8b6aaff6", "title": "Kaggle Learn Courses", "url": "https://www.kaggle.com/learn", "author": "<PERSON><PERSON>", "category": "DATA_SCIENCE", "careerPaths": ["AI/Machine Learning Engineer", "Data Scientist"], "status": 404, "statusText": "Not Found", "responseTime": 419, "isWorking": false, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:04.028Z"}, {"resourceId": "63057b31-754f-489a-af50-d4aed9d52d55", "title": "Interaction Design Foundation", "url": "https://www.interaction-design.org/", "author": "IxDF", "category": "UX_UI_DESIGN", "careerPaths": ["UX/UI Designer"], "status": 200, "statusText": "OK", "responseTime": 567, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:04.174Z"}, {"resourceId": "88dd2e0c-6ca9-459f-9270-88867178ad99", "title": "Introduction to Data Science", "url": "https://skillsbuild.org/students/course-catalog/data-science", "author": "IBM SkillsBuild", "category": "DATA_SCIENCE", "careerPaths": ["Data Scientist"], "status": 200, "statusText": "OK", "responseTime": 689, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:04.297Z"}, {"resourceId": "0811983e-3bce-4dc4-b678-1f2868a64709", "title": "Investment Basics for Beginners", "url": "https://www.investopedia.com/university/beginner/", "author": "Investopedia", "category": "FINANCIAL_LITERACY", "careerPaths": ["Simple Online Business Owner"], "status": 404, "statusText": "Not Found", "responseTime": 868, "isWorking": false, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:04.477Z"}, {"resourceId": "fc388d80-68e9-4dd6-82be-2ab4495dac53", "title": "Kubernetes Official Tutorials", "url": "https://kubernetes.io/docs/tutorials/", "author": "Kubernetes Community", "category": "DEVOPS", "careerPaths": ["DevOps Engineer"], "status": 200, "statusText": "OK", "responseTime": 234, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:05.713Z"}, {"resourceId": "747023f3-375f-4b6a-ad49-fa801007cc91", "title": "Kubernetes Basics", "url": "https://kubernetes.io/docs/tutorials/kubernetes-basics/", "author": "Kubernetes", "category": "DEVOPS", "careerPaths": ["DevOps Engineer"], "status": 200, "statusText": "OK", "responseTime": 236, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:05.714Z"}, {"resourceId": "16dd0ef7-737a-4e56-8323-e362e856450a", "title": "MDN Web Docs", "url": "https://developer.mozilla.org/en-US/docs/Learn", "author": "Mozilla", "category": "WEB_DEVELOPMENT", "careerPaths": ["Freelance Web Developer"], "status": 200, "statusText": "OK", "responseTime": 530, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:06.010Z"}, {"resourceId": "978ac364-d31a-4235-8928-70703e551447", "title": "Linux System Administration", "url": "https://www.edx.org/course/introduction-to-linux", "author": "Linux Foundation", "category": "DEVOPS", "careerPaths": ["DevOps Engineer"], "status": 200, "statusText": "OK", "responseTime": 718, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:06.197Z"}, {"resourceId": "3547f09f-fc25-4793-804c-9463803e47cf", "title": "LinkedIn Learning Tech Skills", "url": "https://www.linkedin.com/learning/", "author": "LinkedIn Learning", "category": "PROJECT_MANAGEMENT", "careerPaths": ["Product Manager"], "status": 200, "statusText": "OK", "responseTime": 931, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:06.410Z"}, {"resourceId": "5c65d986-a2fc-4610-983c-a27fcc5d27b2", "title": "Microsoft Azure Architecture Center", "url": "https://docs.microsoft.com/en-us/azure/architecture/", "author": "Microsoft", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "OK", "responseTime": 516, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:07.929Z"}, {"resourceId": "cbe2960b-09d3-48f7-a3a0-84f5f29eea00", "title": "Microsoft Azure Fundamentals", "url": "https://docs.microsoft.com/en-us/learn/paths/azure-fundamentals/", "author": "Microsoft", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "OK", "responseTime": 520, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:07.934Z"}, {"resourceId": "5e172d5f-898d-4f57-b325-ac670b1ca08e", "title": "Material Design Guidelines", "url": "https://material.io/design", "author": "Google", "category": "UX_UI_DESIGN", "careerPaths": ["UX/UI Designer"], "status": 200, "statusText": "OK", "responseTime": 694, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:08.107Z"}, {"resourceId": "4cf9fecb-79c3-4a33-9903-9505bdc4bee2", "title": "Machine Learning Crash Course", "url": "https://developers.google.com/machine-learning/crash-course", "author": "Google", "category": "ARTIFICIAL_INTELLIGENCE", "careerPaths": ["AI/Machine Learning Engineer", "Data Scientist"], "status": 200, "statusText": "OK", "responseTime": 709, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:08.122Z"}, {"resourceId": "6c01e649-0b89-4e33-b964-5238cc156338", "title": "MLOps Specialization", "url": "https://www.coursera.org/specializations/machine-learning-engineering-for-production-mlops", "author": "DeepLearning.AI", "category": "ARTIFICIAL_INTELLIGENCE", "careerPaths": ["AI/Machine Learning Engineer", "Data Scientist"], "status": 200, "statusText": "OK", "responseTime": 1416, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:08.827Z"}, {"resourceId": "81e24017-831a-44a7-9f07-49f3cec463c8", "title": "Multi-Cloud Architecture Patterns", "url": "https://www.redhat.com/en/topics/cloud-computing/what-is-multicloud", "author": "Red Hat", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "OK", "responseTime": 210, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:10.038Z"}, {"resourceId": "beb9b13d-d40c-4d2a-b190-99a99a298448", "title": "Network Defense Essentials (N|DE)", "url": "https://www.eccouncil.org/train-certify/network-defense-essentials-nde/", "author": "EC-Council", "category": "CYBERSECURITY", "careerPaths": ["Cybersecurity Specialist"], "status": 200, "statusText": "OK", "responseTime": 374, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:10.203Z"}, {"resourceId": "2486e3ff-237c-4467-98b1-076312d844af", "title": "Node.js Developer Roadmap", "url": "https://nodejs.org/en/learn", "author": "Node.js Foundation", "category": "WEB_DEVELOPMENT", "careerPaths": ["Freelance Web Developer"], "status": 200, "statusText": "OK", "responseTime": 535, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:10.364Z"}, {"resourceId": "1", "title": "Overcoming Six Fears of Midlife Career Change", "url": "https://www.linkedin.com/pulse/overcoming-six-fears-midlife-career-change-guide-joanne-savoie-malone-xwpme", "author": "<PERSON>", "category": "ENTREPRENEURSHIP", "careerPaths": ["Simple Online Business Owner"], "status": 200, "statusText": "OK", "responseTime": 778, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:10.607Z"}, {"resourceId": "62ed3496-a3d4-4271-aac4-2fed077e1a4a", "title": "Mobile UI/UX Design Principles", "url": "https://material.io/design/introduction", "author": "Google Material Design", "category": "MOBILE_DEVELOPMENT", "careerPaths": ["Freelance Web Developer"], "status": 200, "statusText": "OK", "responseTime": 860, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:10.688Z"}, {"resourceId": "1f3b7671-11ec-4416-bf0a-fcd8973f3821", "title": "Product Design and UX Principles", "url": "https://www.interaction-design.org/literature/topics/product-design", "author": "Interaction Design Foundation", "category": "UX_UI_DESIGN", "careerPaths": ["Product Manager"], "status": 200, "statusText": "OK", "responseTime": 516, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:12.206Z"}, {"resourceId": "5f3ce1aa-5595-46ba-9729-5d356283016c", "title": "Product Strategy and Roadmapping", "url": "https://www.productplan.com/learn/product-strategy/", "author": "ProductPlan", "category": "PRODUCT_MANAGEMENT", "careerPaths": ["Product Manager"], "status": 200, "statusText": "OK", "responseTime": 763, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:12.454Z"}, {"resourceId": "c8d47ced-f984-4fcd-8e52-2f35c72d7be8", "title": "Product Marketing and Go-to-Market", "url": "https://blog.hubspot.com/marketing/go-to-market-strategy", "author": "HubSpot", "category": "DIGITAL_MARKETING", "careerPaths": ["Product Manager"], "status": 404, "statusText": "Not Found", "responseTime": 861, "isWorking": false, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:12.552Z"}, {"resourceId": "615c14ae-ecf2-463f-80b4-724d42e9038c", "title": "Product Analytics and Metrics", "url": "https://amplitude.com/blog/product-analytics", "author": "Amplitude", "category": "DATA_SCIENCE", "careerPaths": ["Product Manager"], "status": 200, "statusText": "OK", "responseTime": 1542, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:13.231Z"}, {"resourceId": "b4a0b127-4dbd-4eae-9e71-9ad14df524ed", "title": "Product Management Fundamentals", "url": "https://www.coursera.org/learn/uva-darden-product-management", "author": "University of Virginia", "category": "PRODUCT_MANAGEMENT", "careerPaths": ["Product Manager"], "status": 200, "statusText": "OK", "responseTime": 2053, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:13.743Z"}, {"resourceId": "839900d8-38dd-4cd8-9400-806d0d31efd0", "title": "Product-Led Growth Strategies", "url": "https://www.productled.org/foundations", "author": "Product-Led Institute", "category": "PRODUCT_MANAGEMENT", "careerPaths": ["Product Manager"], "status": 200, "statusText": "OK", "responseTime": 308, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:15.052Z"}, {"resourceId": "c66f6bd3-9ff4-46af-b710-537f9e4bce09", "title": "Prometheus Monitoring Guide", "url": "https://prometheus.io/docs/introduction/overview/", "author": "Prometheus Community", "category": "DEVOPS", "careerPaths": ["DevOps Engineer"], "status": 200, "statusText": "OK", "responseTime": 326, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:15.074Z"}, {"resourceId": "aff9d475-38a0-44e1-b4b1-9a5fcd67ebdf", "title": "Project Management Foundations", "url": "https://www.linkedin.com/learning/project-management-foundations-2019", "author": "LinkedIn Learning", "category": "PROJECT_MANAGEMENT", "careerPaths": ["Simple Online Business Owner"], "status": 200, "statusText": "OK", "responseTime": 1294, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:16.040Z"}, {"resourceId": "9a77cea6-eae2-438f-86fc-1090dd196e30", "title": "Professional Presentation Skills", "url": "https://www.toastmasters.org/education/pathways-learning-experience", "author": "Toastmasters International", "category": "LANGUAGE_LEARNING", "careerPaths": ["Simple Online Business Owner", "Digital Marketing Specialist"], "status": 500, "statusText": "Layout not found: {00000000-0000-0000-0000-000000000000}", "responseTime": 1320, "isWorking": false, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:16.066Z"}, {"resourceId": "a17de462-a5a0-48b3-94c1-570fe3d796bf", "title": "Python for Data Science and AI", "url": "https://www.coursera.org/learn/python-for-applied-data-science-ai", "author": "IBM", "category": "ARTIFICIAL_INTELLIGENCE", "careerPaths": ["AI/Machine Learning Engineer", "Data Scientist"], "status": 200, "statusText": "OK", "responseTime": 2169, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:16.918Z"}, {"resourceId": "4e23577f-cdbd-459f-b5f4-cdca8bac0cd9", "title": "React Official Tutorial", "url": "https://react.dev/learn", "author": "Meta", "category": "WEB_DEVELOPMENT", "careerPaths": ["Freelance Web Developer"], "status": 200, "statusText": "OK", "responseTime": 249, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:18.171Z"}, {"resourceId": "69f3fb2f-3daa-4cea-9070-0c8f3f313782", "title": "Serverless Architecture Patterns", "url": "https://aws.amazon.com/serverless/", "author": "Amazon Web Services", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "", "responseTime": 331, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:18.256Z"}, {"resourceId": "18eec276-69de-41b1-9135-d92e6d0ecff2", "title": "React Native Complete Guide", "url": "https://reactnative.dev/docs/tutorial", "author": "Meta", "category": "MOBILE_DEVELOPMENT", "careerPaths": ["Freelance Web Developer"], "status": 200, "statusText": "OK", "responseTime": 570, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:18.491Z"}, {"resourceId": "99239824-725b-492a-aa09-539ac94ce4a4", "title": "Social Media Marketing", "url": "https://academy.hubspot.com/courses/social-media", "author": "HubSpot Academy", "category": "DIGITAL_MARKETING", "careerPaths": ["Digital Marketing Specialist"], "status": 200, "statusText": "OK", "responseTime": 1157, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:19.083Z"}, {"resourceId": "d33db5e0-b004-44a3-8a50-ec9a349b041a", "title": "Sales and Customer Acquisition", "url": "https://blog.hubspot.com/sales/sales-training", "author": "HubSpot", "category": "ENTREPRENEURSHIP", "careerPaths": ["Simple Online Business Owner"], "status": 200, "statusText": "OK", "responseTime": 1447, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:19.370Z"}, {"resourceId": "bc7adfe1-6269-4a04-9ca2-1c9cb7fa1a05", "title": "Terraform Documentation", "url": "https://developer.hashicorp.com/terraform/docs", "author": "HashiCorp", "category": "DEVOPS", "careerPaths": ["DevOps Engineer"], "status": 200, "statusText": "OK", "responseTime": 284, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:20.657Z"}, {"resourceId": "e81ee9a1-71ac-42a9-a7b6-9774beb5014f", "title": "Tax Planning for Career Transitions", "url": "https://www.irs.gov/businesses/small-businesses-self-employed/self-employed-individuals-tax-center", "author": "IRS", "category": "FINANCIAL_LITERACY", "careerPaths": ["Simple Online Business Owner"], "status": 200, "statusText": "OK", "responseTime": 519, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:20.890Z"}, {"resourceId": "254afb4d-3156-4434-8743-57692ad58ca2", "title": "Terraform Getting Started", "url": "https://learn.hashicorp.com/terraform", "author": "HashiCorp", "category": "DEVOPS", "careerPaths": ["Cloud Solutions Architect"], "status": 200, "statusText": "OK", "responseTime": 541, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:20.914Z"}, {"resourceId": "3f3ffc61-3f6c-4e87-a40f-1937850254a7", "title": "Technical Product Management", "url": "https://www.coursera.org/learn/technical-product-management", "author": "University of Alberta", "category": "PRODUCT_MANAGEMENT", "careerPaths": ["Product Manager"], "status": 404, "statusText": "Not Found", "responseTime": 1349, "isWorking": false, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:21.721Z"}, {"resourceId": "8bdc5d40-9620-4318-b7bf-53d9cd2166f7", "title": "User Research and Customer Discovery", "url": "https://www.nngroup.com/articles/which-ux-research-methods/", "author": "Nielsen Norman Group", "category": "UX_UI_DESIGN", "careerPaths": ["Product Manager"], "status": 200, "statusText": "OK", "responseTime": 1538, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:21.911Z"}, {"resourceId": "68316eb6-b6be-47c8-b6af-43ba3ca89f83", "title": "edX Computer Science Courses", "url": "https://www.edx.org/learn/computer-science", "author": "edX", "category": "WEB_DEVELOPMENT", "careerPaths": ["Freelance Web Developer"], "status": 200, "statusText": "OK", "responseTime": 300, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:23.215Z"}, {"resourceId": "188413a1-cd56-43b5-bb04-51f6d3582b75", "title": "freeCodeCamp Full Stack Development", "url": "https://www.freecodecamp.org/", "author": "freeCodeCamp", "category": "WEB_DEVELOPMENT", "careerPaths": ["Freelance Web Developer"], "status": 200, "statusText": "OK", "responseTime": 658, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:23.575Z"}, {"resourceId": "2a31dc8c-63c2-473c-a0ba-79d0980be296", "title": "iOS App Development for Beginners", "url": "https://developer.apple.com/tutorials/swiftui", "author": "Apple Developer", "category": "MOBILE_DEVELOPMENT", "careerPaths": [], "status": 200, "statusText": "OK", "responseTime": 658, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:23.577Z"}, {"resourceId": "7a0807f2-a9b4-418e-91cb-47743d261f7a", "title": "Y Combinator Startup School", "url": "https://www.startupschool.org/", "author": "Y Combinator", "category": "ENTREPRENEURSHIP", "careerPaths": ["Simple Online Business Owner"], "status": 200, "statusText": "OK", "responseTime": 1070, "isWorking": true, "isRedirect": false, "error": null, "testedAt": "2025-08-13T09:05:23.983Z"}], "alerts": [{"type": "HIGH_BROKEN_URL_COUNT", "severity": "HIGH", "message": "9 broken URLs detected (threshold: 5)", "action": "Immediate review and fixing required"}, {"type": "LOW_SUCCESS_RATE", "severity": "MEDIUM", "message": "URL success rate is 89.89% (threshold: 90%)", "action": "Review and fix failing URLs"}], "recommendations": [{"type": "FIX_BROKEN_URLS", "priority": "HIGH", "description": "Fix 9 broken URLs", "resources": [{"title": "AWS DevOps Learning Path", "url": "https://aws.amazon.com/training/learning-paths/devops/", "error": "HTTP 404"}, {"title": "Coursera Personal Finance Courses", "url": "https://www.coursera.org/browse/personal-development/personal-finance", "error": "HTTP 404"}, {"title": "Facebook Blueprint", "url": "https://www.facebook.com/business/learn", "error": "HTTP 400"}, {"title": "Google Analytics Academy", "url": "https://analytics.google.com/analytics/academy/", "error": "HTTP 404"}, {"title": "Kaggle Learn Courses", "url": "https://www.kaggle.com/learn", "error": "HTTP 404"}, {"title": "Investment Basics for Beginners", "url": "https://www.investopedia.com/university/beginner/", "error": "HTTP 404"}, {"title": "Product Marketing and Go-to-Market", "url": "https://blog.hubspot.com/marketing/go-to-market-strategy", "error": "HTTP 404"}, {"title": "Professional Presentation Skills", "url": "https://www.toastmasters.org/education/pathways-learning-experience", "error": "HTTP 500"}, {"title": "Technical Product Management", "url": "https://www.coursera.org/learn/technical-product-management", "error": "HTTP 404"}]}]}