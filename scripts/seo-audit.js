#!/usr/bin/env node

/**
 * SEO Audit Script for FAAFO Career Platform
 * Checks various SEO factors and provides recommendations
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

class SEOAuditor {
  constructor() {
    this.results = {
      technical: [],
      content: [],
      performance: [],
      recommendations: []
    };
  }

  async runAudit() {
    console.log('🔍 Starting SEO Audit for FAAFO Career Platform...\n');

    await this.checkTechnicalSEO();
    await this.checkContentSEO();
    await this.checkPerformance();
    this.generateRecommendations();
    this.generateReport();
  }

  async checkTechnicalSEO() {
    console.log('📋 Checking Technical SEO...');

    // Check robots.txt
    const robotsPath = path.join(__dirname, '../faafo-career-platform/src/app/robots.ts');
    if (fs.existsSync(robotsPath)) {
      this.results.technical.push('✅ robots.txt file exists');
    } else {
      this.results.technical.push('❌ robots.txt file missing');
    }

    // Check sitemap
    const sitemapPath = path.join(__dirname, '../faafo-career-platform/src/app/sitemap.ts');
    if (fs.existsSync(sitemapPath)) {
      this.results.technical.push('✅ sitemap.xml file exists');
    } else {
      this.results.technical.push('❌ sitemap.xml file missing');
    }

    // Check meta tags in layout
    const layoutPath = path.join(__dirname, '../faafo-career-platform/src/app/layout.tsx');
    if (fs.existsSync(layoutPath)) {
      const layoutContent = fs.readFileSync(layoutPath, 'utf8');
      
      if (layoutContent.includes('title:')) {
        this.results.technical.push('✅ Page title configured');
      } else {
        this.results.technical.push('❌ Page title missing');
      }

      if (layoutContent.includes('description:')) {
        this.results.technical.push('✅ Meta description configured');
      } else {
        this.results.technical.push('❌ Meta description missing');
      }

      if (layoutContent.includes('openGraph:')) {
        this.results.technical.push('✅ Open Graph tags configured');
      } else {
        this.results.technical.push('❌ Open Graph tags missing');
      }

      if (layoutContent.includes('twitter:')) {
        this.results.technical.push('✅ Twitter Card tags configured');
      } else {
        this.results.technical.push('❌ Twitter Card tags missing');
      }
    }

    // Check for structured data
    const seoConfigPath = path.join(__dirname, '../faafo-career-platform/src/lib/seo/seo-config.ts');
    if (fs.existsSync(seoConfigPath)) {
      this.results.technical.push('✅ SEO configuration system exists');
    } else {
      this.results.technical.push('❌ SEO configuration system missing');
    }
  }

  async checkContentSEO() {
    console.log('📝 Checking Content SEO...');

    // Check for blog/content section
    const blogPath = path.join(__dirname, '../faafo-career-platform/src/app/blog');
    if (fs.existsSync(blogPath)) {
      this.results.content.push('✅ Blog section exists');
    } else {
      this.results.content.push('❌ Blog section missing - important for content marketing');
    }

    // Check main pages for SEO optimization
    const pagesToCheck = [
      'page.tsx', // Homepage
      'assessment/page.tsx',
      'career-paths/page.tsx',
      'resources/page.tsx',
      'skills/gap-analyzer/page.tsx'
    ];

    for (const pagePath of pagesToCheck) {
      const fullPath = path.join(__dirname, '../faafo-career-platform/src/app', pagePath);
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        if (content.includes('export const metadata')) {
          this.results.content.push(`✅ ${pagePath} has metadata configuration`);
        } else {
          this.results.content.push(`❌ ${pagePath} missing metadata configuration`);
        }

        // Check for heading structure
        if (content.includes('<h1') || content.includes('heading')) {
          this.results.content.push(`✅ ${pagePath} has heading structure`);
        } else {
          this.results.content.push(`❌ ${pagePath} missing proper heading structure`);
        }
      }
    }
  }

  async checkPerformance() {
    console.log('⚡ Checking Performance Factors...');

    // Check for image optimization
    const publicPath = path.join(__dirname, '../faafo-career-platform/public');
    if (fs.existsSync(publicPath)) {
      const files = fs.readdirSync(publicPath, { recursive: true });
      const imageFiles = files.filter(file => 
        typeof file === 'string' && /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(file)
      );
      
      if (imageFiles.length > 0) {
        this.results.performance.push(`✅ Found ${imageFiles.length} image files`);
        
        // Check for WebP images
        const webpFiles = imageFiles.filter(file => file.endsWith('.webp'));
        if (webpFiles.length > 0) {
          this.results.performance.push(`✅ ${webpFiles.length} WebP images found (good for performance)`);
        } else {
          this.results.performance.push('⚠️ No WebP images found - consider converting for better performance');
        }
      }
    }

    // Check for Next.js optimization features
    const nextConfigPath = path.join(__dirname, '../faafo-career-platform/next.config.js');
    if (fs.existsSync(nextConfigPath)) {
      const configContent = fs.readFileSync(nextConfigPath, 'utf8');
      
      if (configContent.includes('images:')) {
        this.results.performance.push('✅ Next.js Image optimization configured');
      } else {
        this.results.performance.push('⚠️ Next.js Image optimization not configured');
      }
    }
  }

  generateRecommendations() {
    console.log('💡 Generating SEO Recommendations...');

    // High Priority Recommendations
    this.results.recommendations.push({
      priority: 'HIGH',
      category: 'Content Marketing',
      recommendation: 'Create 20+ high-quality blog posts targeting long-tail keywords',
      impact: 'Significant organic traffic increase',
      effort: 'High'
    });

    this.results.recommendations.push({
      priority: 'HIGH',
      category: 'Technical SEO',
      recommendation: 'Implement comprehensive structured data for all page types',
      impact: 'Better search result appearance and click-through rates',
      effort: 'Medium'
    });

    this.results.recommendations.push({
      priority: 'HIGH',
      category: 'Local SEO',
      recommendation: 'Create location-based landing pages for major cities',
      impact: 'Capture local career coaching searches',
      effort: 'Medium'
    });

    // Medium Priority Recommendations
    this.results.recommendations.push({
      priority: 'MEDIUM',
      category: 'Link Building',
      recommendation: 'Develop partnerships with career blogs and educational sites',
      impact: 'Improved domain authority and referral traffic',
      effort: 'High'
    });

    this.results.recommendations.push({
      priority: 'MEDIUM',
      category: 'Performance',
      recommendation: 'Optimize images and implement lazy loading',
      impact: 'Better Core Web Vitals scores',
      effort: 'Low'
    });

    // Low Priority Recommendations
    this.results.recommendations.push({
      priority: 'LOW',
      category: 'Social SEO',
      recommendation: 'Optimize social media profiles and create shareable content',
      impact: 'Increased brand awareness and social signals',
      effort: 'Medium'
    });
  }

  generateReport() {
    console.log('\n📊 SEO AUDIT REPORT\n');
    console.log('='.repeat(50));

    // Technical SEO Results
    console.log('\n🔧 TECHNICAL SEO:');
    this.results.technical.forEach(result => console.log(`  ${result}`));

    // Content SEO Results
    console.log('\n📝 CONTENT SEO:');
    this.results.content.forEach(result => console.log(`  ${result}`));

    // Performance Results
    console.log('\n⚡ PERFORMANCE:');
    this.results.performance.forEach(result => console.log(`  ${result}`));

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('\n🔴 HIGH PRIORITY:');
    this.results.recommendations
      .filter(r => r.priority === 'HIGH')
      .forEach(r => {
        console.log(`  📌 ${r.recommendation}`);
        console.log(`     Category: ${r.category}`);
        console.log(`     Impact: ${r.impact}`);
        console.log(`     Effort: ${r.effort}\n`);
      });

    console.log('🟡 MEDIUM PRIORITY:');
    this.results.recommendations
      .filter(r => r.priority === 'MEDIUM')
      .forEach(r => {
        console.log(`  📌 ${r.recommendation}`);
        console.log(`     Category: ${r.category}`);
        console.log(`     Impact: ${r.impact}`);
        console.log(`     Effort: ${r.effort}\n`);
      });

    console.log('🟢 LOW PRIORITY:');
    this.results.recommendations
      .filter(r => r.priority === 'LOW')
      .forEach(r => {
        console.log(`  📌 ${r.recommendation}`);
        console.log(`     Category: ${r.category}`);
        console.log(`     Impact: ${r.impact}`);
        console.log(`     Effort: ${r.effort}\n`);
      });

    // Next Steps
    console.log('🚀 IMMEDIATE NEXT STEPS:');
    console.log('  1. Set up Google Search Console and Google Analytics');
    console.log('  2. Submit sitemap to search engines');
    console.log('  3. Start creating high-value blog content');
    console.log('  4. Optimize existing page titles and meta descriptions');
    console.log('  5. Implement comprehensive structured data');

    // Save report to file
    const reportData = {
      timestamp: new Date().toISOString(),
      technical: this.results.technical,
      content: this.results.content,
      performance: this.results.performance,
      recommendations: this.results.recommendations
    };

    const reportPath = path.join(__dirname, '../seo-audit-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  }
}

// Run the audit
if (require.main === module) {
  const auditor = new SEOAuditor();
  auditor.runAudit().catch(console.error);
}

module.exports = SEOAuditor;
