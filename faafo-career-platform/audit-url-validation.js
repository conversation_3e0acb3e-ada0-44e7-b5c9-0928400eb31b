const { PrismaClient } = require('@prisma/client');
const https = require('https');
const http = require('http');
const { URL } = require('url');

const prisma = new PrismaClient();

async function testUrl(url, timeout = 10000) {
  return new Promise((resolve) => {
    try {
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const client = isHttps ? https : http;
      
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: 'HEAD',
        timeout: timeout,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; FAAFO-Audit/1.0)'
        }
      };

      const req = client.request(options, (res) => {
        resolve({
          url,
          status: res.statusCode,
          statusText: res.statusMessage,
          success: res.statusCode >= 200 && res.statusCode < 400,
          redirected: res.statusCode >= 300 && res.statusCode < 400,
          headers: {
            contentType: res.headers['content-type'],
            location: res.headers['location']
          }
        });
      });

      req.on('error', (error) => {
        resolve({
          url,
          status: 0,
          statusText: error.message,
          success: false,
          error: error.code || error.message
        });
      });

      req.on('timeout', () => {
        req.destroy();
        resolve({
          url,
          status: 0,
          statusText: 'Request timeout',
          success: false,
          error: 'TIMEOUT'
        });
      });

      req.end();
    } catch (error) {
      resolve({
        url,
        status: 0,
        statusText: error.message,
        success: false,
        error: 'INVALID_URL'
      });
    }
  });
}

async function auditUrlValidation() {
  console.log('🔍 CRITICAL AUDIT: URL Validation Check');
  console.log('========================================\n');

  try {
    // Get all active resources
    const resources = await prisma.learningResource.findMany({
      where: { isActive: true },
      select: {
        id: true,
        title: true,
        url: true,
        cost: true,
        author: true
      },
      orderBy: { title: 'asc' }
    });

    console.log(`Found ${resources.length} active resources to test\n`);

    const results = [];
    const batchSize = 5; // Test 5 URLs at a time to avoid overwhelming servers
    
    console.log('Testing URLs (this may take a few minutes)...\n');

    for (let i = 0; i < resources.length; i += batchSize) {
      const batch = resources.slice(i, i + batchSize);
      console.log(`Testing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(resources.length/batchSize)}...`);
      
      const batchPromises = batch.map(resource => 
        testUrl(resource.url).then(result => ({
          ...result,
          resourceId: resource.id,
          title: resource.title,
          cost: resource.cost,
          author: resource.author
        }))
      );
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Small delay between batches to be respectful
      if (i + batchSize < resources.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Analyze results
    const successfulUrls = results.filter(r => r.success);
    const failedUrls = results.filter(r => !r.success);
    const redirectedUrls = results.filter(r => r.redirected);
    
    console.log('\n==========================================');
    console.log('🎯 URL VALIDATION RESULTS:');
    console.log('==========================================');
    console.log(`Total URLs tested: ${results.length}`);
    console.log(`Successful (2xx): ${successfulUrls.length}`);
    console.log(`Failed (4xx, 5xx, errors): ${failedUrls.length}`);
    console.log(`Redirected (3xx): ${redirectedUrls.length}`);
    console.log(`Success rate: ${((successfulUrls.length / results.length) * 100).toFixed(1)}%`);
    console.log(`Previous agent claimed: 100% success rate`);
    console.log(`✓ Match: ${failedUrls.length === 0 ? 'YES' : 'NO'}`);

    if (failedUrls.length > 0) {
      console.log('\n🚨 FAILED URLS FOUND:');
      failedUrls.forEach(result => {
        console.log(`   ❌ ${result.title}`);
        console.log(`      URL: ${result.url}`);
        console.log(`      Status: ${result.status} - ${result.statusText}`);
        console.log(`      Error: ${result.error || 'N/A'}`);
        console.log(`      Cost: ${result.cost}`);
        console.log('');
      });
    }

    if (redirectedUrls.length > 0) {
      console.log('\n⚠️  REDIRECTED URLS (may need updating):');
      redirectedUrls.forEach(result => {
        console.log(`   🔄 ${result.title}`);
        console.log(`      URL: ${result.url}`);
        console.log(`      Status: ${result.status} - ${result.statusText}`);
        console.log(`      Redirects to: ${result.headers.location || 'Unknown'}`);
        console.log('');
      });
    }

    // Check for potential paywalls (free resources that might not be free)
    const suspiciousFreeResources = successfulUrls.filter(r => 
      r.cost === 'FREE' && 
      r.headers.contentType && 
      r.headers.contentType.includes('text/html') &&
      (r.url.includes('coursera') || r.url.includes('udemy') || r.url.includes('linkedin'))
    );

    if (suspiciousFreeResources.length > 0) {
      console.log('\n⚠️  SUSPICIOUS "FREE" RESOURCES (may have paywalls):');
      suspiciousFreeResources.forEach(result => {
        console.log(`   💰 ${result.title}`);
        console.log(`      URL: ${result.url}`);
        console.log(`      Marked as: ${result.cost}`);
        console.log('');
      });
    }

    console.log('\n==========================================');
    console.log('📊 DETAILED BREAKDOWN BY STATUS:');
    console.log('==========================================');
    
    const statusCounts = {};
    results.forEach(result => {
      const status = result.status || 'ERROR';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    Object.entries(statusCounts).sort().forEach(([status, count]) => {
      console.log(`   ${status}: ${count} URLs`);
    });

    return {
      total: results.length,
      successful: successfulUrls.length,
      failed: failedUrls.length,
      redirected: redirectedUrls.length,
      successRate: (successfulUrls.length / results.length) * 100,
      failedUrls: failedUrls,
      suspiciousFree: suspiciousFreeResources.length
    };

  } catch (error) {
    console.error('❌ URL validation audit failed:', error);
    return null;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the audit
auditUrlValidation().then(results => {
  if (results) {
    console.log('\n🏁 URL Audit Complete');
    process.exit(results.failed > 0 ? 1 : 0);
  } else {
    console.log('\n❌ URL Audit Failed');
    process.exit(1);
  }
});
