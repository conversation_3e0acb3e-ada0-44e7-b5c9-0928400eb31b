const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Broken URLs identified in audit that need to be fixed or removed
const BROKEN_URLS = [
  'https://aws.amazon.com/training/learning-paths/devops/',
  'https://helpx.adobe.com/xd/user-guide.html',
  'https://www.coursera.org/browse/personal-development/personal-finance',
  'https://blog.hubspot.com/marketing/go-to-market-strategy',
  'https://www.coursera.org/learn/technical-product-management'
];

// Redirected URLs that need updating to their final destinations
const URL_REDIRECTS = {
  'https://aws.amazon.com/training/digital/aws-cloud-practitioner-essentials/': 'https://aws.amazon.com/training/learn-about/cloud-practitioner/',
  'https://www.edx.org/course/introduction-to-investments': 'https://www.edx.org/learn/investing/indian-institute-of-management-bangalore-introduction-to-investments',
  'https://developer.android.com/courses/android-basics-kotlin/course': 'https://developer.android.com/courses/android-basics-compose/course',
  'https://www.edx.org/professional-certificate/uc-berkeleyx-blockchain-fundamentals': 'https://www.edx.org/certificates/professional-certificate/uc-berkeleyx-blockchain-fundamentals',
  'https://www.strategyzer.com/canvas/business-model-canvas': 'https://www.strategyzer.com/library/business-model-canvas',
  'https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/migrate/': 'https://learn.microsoft.com/en-us/azure/cloud-adoption-framework/migrate/',
  'https://www.figma.com/resources/learn-design-systems/': 'https://www.figma.com/resource-library/design-basics/',
  'https://flutter.dev/docs/get-started/codelab': 'https://docs.flutter.dev/get-started/codelab',
  'https://learndigital.withgoogle.com/digitalgarage/course/digital-marketing': 'https://grow.google',
  'https://docs.gitlab.com/ee/ci/': 'https://docs.gitlab.com/ci/',
  'https://analytics.google.com/analytics/academy/': 'https://goo.gle/ga-courses',
  'https://grow.google/certificates/product-management/': 'https://grow.google/intl/nl/',
  'https://grow.google/certificates/ux-design/': 'https://grow.google/intl/nl/',
  'https://www.investopedia.com/university/beginner/': 'https://www.investopedia.com/university/beginner',
  'https://www.edx.org/course/introduction-to-linux': 'https://www.edx.org/learn/linux/the-linux-foundation-introduction-to-linux',
  'https://developer.mozilla.org/en-US/docs/Learn': 'https://developer.mozilla.org/en-US/docs/Learn_web_development',
  'https://www.coursera.org/specializations/machine-learning-engineering-for-production-mlops': 'https://www.coursera.org/learn/introduction-to-machine-learning-in-production',
  'https://material.io/design': 'https://m3.material.io/',
  'https://docs.microsoft.com/en-us/azure/architecture/': 'https://learn.microsoft.com/en-us/azure/architecture/',
  'https://docs.microsoft.com/en-us/learn/paths/azure-fundamentals/': 'https://learn.microsoft.com/en-us/learn/paths/azure-fundamentals/',
  'https://material.io/design/introduction': 'https://m2.material.io/design/introduction/',
  'https://nodejs.org/en/learn': 'https://nodejs.org/en/learn/getting-started/introduction-to-nodejs',
  'https://amplitude.com/blog/product-analytics': 'https://amplitude.com/blog',
  'https://www.coursera.org/learn/uva-darden-product-management': 'https://www.coursera.org/learn/uva-darden-digital-product-management',
  'https://www.toastmasters.org/education/pathways-learning-experience': 'https://www.toastmasters.org/sitecore/service/nolayout.aspx?item=%2feducation%2fpathways-learning-experience&layout=%7b00000000-0000-0000-0000-000000000000%7d&device=Default',
  'https://www.linkedin.com/learning/project-management-foundations-2019': 'https://www.linkedin.com/learning/project-management-foundations-15528659',
  'https://blog.hubspot.com/sales/sales-training': 'https://www.hubspot.com/sales/sales-training',
  'https://learn.hashicorp.com/terraform': 'https://developer.hashicorp.com/terraform/tutorials'
};

// Working replacement URLs for broken resources
const URL_REPLACEMENTS = {
  'https://aws.amazon.com/training/learning-paths/devops/': 'https://aws.amazon.com/training/learn-about/devops/',
  'https://helpx.adobe.com/xd/user-guide.html': 'https://helpx.adobe.com/xd/get-started.html',
  'https://www.coursera.org/browse/personal-development/personal-finance': 'https://www.coursera.org/browse/business/finance',
  'https://blog.hubspot.com/marketing/go-to-market-strategy': 'https://blog.hubspot.com/marketing/gtm-strategy',
  'https://www.coursera.org/learn/technical-product-management': 'https://www.coursera.org/learn/real-world-product-management'
};

async function fixCriticalIssues() {
  console.log('🔧 FIXING CRITICAL ISSUES - FAAFO Career Platform');
  console.log('================================================\n');

  try {
    // 1. Fix broken URLs by replacing with working alternatives
    console.log('1. FIXING BROKEN URLS:');
    for (const [brokenUrl, replacementUrl] of Object.entries(URL_REPLACEMENTS)) {
      // Check if the replacement URL already exists
      const existingResource = await prisma.learningResource.findFirst({
        where: { url: replacementUrl, isActive: true }
      });

      if (existingResource) {
        // If replacement URL exists, deactivate the broken one instead
        const result = await prisma.learningResource.updateMany({
          where: { url: brokenUrl, isActive: true },
          data: { isActive: false }
        });

        if (result.count > 0) {
          console.log(`   ⚠️  Deactivated ${result.count} broken resource(s): ${brokenUrl} (replacement already exists)`);
        }
      } else {
        // Safe to update since replacement URL doesn't exist
        const result = await prisma.learningResource.updateMany({
          where: { url: brokenUrl, isActive: true },
          data: { url: replacementUrl }
        });

        if (result.count > 0) {
          console.log(`   ✅ Updated ${result.count} resource(s): ${brokenUrl} → ${replacementUrl}`);
        }
      }
    }

    // 2. Update redirected URLs to their final destinations
    console.log('\n2. UPDATING REDIRECTED URLS:');
    let redirectUpdateCount = 0;
    for (const [oldUrl, newUrl] of Object.entries(URL_REDIRECTS)) {
      // Check if the new URL already exists
      const existingResource = await prisma.learningResource.findFirst({
        where: { url: newUrl, isActive: true }
      });

      if (existingResource) {
        // If new URL exists, deactivate the old one instead of updating
        const result = await prisma.learningResource.updateMany({
          where: { url: oldUrl, isActive: true },
          data: { isActive: false }
        });

        if (result.count > 0) {
          console.log(`   ⚠️  Deactivated ${result.count} duplicate resource(s): ${oldUrl} (target URL already exists)`);
        }
      } else {
        // Safe to update since new URL doesn't exist
        const result = await prisma.learningResource.updateMany({
          where: { url: oldUrl, isActive: true },
          data: { url: newUrl }
        });

        if (result.count > 0) {
          console.log(`   ✅ Updated ${result.count} resource(s): ${oldUrl} → ${newUrl}`);
          redirectUpdateCount += result.count;
        }
      }
    }

    // 3. Remove any remaining broken URLs that can't be fixed
    console.log('\n3. REMOVING UNFIXABLE BROKEN URLS:');
    const remainingBrokenUrls = BROKEN_URLS.filter(url => !URL_REPLACEMENTS[url]);
    let removedCount = 0;
    
    for (const brokenUrl of remainingBrokenUrls) {
      const result = await prisma.learningResource.updateMany({
        where: { url: brokenUrl, isActive: true },
        data: { isActive: false }
      });
      
      if (result.count > 0) {
        console.log(`   ⚠️  Deactivated ${result.count} resource(s) with broken URL: ${brokenUrl}`);
        removedCount += result.count;
      }
    }

    // 4. Verify final state
    console.log('\n4. VERIFYING FIXES:');
    
    // Count total active resources
    const totalResources = await prisma.learningResource.count({
      where: { isActive: true }
    });
    
    // Check for any remaining broken URLs
    const remainingBrokenCount = await prisma.learningResource.count({
      where: {
        isActive: true,
        url: { in: BROKEN_URLS }
      }
    });

    // Check career paths still have resources
    const careerPaths = await prisma.careerPath.findMany({
      include: {
        learningResources: {
          where: { isActive: true }
        }
      }
    });

    const emptyPaths = careerPaths.filter(path => path.learningResources.length === 0);

    console.log(`   Total active resources: ${totalResources}`);
    console.log(`   Remaining broken URLs: ${remainingBrokenCount}`);
    console.log(`   Empty career paths: ${emptyPaths.length}`);

    // 5. Generate updated resource distribution
    console.log('\n5. UPDATED RESOURCE DISTRIBUTION:');
    careerPaths.forEach(path => {
      console.log(`   ${path.name}: ${path.learningResources.length} resources`);
    });

    console.log('\n================================================');
    console.log('🎯 FIX SUMMARY:');
    console.log(`✅ Broken URLs fixed: ${Object.keys(URL_REPLACEMENTS).length}`);
    console.log(`✅ Redirected URLs updated: ${redirectUpdateCount}`);
    console.log(`⚠️  Resources deactivated: ${removedCount}`);
    console.log(`📊 Total active resources: ${totalResources}`);
    console.log(`🎯 Remaining broken URLs: ${remainingBrokenCount}`);
    console.log(`🏁 Empty career paths: ${emptyPaths.length}`);
    
    if (remainingBrokenCount === 0 && emptyPaths.length === 0) {
      console.log('\n🎉 ALL CRITICAL ISSUES FIXED! Platform is now production-ready.');
    } else {
      console.log('\n⚠️  Some issues remain. Manual review required.');
    }

    console.log('================================================');

  } catch (error) {
    console.error('❌ Fix operation failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
if (require.main === module) {
  fixCriticalIssues()
    .then(() => {
      console.log('\n✅ Fix operation completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Fix operation failed:', error);
      process.exit(1);
    });
}

module.exports = { fixCriticalIssues };
