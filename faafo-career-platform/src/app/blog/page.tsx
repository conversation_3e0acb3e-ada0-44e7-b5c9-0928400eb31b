import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, User, ArrowRight } from 'lucide-react';
import EnhancedSEO from '@/components/seo/EnhancedSEO';

export const metadata: Metadata = {
  title: 'Career Change Blog | Expert Tips & Success Stories | FAAFO',
  description: 'Read expert career change advice, success stories, and practical tips. Learn from professionals who successfully transitioned careers with actionable insights.',
  keywords: [
    'career change blog',
    'career transition tips',
    'career change success stories',
    'professional development advice',
    'career coaching articles',
    'job change guidance',
    'career planning blog'
  ],
  openGraph: {
    title: 'Career Change Blog | Expert Tips & Success Stories',
    description: 'Expert career advice and real success stories to help you navigate your career transition successfully.',
    type: 'website',
    url: 'https://faafo-career.com/blog'
  }
};

// Sample blog posts data (in a real app, this would come from a CMS or database)
const blogPosts = [
  {
    id: 1,
    title: 'How to Change Careers at 40: A Complete Guide',
    excerpt: 'Discover proven strategies for successfully transitioning careers in your 40s, including financial planning, skill development, and networking tips.',
    category: 'Career Change',
    readTime: '8 min read',
    publishDate: '2024-01-15',
    author: '<PERSON>',
    slug: 'how-to-change-careers-at-40',
    featured: true
  },
  {
    id: 2,
    title: 'Breaking into Tech Without a Computer Science Degree',
    excerpt: 'Learn how to transition into tech roles through bootcamps, self-learning, and strategic career moves. Real stories from successful career changers.',
    category: 'Tech Careers',
    readTime: '6 min read',
    publishDate: '2024-01-12',
    author: 'Mike Chen',
    slug: 'breaking-into-tech-without-cs-degree',
    featured: true
  },
  {
    id: 3,
    title: 'The Ultimate Guide to Remote Work Career Paths',
    excerpt: 'Explore high-paying remote career options, learn how to position yourself for remote roles, and discover the skills employers want.',
    category: 'Remote Work',
    readTime: '10 min read',
    publishDate: '2024-01-10',
    author: 'Emily Rodriguez',
    slug: 'ultimate-guide-remote-work-careers',
    featured: false
  },
  {
    id: 4,
    title: 'From Corporate to Freelance: My 6-Month Transition Story',
    excerpt: 'A detailed account of transitioning from a corporate job to successful freelancing, including challenges, wins, and lessons learned.',
    category: 'Freelancing',
    readTime: '7 min read',
    publishDate: '2024-01-08',
    author: 'David Park',
    slug: 'corporate-to-freelance-transition-story',
    featured: false
  },
  {
    id: 5,
    title: 'Skill Gap Analysis: What It Is and Why You Need It',
    excerpt: 'Understand how to identify skill gaps in your career, create a learning plan, and use our free tool to accelerate your professional growth.',
    category: 'Skills Development',
    readTime: '5 min read',
    publishDate: '2024-01-05',
    author: 'Lisa Thompson',
    slug: 'skill-gap-analysis-guide',
    featured: false
  },
  {
    id: 6,
    title: 'Interview Tips That Actually Work: From 100+ Interviews',
    excerpt: 'Practical interview advice based on real experiences. Learn what works, what doesn\'t, and how to stand out in competitive job markets.',
    category: 'Interview Tips',
    readTime: '9 min read',
    publishDate: '2024-01-03',
    author: 'James Wilson',
    slug: 'interview-tips-that-work',
    featured: false
  }
];

const categories = ['All', 'Career Change', 'Tech Careers', 'Remote Work', 'Freelancing', 'Skills Development', 'Interview Tips'];

export default function BlogPage() {
  const featuredPosts = blogPosts.filter(post => post.featured);
  const regularPosts = blogPosts.filter(post => !post.featured);

  const structuredData = [
    {
      '@context': 'https://schema.org',
      '@type': 'Blog',
      name: 'FAAFO Career Change Blog',
      description: 'Expert career advice, tips, and success stories for professionals looking to change careers',
      url: 'https://faafo-career.com/blog',
      publisher: {
        '@type': 'Organization',
        name: 'FAAFO Career Platform',
        logo: 'https://faafo-career.com/images/logo.png'
      },
      blogPost: blogPosts.map(post => ({
        '@type': 'BlogPosting',
        headline: post.title,
        description: post.excerpt,
        url: `https://faafo-career.com/blog/${post.slug}`,
        datePublished: post.publishDate,
        author: {
          '@type': 'Person',
          name: post.author
        }
      }))
    }
  ];

  return (
    <>
      <EnhancedSEO
        title="Career Change Blog | Expert Tips & Success Stories | FAAFO"
        description="Read expert career change advice, success stories, and practical tips. Learn from professionals who successfully transitioned careers."
        keywords={['career change blog', 'career transition tips', 'career advice', 'professional development']}
        canonicalUrl="https://faafo-career.com/blog"
        structuredData={structuredData}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-12">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Career Change Blog
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Expert advice, real success stories, and practical tips to help you navigate your career transition successfully.
            </p>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {categories.map((category) => (
              <Badge
                key={category}
                variant={category === 'All' ? 'default' : 'outline'}
                className="cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900"
              >
                {category}
              </Badge>
            ))}
          </div>

          {/* Featured Posts */}
          {featuredPosts.length > 0 && (
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Featured Articles</h2>
              <div className="grid md:grid-cols-2 gap-6">
                {featuredPosts.map((post) => (
                  <Card key={post.id} className="hover:shadow-lg transition-shadow duration-300">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="secondary">{post.category}</Badge>
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <Clock className="w-4 h-4 mr-1" />
                          {post.readTime}
                        </div>
                      </div>
                      <CardTitle className="text-xl hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                        <Link href={`/blog/${post.slug}`}>
                          {post.title}
                        </Link>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600 dark:text-gray-300 mb-4">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <User className="w-4 h-4 mr-1" />
                          {post.author}
                          <Calendar className="w-4 h-4 ml-3 mr-1" />
                          {new Date(post.publishDate).toLocaleDateString()}
                        </div>
                        <Link 
                          href={`/blog/${post.slug}`}
                          className="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
                        >
                          Read More
                          <ArrowRight className="w-4 h-4 ml-1" />
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Regular Posts */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Latest Articles</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {regularPosts.map((post) => (
                <Card key={post.id} className="hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline">{post.category}</Badge>
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <Clock className="w-4 h-4 mr-1" />
                        {post.readTime}
                      </div>
                    </div>
                    <CardTitle className="text-lg hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                      <Link href={`/blog/${post.slug}`}>
                        {post.title}
                      </Link>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <User className="w-3 h-3 mr-1" />
                        {post.author}
                      </div>
                      <Link 
                        href={`/blog/${post.slug}`}
                        className="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium text-sm"
                      >
                        Read More
                        <ArrowRight className="w-3 h-3 ml-1" />
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Newsletter Signup */}
          <div className="mt-16 text-center">
            <Card className="max-w-2xl mx-auto">
              <CardContent className="pt-6">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Stay Updated with Career Tips
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  Get weekly career advice, success stories, and exclusive resources delivered to your inbox.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Subscribe
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
}
