import { NextResponse, NextRequest } from 'next/server';

// Force dynamic rendering to prevent static generation issues
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const revalidate = false;

// Validation schemas will be imported dynamically in handlers

// GET - List user's resumes
export async function GET(request: NextRequest): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      prisma,
      { getServerSession },
      { authOptions },
      { log },
      { withRateLimit, rateLimiters },
      { withUnifiedErrorHandling }
    ] = await Promise.all([
      import('@/lib/prisma').then(m => m.default),
      import('next-auth/next'),
      import('@/lib/auth'),
      import('@/lib/logger'),
      import('@/lib/rate-limit'),
      import('@/lib/unified-api-error-handler')
    ]);

    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const startTime = Date.now();
    const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    log.auth('resume_access_denied', undefined, false, {
      component: 'resume_builder_api',
      action: 'list_resumes'
    });
    const error = new Error('Not authenticated') as any;
    error.statusCode = 401;
    throw error;
  }

  log.info('Fetching user resumes', {
    component: 'resume_builder_api',
    action: 'list_resumes',
    userId: session.user.email
  });

  const dbStartTime = Date.now();
  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    select: { id: true }
  });

  if (!user) {
    const error = new Error('User not found') as any;
    error.statusCode = 404;
    throw error;
  }

  const resumes = await prisma.resume.findMany({
    where: {
      userId: user.id,
      isActive: true
    },
    select: {
      id: true,
      title: true,
      template: true,
      isPublic: true,
      lastExported: true,
      exportCount: true,
      createdAt: true,
      updatedAt: true
    },
    orderBy: { updatedAt: 'desc' }
  });

  const dbDuration = Date.now() - dbStartTime;
  log.database('findMany', 'resume', dbDuration, {
    userId: user.id
  });

  const totalDuration = Date.now() - startTime;
  log.api('GET', '/api/resume-builder', 200, totalDuration, {
    component: 'resume_builder_api',
    userId: session.user.email
  });

  const response = NextResponse.json({
    success: true,
    data: resumes
  });

  // Add caching headers for better performance
  response.headers.set('Cache-Control', 'private, max-age=30, stale-while-revalidate=120');

  return response;
  } catch (error: any) {
    console.error('Resume list fetch failed:', error.message);

    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}

// POST - Create new resume
export async function POST(request: NextRequest): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      prisma,
      { getServerSession },
      { authOptions },
      { log },
      { withRateLimit, rateLimiters },
      { withCSRFProtection },
      { ValidationPipelines }
    ] = await Promise.all([
      import('@/lib/prisma').then(m => m.default),
      import('next-auth/next'),
      import('@/lib/auth'),
      import('@/lib/logger'),
      import('@/lib/rate-limit'),
      import('@/lib/csrf'),
      import('@/lib/validation-pipeline')
    ]);

    return withCSRFProtection(request, async () => {
      // Apply rate limiting
      const rateLimitResult = withRateLimit(rateLimiters.api)(request);
      if (!rateLimitResult.allowed) {
        const error = new Error('Too many requests') as any;
        error.statusCode = 429;
        throw error;
      }
      const startTime = Date.now();
      const session = await getServerSession(authOptions);

      if (!session?.user?.email) {
        const error = new Error('Not authenticated') as any;
        error.statusCode = 401;
        throw error;
      }

      const user = await prisma.user.findUnique({
        where: { email: session.user.email },
        select: { id: true }
      });

      if (!user) {
        const error = new Error('User not found') as any;
        error.statusCode = 404;
        throw error;
      }

      const body = await request.json();

      // Step 1: Use validation pipeline for comprehensive validation and sanitization
      const personalInfoPipeline = ValidationPipelines.createPersonalInfoPipeline();
      const resumePipeline = ValidationPipelines.createResumePipeline();

      // Validate personal info
      const personalInfoResult = await personalInfoPipeline.validate(body.personalInfo);
      if (!personalInfoResult.isValid) {
        const error = new Error('Personal information validation failed') as any;
        error.statusCode = 400;
        error.details = personalInfoResult.errors;
        throw error;
      }

      // Validate resume data
      const resumeResult = await resumePipeline.validate(body);
      if (!resumeResult.isValid) {
        const error = new Error('Resume data validation failed') as any;
        error.statusCode = 400;
        error.details = resumeResult.errors;
        throw error;
      }

      // Step 2: Use sanitized data from validation pipeline
      const sanitizedBody = {
        ...resumeResult.sanitizedData,
        personalInfo: personalInfoResult.sanitizedData
      };

      // Step 3: Use sanitized data directly (validation already done by pipeline)
      const validatedData = sanitizedBody;

      log.info('Creating new resume', {
        component: 'resume_builder_api',
        action: 'create_resume',
        userId: user.id
      });

      const dbStartTime = Date.now();
      const resume = await prisma.resume.create({
        data: {
          userId: user.id,
          title: validatedData.title,
          personalInfo: validatedData.personalInfo,
          summary: validatedData.summary,
          experience: validatedData.experience || [],
          education: validatedData.education || [],
          skills: validatedData.skills || [],
          sections: validatedData.sections || {},
          template: validatedData.template,
          isPublic: validatedData.isPublic
        }
      });

      const dbDuration = Date.now() - dbStartTime;
      log.database('create', 'resume', dbDuration, {
        userId: user.id
      });

      const totalDuration = Date.now() - startTime;
      log.api('POST', '/api/resume-builder', 201, totalDuration, {
        component: 'resume_builder_api',
        userId: session.user.email
      });

      return NextResponse.json({
        success: true,
        data: resume
      });
    }) as Promise<NextResponse<any>>;
  } catch (error: any) {
    console.error('Resume creation failed:', error.message);

    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}
