import { NextResponse, NextRequest } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const revalidate = false;

// Validation schemas will be imported dynamically in handlers

// Validation schemas moved to dynamic imports in handlers


// GET - Retrieve specific resume
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      prisma,
      { getServerSession },
      { authOptions },
      { log },
      { withRateLimit, rateLimiters }
    ] = await Promise.all([
      import('@/lib/prisma').then(m => m.default),
      import('next-auth/next'),
      import('@/lib/auth'),
      import('@/lib/logger'),
      import('@/lib/rate-limit')
    ]);

    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const startTime = Date.now();
    const session = await getServerSession(authOptions);
    const { id } = await params;

    if (!session?.user?.email) {
      log.auth('resume_access_denied', undefined, false, {
        component: 'resume_builder_api',
        action: 'get_resume'
      });
      const error = new Error('Not authenticated') as any;
      error.statusCode = 401;
      throw error;
    }

    log.info('Fetching resume', {
      component: 'resume_builder_api',
      action: 'get_resume',
      userId: session.user.email
    });

    const dbStartTime = Date.now();
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    });

    if (!user) {
      const error = new Error('User not found') as any;
      error.statusCode = 404;
      throw error;
    }

    const resume = await prisma.resume.findFirst({
      where: {
        id,
        userId: user.id,
        isActive: true
      }
    });

    const dbDuration = Date.now() - dbStartTime;
    log.database('findFirst', 'resume', dbDuration, {
      userId: user.id
    });

    if (!resume) {
      const error = new Error('Resume not found') as any;
      error.statusCode = 404;
      throw error;
    }

    const totalDuration = Date.now() - startTime;
    log.api('GET', `/api/resume-builder/${id}`, 200, totalDuration, {
      component: 'resume_builder_api',
      userId: session.user.email
    });

    return NextResponse.json({
      success: true,
      data: resume
    });
  } catch (error: any) {
    console.error('Resume GET error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}

// PUT - Update resume
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      prisma,
      { getServerSession },
      { authOptions },
      { log },
      { withRateLimit, rateLimiters },
      { withCSRFProtection },
      { z },
      { ValidationPipelines }
    ] = await Promise.all([
      import('@/lib/prisma').then(m => m.default),
      import('next-auth/next'),
      import('@/lib/auth'),
      import('@/lib/logger'),
      import('@/lib/rate-limit'),
      import('@/lib/csrf'),
      import('zod'),
      import('@/lib/validation-pipeline')
    ]);

    // Define validation schemas
    const personalInfoSchema = z.object({
      firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
      lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
      email: z.string().email('Invalid email format').max(100, 'Email too long'),
      phone: z.string().optional(),
      location: z.string().optional(),
      website: z.string().url().optional().or(z.literal('')),
      linkedin: z.string().url().optional().or(z.literal('')),
      github: z.string().url().optional().or(z.literal(''))
    });

    return withCSRFProtection(request, async () => {
      // Apply rate limiting
      const rateLimitResult = withRateLimit(rateLimiters.api)(request);
      if (!rateLimitResult.allowed) {
        const error = new Error('Too many requests') as any;
        error.statusCode = 429;
        throw error;
      }
      const startTime = Date.now();
      const session = await getServerSession(authOptions);
      const { id } = await params;

      if (!session?.user?.email) {
        const error = new Error('Not authenticated') as any;
        error.statusCode = 401;
        throw error;
      }

      const user = await prisma.user.findUnique({
        where: { email: session.user.email },
        select: { id: true }
      });

      if (!user) {
        const error = new Error('User not found') as any;
        error.statusCode = 404;
        throw error;
      }

      // Verify resume ownership
      const existingResume = await prisma.resume.findFirst({
        where: {
          id,
          userId: user.id,
          isActive: true
        }
      });

      if (!existingResume) {
        const error = new Error('Resume not found') as any;
        error.statusCode = 404;
        throw error;
      }

      const body = await request.json();

      // Step 1: Use validation pipeline for comprehensive validation and sanitization
      const personalInfoPipeline = ValidationPipelines.createPersonalInfoPipeline();
      const resumePipeline = ValidationPipelines.createResumePipeline();

      // Validate personal info if provided
      if (body.personalInfo) {
        const personalInfoResult = await personalInfoPipeline.validate(body.personalInfo);
        if (!personalInfoResult.isValid) {
          const error = new Error('Personal information validation failed') as any;
          error.statusCode = 400;
          error.details = personalInfoResult.errors;
          throw error;
        }
        body.personalInfo = personalInfoResult.sanitizedData;
      }

      // Validate resume data
      const resumeResult = await resumePipeline.validate(body);
      if (!resumeResult.isValid) {
        const error = new Error('Resume data validation failed') as any;
        error.statusCode = 400;
        error.details = resumeResult.errors;
        throw error;
      }

      // Step 2: Use sanitized data from validation pipeline
      const sanitizedBody = resumeResult.sanitizedData;

      // Step 3: Use sanitized data directly (validation already done by pipeline)
      const validatedData = sanitizedBody;

      log.info('Updating resume', {
        component: 'resume_builder_api',
        action: 'update_resume',
        userId: user.id
      });

      const dbStartTime = Date.now();
      const updatedResume = await prisma.resume.update({
        where: { id },
        data: {
          ...validatedData,
          updatedAt: new Date()
        }
      });

      const dbDuration = Date.now() - dbStartTime;
      log.database('update', 'resume', dbDuration, {
        userId: user.id
      });

      const totalDuration = Date.now() - startTime;
      log.api('PUT', `/api/resume-builder/${id}`, 200, totalDuration, {
        component: 'resume_builder_api',
        userId: session.user.email
      });

      return NextResponse.json({
        success: true,
        data: updatedResume
      });
    }) as Promise<NextResponse<any>>;
  } catch (error: any) {
    console.error('Resume PUT error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}

// DELETE - Delete resume (soft delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      prisma,
      { getServerSession },
      { authOptions },
      { log },
      { withRateLimit, rateLimiters },
      { withCSRFProtection }
    ] = await Promise.all([
      import('@/lib/prisma').then(m => m.default),
      import('next-auth/next'),
      import('@/lib/auth'),
      import('@/lib/logger'),
      import('@/lib/rate-limit'),
      import('@/lib/csrf')
    ]);

    return withCSRFProtection(request, async () => {
      // Apply rate limiting
      const rateLimitResult = withRateLimit(rateLimiters.api)(request);
      if (!rateLimitResult.allowed) {
        const error = new Error('Too many requests') as any;
        error.statusCode = 429;
        throw error;
      }
      const startTime = Date.now();
      const session = await getServerSession(authOptions);
      const { id } = await params;

      if (!session?.user?.email) {
        const error = new Error('Not authenticated') as any;
        error.statusCode = 401;
        throw error;
      }

      const user = await prisma.user.findUnique({
        where: { email: session.user.email },
        select: { id: true }
      });

      if (!user) {
        const error = new Error('User not found') as any;
        error.statusCode = 404;
        throw error;
      }

      // Verify resume ownership
      const existingResume = await prisma.resume.findFirst({
        where: {
          id,
          userId: user.id,
          isActive: true
        }
      });

      if (!existingResume) {
        const error = new Error('Resume not found') as any;
        error.statusCode = 404;
        throw error;
      }

      log.info('Deleting resume', {
        component: 'resume_builder_api',
        action: 'delete_resume',
        userId: user.id
      });

      const dbStartTime = Date.now();
      // Soft delete by setting isActive to false
      await prisma.resume.update({
        where: { id },
        data: {
          isActive: false,
          updatedAt: new Date()
        }
      });

      const dbDuration = Date.now() - dbStartTime;
      log.database('update', 'resume', dbDuration, {
        userId: user.id,
        action: 'soft_delete'
      });

      const totalDuration = Date.now() - startTime;
      log.api('DELETE', `/api/resume-builder/${id}`, 200, totalDuration, {
        component: 'resume_builder_api',
        userId: session.user.email
      });

      return NextResponse.json({
        success: true,
        message: 'Resume deleted successfully'
      });
    }) as Promise<NextResponse<any>>;
  } catch (error: any) {
    console.error('Resume DELETE error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}
