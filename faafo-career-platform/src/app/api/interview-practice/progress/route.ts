// @ts-nocheck - Temporary type suppression for build
import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const revalidate = false;

import { UnifiedAuthenticationService } from '@/lib/unified-authentication-service';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
import prisma from '@/lib/prisma';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';

// Using unified caching service - no need for custom cache implementation

// GET - Retrieve user's interview practice progress
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Apply rate limiting
  const rateLimitResult = withRateLimit(rateLimiters.api)(request);
  if (!rateLimitResult.allowed) {
    const error = new Error('Too many requests') as any;
    error.statusCode = 429;
    throw error;
  }
  // Enhanced authentication using unified service
  const validation = await UnifiedAuthenticationService.validateSession(request, {
    validateUserExists: true,
    checkAccountLock: true,
    refreshSession: true,
    enableSecurityLogging: true
  });

  if (!validation.isValid) {
    const error = new Error(validation.error);
    (error as any).statusCode = validation.statusCode || 401;
    throw error;
  }

  const userId = validation.userId!;
  const { searchParams } = new URL(request.url);
  const skillArea = searchParams.get('skillArea');

  // Check cache first (only for general progress, not skill-specific)
  if (!skillArea) {
    const cachedData = await consolidatedCache.get<any>(`interview:progress:${userId}`);
    if (cachedData) {
      return NextResponse.json({
        success: true,
        data: cachedData,
        cached: true
      });
    }
  }

  // Get overall progress across all skill areas
  const progressQuery: any = { userId };
  if (skillArea) {
    progressQuery.skillArea = skillArea;
  }

  const progressRecords = await prisma.interviewProgress
    .findMany({
      where: progressQuery,
      orderBy: { lastPracticed: 'desc' }
    })
    .catch(error => {
      console.warn('Error fetching progress records:', error);
      return []; // Return empty array if progress records don't exist yet
    });

  // Get recent session statistics with optimized query
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

  const [recentSessions, sessionStats] = await Promise.all([
    prisma.interviewSession
      .findMany({
        where: {
          userId,
          createdAt: { gte: thirtyDaysAgo }
        },
        select: {
          id: true,
          status: true,
          totalQuestions: true,
          completedQuestions: true,
          timeSpent: true,
          overallScore: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: { createdAt: 'desc' }
      })
      .catch(error => {
        console.warn('Error fetching recent sessions:', error);
        return []; // Return empty array if sessions don't exist yet
      }),
    // Get aggregated response data separately to avoid N+1
    prisma.interviewResponse
      .findMany({
        where: {
          userId,
          createdAt: { gte: thirtyDaysAgo },
          isCompleted: true
        },
        select: {
          sessionId: true,
          aiScore: true,
          responseTime: true,
          questionId: true,
          question: {
            select: {
              questionType: true,
              category: true
            }
          }
        }
      })
      .catch(error => {
        console.warn('Error fetching session stats:', error);
        return []; // Return empty array if responses don't exist yet
      })
  ]);

  // Calculate overall statistics using optimized data
  const totalSessions = recentSessions.length;
  const completedSessions = recentSessions.filter(s => s.status === 'COMPLETED').length;
  const totalQuestions = (recentSessions as any).reduce((sum: number, s: any) => sum + Number(s.totalQuestions), 0);
  const completedQuestions = (recentSessions as any).reduce(
    (sum: number, s: any) => sum + Number(s.completedQuestions),
    0
  );

  // Use pre-fetched response data
  const allScores = sessionStats.filter(r => r.aiScore !== null).map(r => r.aiScore as number);

  const averageScore =
    allScores.length > 0
      ? allScores.reduce((sum, score) => sum + score, 0) / allScores.length
      : null;

  const bestScore = allScores.length > 0 ? Math.max(...allScores) : null;

  // Calculate practice time (approximate)
  const totalPracticeTime = (recentSessions as any).reduce((sum: number, s: any) => sum + Number(s.timeSpent), 0);

  // Group responses by session for easier processing
  const responsesBySession = sessionStats.reduce(
    (acc, response) => {
      if (!acc[response.sessionId]) {
        acc[response.sessionId] = [];
      }
      acc[response.sessionId]!.push(response);
      return acc;
    },
    {} as Record<string, any[]>
  );

  // Calculate skill area breakdown from pre-fetched data
  const skillAreaStats = sessionStats.reduce(
    (acc, response) => {
      const category = response?.question?.category || 'General';
      if (!acc[category]) {
        acc[category] = {
          category,
          scores: [],
          responseCount: 0,
          averageResponseTime: 0,
          totalResponseTime: 0
        };
      }

      if (response.aiScore !== null) {
        acc[category].scores.push(response.aiScore);
      }
      acc[category].responseCount++;
      acc[category].totalResponseTime += response.responseTime || 0;

      return acc;
    },
    {} as Record<string, any>
  );

  // Calculate averages for skill areas
  Object.values(skillAreaStats).forEach((stat: any) => {
    stat.averageScore =
      stat.scores.length > 0
        ? stat.scores.reduce((sum: number, score: number) => sum + score, 0) / stat.scores.length
        : null;
    stat.averageResponseTime =
      stat.responseCount > 0 ? stat.totalResponseTime / stat.responseCount : 0;
    delete stat.scores; // Remove raw scores array
    delete stat.totalResponseTime; // Remove intermediate calculation
  });

  // Calculate question type performance from pre-fetched data
  const questionTypeStats = sessionStats.reduce(
    (acc, response) => {
      const key = `${response?.question?.questionType || 'UNKNOWN'}_${response?.question?.category || 'General'}`;
      if (!acc[key]) {
        acc[key] = {
          questionType: response?.question?.questionType || 'UNKNOWN',
          category: response?.question?.category || 'General',
          scores: [],
          responseTimes: [],
          responseCount: 0
        };
      }

      if (response.aiScore !== null) {
        acc[key].scores.push(response.aiScore);
      }
      if (response.responseTime !== null) {
        acc[key].responseTimes.push(response.responseTime);
      }
      acc[key].responseCount++;

      return acc;
    },
    {} as Record<string, any>
  );

  // Calculate averages and convert to array
  const questionTypeStatsArray = Object.values(questionTypeStats)
    .map((stat: any) => ({
      questionType: stat.questionType,
      category: stat.category,
      responseCount: stat.responseCount,
      averageScore:
        stat.scores.length > 0
          ? stat.scores.reduce((sum: number, score: number) => sum + score, 0) / stat.scores.length
          : null,
      averageResponseTime:
        stat.responseTimes.length > 0
          ? stat.responseTimes.reduce((sum: number, time: number) => sum + time, 0) /
            stat.responseTimes.length
          : null
    }))
    .sort((a, b) => (b.averageScore || 0) - (a.averageScore || 0));

  // Calculate improvement trend (last 10 sessions) using optimized data
  const recentSessionScores = recentSessions
    .slice(0, 10)
    .reverse()
    .map(session => {
      const sessionResponses = responsesBySession[session.id] || [];
      const sessionScores = sessionResponses
        .filter(r => r.aiScore !== null)
        .map(r => r.aiScore as number);

      return sessionScores.length > 0
        ? sessionScores.reduce((sum, score) => sum + score, 0) / sessionScores.length
        : null;
    })
    .filter(score => score !== null) as number[];

  let improvementRate = null;
  if (recentSessionScores.length >= 2) {
    const firstHalf = recentSessionScores.slice(0, Math.floor(recentSessionScores.length / 2));
    const secondHalf = recentSessionScores.slice(Math.floor(recentSessionScores.length / 2));

    const firstAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length;

    improvementRate = ((secondAvg - firstAvg) / firstAvg) * 100;
  }

  // Calculate streak
  let currentStreak = 0;
  let longestStreak = 0;
  let tempStreak = 0;

  const sessionDates = recentSessions
    .map(s => s.createdAt.toDateString())
    .filter((date, index, arr) => arr.indexOf(date) === index)
    .sort();

  for (let i = 0; i < sessionDates.length; i++) {
    const currentDate = new Date(sessionDates[i]!);
    const prevDate = i > 0 ? new Date(sessionDates[i - 1]!) : null;

    if (prevDate && currentDate.getTime() - prevDate.getTime() <= 24 * 60 * 60 * 1000) {
      tempStreak++;
    } else {
      tempStreak = 1;
    }

    longestStreak = Math.max(longestStreak, tempStreak);

    if (i === sessionDates.length - 1) {
      const today = new Date().toDateString();
      const lastSessionDate = sessionDates[sessionDates.length - 1];
      if (
        lastSessionDate === today ||
        new Date().getTime() - new Date(lastSessionDate!).getTime() <= 24 * 60 * 60 * 1000
      ) {
        currentStreak = tempStreak;
      }
    }
  }

  const overallProgress = {
    totalSessions,
    completedSessions,
    totalQuestions,
    completedQuestions,
    averageScore,
    bestScore,
    totalPracticeTime,
    improvementRate,
    currentStreak,
    longestStreak,
    completionRate: totalQuestions > 0 ? (completedQuestions / totalQuestions) * 100 : 0
  };

  const responseData = {
    overallProgress,
    skillAreaProgress: progressRecords,
    skillAreaStats: Object.values(skillAreaStats),
    questionTypeStats: questionTypeStatsArray,
    recentSessions: recentSessions.slice(0, 5), // Last 5 sessions
    improvementTrend: recentSessionScores
  };

  // Cache the response if it's general progress (not skill-specific)
  if (!skillArea) {
    await consolidatedCache.set(`interview:progress:${userId}`, responseData, {
      ttl: 300000, // 5 minutes
      tags: ['interview', 'progress', userId]
    });
  }

  return NextResponse.json({
    success: true,
    data: responseData
  });
});

// POST - Update progress manually (for achievements, milestones, etc.)
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    // Enhanced authentication using unified service
    const validation = await UnifiedAuthenticationService.validateSession(request, {
      validateUserExists: true,
      checkAccountLock: true,
      refreshSession: true,
      enableSecurityLogging: true
    });

    if (!validation.isValid) {
      const error = new Error(validation.error);
      (error as any).statusCode = validation.statusCode || 401;
      throw error;
    }

    const userId = validation.userId!;

    // This endpoint can be used to manually update progress records
    // or trigger progress recalculation

    // Get all user's sessions for recalculation
    const userSessions = await prisma.interviewSession.findMany({
      where: { userId },
      include: {
        responses: {
          where: { userId },
          include: {
            question: {
              select: {
                category: true,
                questionType: true
              }
            }
          }
        }
      }
    });

    // Group by skill area and recalculate progress
    const skillAreaData: { [key: string]: any } = {};

    userSessions.forEach(session => {
      session.responses.forEach(response => {
        const category = response?.question?.category || 'General';

        if (!skillAreaData[category]) {
          skillAreaData[category] = {
            totalSessions: new Set(),
            completedSessions: new Set(),
            scores: [],
            practiceTime: 0,
            lastPracticed: null
          };
        }

        skillAreaData[category].totalSessions.add(session.id);
        if (session.status === 'COMPLETED') {
          skillAreaData[category].completedSessions.add(session.id);
        }

        if (response.aiScore) {
          skillAreaData[category].scores.push(response.aiScore);
        }

        skillAreaData[category].practiceTime += session.timeSpent;

        if (
          !skillAreaData[category].lastPracticed ||
          response.createdAt > skillAreaData[category].lastPracticed
        ) {
          skillAreaData[category].lastPracticed = response.createdAt;
        }
      });
    });

    // Update or create progress records
    const progressUpdates = await Promise.all(
      Object.entries(skillAreaData).map(async ([skillArea, data]) => {
        const averageScore =
          data.scores.length > 0
            ? data.scores.reduce((sum: number, score: number) => sum + score, 0) /
              data.scores.length
            : null;

        const bestScore = data.scores.length > 0 ? Math.max(...data.scores) : null;

        return prisma.interviewProgress.upsert({
          where: {
            userId_skillArea: {
              userId,
              skillArea: skillArea as any
            }
          },
          update: {
            totalSessions: data.totalSessions.size,
            completedSessions: data.completedSessions.size,
            averageScore,
            bestScore,
            lastSessionScore: data.scores[data.scores.length - 1] || null,
            totalPracticeTime: data.practiceTime,
            lastPracticed: data.lastPracticed,
            updatedAt: new Date()
          },
          create: {
            userId,
            skillArea: skillArea as any,
            totalSessions: data.totalSessions.size,
            completedSessions: data.completedSessions.size,
            averageScore,
            bestScore,
            lastSessionScore: data.scores[data.scores.length - 1] || null,
            totalPracticeTime: data.practiceTime,
            lastPracticed: data.lastPracticed
          }
        });
      })
    );

    return NextResponse.json({
      success: true,
      data: progressUpdates,
      message: 'Progress updated successfully'
    });
  });
});
