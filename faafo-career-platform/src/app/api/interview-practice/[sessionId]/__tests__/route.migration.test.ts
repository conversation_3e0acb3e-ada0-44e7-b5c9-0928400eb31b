/**
 * VDD Migration Tests for /api/interview-practice/[sessionId]/route.ts
 *
 * These tests verify the migration from legacy authentication services
 * (SessionSecurity, UserValidationService) to UnifiedAuthenticationService.
 *
 * Following VDD Cycle:
 * 1. These tests will FAIL initially (Red)
 * 2. Migration implementation will make them pass (Green)
 * 3. Refactor for quality (Refactor)
 */

import { NextRequest } from 'next/server';
import { GET, PATCH, DELETE } from '@/app/api/interview-practice/[sessionId]/route';
import { UnifiedAuthenticationService } from '@/lib/unified-authentication-service';
import prisma from '@/lib/prisma';

// Mock dependencies
jest.mock('@/lib/unified-authentication-service');
jest.mock('@/lib/prisma', () => ({
  prisma: {
    interviewSession: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    }
  }
}));

const mockUnifiedAuthService = UnifiedAuthenticationService as jest.Mocked<
  typeof UnifiedAuthenticationService
>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('Interview Session Route Migration Tests', () => {
  let mockRequest: NextRequest;
  let mockParams: { params: Promise<{ sessionId: string }> };

  beforeEach(() => {
    jest.clearAllMocks();
    mockRequest = new NextRequest('http://localhost:3000/api/interview-practice/test-session-id');
    mockParams = { params: Promise.resolve({ sessionId: 'test-session-id' }) };

    // Set up default mocks for all UnifiedAuthenticationService methods
    mockUnifiedAuthService.validateSession.mockResolvedValue({
      isValid: true,
      userId: 'test-user-id',
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        emailVerified: null
      }
    });

    mockUnifiedAuthService.validateSessionAccess.mockResolvedValue({
      isValid: true,
      userId: 'test-user-id',
      sessionId: 'test-session-id',
      hasAccess: true
    });

    mockUnifiedAuthService.validateSessionStateTransition.mockReturnValue({
      isValid: true,
      canTransition: true
    });
  });

  describe('GET /api/interview-practice/[sessionId] - Migration Verification', () => {
    it('should use UnifiedAuthenticationService.validateSession instead of UserValidationService', async () => {
      // Arrange - Mock successful validation
      mockUnifiedAuthService.validateSession.mockResolvedValue({
        isValid: true,
        userId: 'test-user-id',
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'Test User',
          emailVerified: null
        }
      });

      mockPrisma.interviewSession.findFirst.mockResolvedValue({
        id: 'test-session-id',
        userId: 'test-user-id',
        totalQuestions: 5,
        questions: [],
        responses: []
      } as any);

      // Act
      await GET(mockRequest, mockParams);

      // Assert - Verify route uses UnifiedAuthenticationService with correct parameters
      expect(mockUnifiedAuthService.validateSession).toHaveBeenCalledWith(mockRequest, {
        validateUserExists: true,
        checkAccountLock: true,
        refreshSession: true,
        enableSecurityLogging: true
      });
      expect(mockUnifiedAuthService.validateSession).toHaveBeenCalledTimes(1);
    });

    it('should handle authentication failure with unified error format', async () => {
      // Arrange
      mockUnifiedAuthService.validateSession.mockResolvedValue({
        isValid: false,
        error: 'Authentication required',
        statusCode: 401
      });

      // Act
      const response = await GET(mockRequest, mockParams);

      // Assert
      expect(response.status).toBe(401);
      // Verify it's an authentication error response
      expect(response.ok).toBe(false);
    });

    it('should validate session access using unified service', async () => {
      // Arrange
      mockUnifiedAuthService.validateSession.mockResolvedValue({
        isValid: true,
        userId: 'test-user-id',
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'Test User',
          emailVerified: null
        }
      });

      mockUnifiedAuthService.validateSessionAccess.mockResolvedValue({
        isValid: true,
        userId: 'test-user-id',
        session: { id: 'test-session-id' }
      });

      mockPrisma.interviewSession.findFirst.mockResolvedValue({
        id: 'test-session-id',
        userId: 'test-user-id',
        totalQuestions: 5,
        questions: [],
        responses: []
      } as any);

      // Act
      await GET(mockRequest, mockParams);

      // Assert - This will FAIL initially
      expect(mockUnifiedAuthService.validateSessionAccess).toHaveBeenCalledWith(
        mockRequest,
        'test-session-id',
        { sessionType: 'interview' }
      );
    });
  });

  describe('PATCH /api/interview-practice/[sessionId] - Migration Verification', () => {
    it('should use UnifiedAuthenticationService.validateSessionAccess instead of SessionSecurity', async () => {
      // Arrange
      const mockBody = { status: 'IN_PROGRESS' };
      mockRequest = new NextRequest(
        'http://localhost:3000/api/interview-practice/test-session-id',
        {
          method: 'PATCH',
          body: JSON.stringify(mockBody),
          headers: { 'Content-Type': 'application/json' }
        }
      );

      mockUnifiedAuthService.validateSessionAccess.mockResolvedValue({
        isValid: true,
        userId: 'test-user-id',
        session: { id: 'test-session-id' }
      });

      mockPrisma.interviewSession.findUnique.mockResolvedValue({
        status: 'NOT_STARTED'
      } as any);

      mockPrisma.interviewSession.update.mockResolvedValue({
        id: 'test-session-id',
        status: 'IN_PROGRESS',
        questions: [],
        responses: []
      } as any);

      // Act
      await PATCH(mockRequest, mockParams);

      // Assert - This will FAIL initially because route still uses SessionSecurity
      expect(mockUnifiedAuthService.validateSessionAccess).toHaveBeenCalledWith(
        mockRequest,
        'test-session-id',
        { sessionType: 'interview' }
      );
    });

    it('should use UnifiedAuthenticationService.validateSessionStateTransition', async () => {
      // Arrange
      const mockBody = { status: 'COMPLETED' };
      mockRequest = new NextRequest(
        'http://localhost:3000/api/interview-practice/test-session-id',
        {
          method: 'PATCH',
          body: JSON.stringify(mockBody),
          headers: { 'Content-Type': 'application/json' }
        }
      );

      mockUnifiedAuthService.validateSessionAccess.mockResolvedValue({
        isValid: true,
        userId: 'test-user-id'
      });

      mockPrisma.interviewSession.findUnique.mockResolvedValue({
        status: 'IN_PROGRESS'
      } as any);

      mockPrisma.interviewSession.update.mockResolvedValue({
        id: 'test-session-id',
        status: 'COMPLETED',
        questions: [],
        responses: []
      } as any);

      // Act
      await PATCH(mockRequest, mockParams);

      // Assert - This will FAIL initially
      expect(UnifiedAuthenticationService.validateSessionStateTransition).toHaveBeenCalledWith(
        'IN_PROGRESS',
        'COMPLETED',
        'interview'
      );
    });
  });

  describe('DELETE /api/interview-practice/[sessionId] - Migration Verification', () => {
    it('should use UnifiedAuthenticationService.validateSessionAccess instead of SessionSecurity', async () => {
      // Arrange
      mockRequest = new NextRequest(
        'http://localhost:3000/api/interview-practice/test-session-id',
        {
          method: 'DELETE'
        }
      );

      mockUnifiedAuthService.validateSessionAccess.mockResolvedValue({
        isValid: true,
        userId: 'test-user-id'
      });

      mockPrisma.interviewSession.delete.mockResolvedValue({
        id: 'test-session-id'
      } as any);

      // Act
      await DELETE(mockRequest, mockParams);

      // Assert - This will FAIL initially because route still uses SessionSecurity
      expect(mockUnifiedAuthService.validateSessionAccess).toHaveBeenCalledWith(
        mockRequest,
        'test-session-id',
        { sessionType: 'interview' }
      );
    });
  });

  describe('Migration Completeness Tests', () => {
    it('should not import legacy SessionSecurity service', () => {
      // This test verifies that after migration, the route file doesn't import SessionSecurity
      const routeFileContent = require('fs').readFileSync(
        require('path').join(__dirname, '../route.ts'),
        'utf8'
      );

      // This will FAIL initially and pass after migration
      expect(routeFileContent).not.toContain('SessionSecurity');
      expect(routeFileContent).not.toContain('@/lib/session-security');
    });

    it('should not import legacy UserValidationService', () => {
      // This test verifies that after migration, the route file doesn't import UserValidationService
      const routeFileContent = require('fs').readFileSync(
        require('path').join(__dirname, '../route.ts'),
        'utf8'
      );

      // This will FAIL initially and pass after migration
      expect(routeFileContent).not.toContain('UserValidationService');
      expect(routeFileContent).not.toContain('@/lib/user-validation-service');
    });

    it('should import UnifiedAuthenticationService', () => {
      // This test verifies that after migration, the route file imports UnifiedAuthenticationService
      const routeFileContent = require('fs').readFileSync(
        require('path').join(__dirname, '../route.ts'),
        'utf8'
      );

      // This will FAIL initially and pass after migration
      expect(routeFileContent).toContain('UnifiedAuthenticationService');
      expect(routeFileContent).toContain('@/lib/unified-authentication-service');
    });
  });
});
