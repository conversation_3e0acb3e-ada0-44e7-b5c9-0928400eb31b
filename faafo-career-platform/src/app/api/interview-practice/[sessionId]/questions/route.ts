import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const revalidate = false;

// Enhanced fallback question banks organized by type and context
const QUESTION_BANKS = {
  // Core behavioral questions - always relevant
  BEHAVIORAL_CORE: [
    {
      questionText: 'Tell me about yourself and your professional background.',
      questionType: 'BEHAVIORAL',
      category: 'GENERAL',
      difficulty: 'BEGINNER',
      expectedDuration: 180,
      context:
        'This is a common opening question to assess communication skills and professional summary.',
      hints: {
        structure: 'Use a brief professional summary highlighting relevant experience',
        keyPoints: ['Current role', 'Key achievements', 'Career goals'],
        commonMistakes: ['Being too personal', 'Rambling without structure']
      },
      followUpQuestions: ['What interests you about this role?'],
      industrySpecific: false,
      tags: ['introduction', 'general'],
      isRequired: true,
      priority: 1
    },
    {
      questionText: 'Why are you interested in this position?',
      questionType: 'BEHAVIORAL',
      category: 'GENERAL',
      difficulty: 'BEGINNER',
      expectedDuration: 120,
      context: 'Assesses motivation and research about the role and company.',
      hints: {
        structure: 'Connect your skills and interests to the role requirements',
        keyPoints: ['Role alignment', 'Company research', 'Career goals'],
        commonMistakes: ['Generic answers', 'Focusing only on benefits to you']
      },
      followUpQuestions: ['What do you know about our company?'],
      industrySpecific: false,
      tags: ['motivation', 'research'],
      isRequired: true,
      priority: 1
    },
    {
      questionText: 'Describe a challenging situation you faced at work and how you handled it.',
      questionType: 'BEHAVIORAL',
      category: 'PROBLEM_SOLVING',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 240,
      context: 'Uses STAR method to assess problem-solving and resilience.',
      hints: {
        structure: 'Use STAR method: Situation, Task, Action, Result',
        keyPoints: ['Clear problem description', 'Your specific actions', 'Positive outcome'],
        commonMistakes: ['Vague situations', 'Not taking ownership', 'No clear result']
      },
      followUpQuestions: [
        'What would you do differently?',
        'How did this experience change your approach?'
      ],
      industrySpecific: false,
      tags: ['problem-solving', 'star-method'],
      isRequired: true,
      priority: 1
    },
    {
      questionText: 'What are your greatest strengths and how do they apply to this role?',
      questionType: 'BEHAVIORAL',
      category: 'SOFT_SKILLS',
      difficulty: 'BEGINNER',
      expectedDuration: 150,
      context: 'Evaluates self-awareness and role alignment.',
      hints: {
        structure: 'Choose 2-3 relevant strengths with specific examples',
        keyPoints: ['Role-relevant strengths', 'Concrete examples', 'Impact on work'],
        commonMistakes: ['Generic strengths', 'No examples', 'Irrelevant to role']
      },
      followUpQuestions: ['Can you give me a specific example?'],
      industrySpecific: false,
      tags: ['strengths', 'self-awareness'],
      isRequired: true,
      priority: 1
    },
    {
      questionText: 'Where do you see yourself in 5 years?',
      questionType: 'BEHAVIORAL',
      category: 'GENERAL',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 120,
      context: 'Assesses career planning and long-term commitment.',
      hints: {
        structure: 'Show growth mindset while staying realistic',
        keyPoints: ['Career progression', 'Skill development', 'Value to company'],
        commonMistakes: ['Unrealistic goals', 'Vague answers', 'Job-hopping implications']
      },
      followUpQuestions: ['How does this role fit into your career plan?'],
      industrySpecific: false,
      tags: ['career-goals', 'planning'],
      isRequired: true,
      priority: 1
    },
    {
      questionText: 'What are your biggest weaknesses and how are you working to improve them?',
      questionType: 'BEHAVIORAL',
      category: 'SOFT_SKILLS',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Assesses self-awareness, honesty, and commitment to personal growth.',
      hints: {
        structure: 'Choose a real weakness, explain impact, describe improvement efforts',
        keyPoints: ['Genuine weakness', 'Self-awareness', 'Improvement plan', 'Progress made'],
        commonMistakes: [
          'Fake weaknesses',
          'No improvement plan',
          'Weaknesses that are actually strengths'
        ]
      },
      followUpQuestions: [
        'How do you measure your progress?',
        'Can you give an example of improvement?'
      ],
      industrySpecific: false,
      tags: ['weaknesses', 'self-improvement', 'honesty'],
      isRequired: true,
      priority: 1
    },
    {
      questionText: 'Describe a time when you had to work with a difficult team member.',
      questionType: 'BEHAVIORAL',
      category: 'TEAMWORK',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 200,
      context: 'Evaluates interpersonal skills, conflict resolution, and team collaboration.',
      hints: {
        structure: 'Use STAR method focusing on your approach and resolution',
        keyPoints: [
          'Specific situation',
          'Your diplomatic approach',
          'Resolution achieved',
          'Relationship outcome'
        ],
        commonMistakes: ['Blaming the person', 'Not showing your role', 'No positive outcome']
      },
      followUpQuestions: [
        'How did you maintain professionalism?',
        'What did you learn about working with different personalities?'
      ],
      industrySpecific: false,
      tags: ['teamwork', 'conflict-resolution', 'interpersonal-skills'],
      isRequired: true,
      priority: 1
    },
    {
      questionText:
        'Tell me about a time you failed at something important. How did you handle it?',
      questionType: 'BEHAVIORAL',
      category: 'ADAPTABILITY',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 220,
      context: 'Tests resilience, learning from failure, and ability to bounce back.',
      hints: {
        structure: 'Describe failure, your response, lessons learned, and how you applied them',
        keyPoints: [
          'Honest failure example',
          'Emotional response',
          'Learning extracted',
          'Future application'
        ],
        commonMistakes: [
          'Minimizing the failure',
          'Not taking responsibility',
          'No learning outcome'
        ]
      },
      followUpQuestions: [
        'How has this failure shaped your approach?',
        'What would you do differently now?'
      ],
      industrySpecific: false,
      tags: ['failure', 'resilience', 'learning'],
      isRequired: true,
      priority: 1
    },
    {
      questionText: 'Describe a situation where you had to learn something completely new quickly.',
      questionType: 'BEHAVIORAL',
      category: 'ADAPTABILITY',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Assesses learning agility, adaptability, and approach to new challenges.',
      hints: {
        structure: 'Explain the situation, your learning strategy, and the outcome',
        keyPoints: [
          'Learning challenge',
          'Strategy used',
          'Resources leveraged',
          'Success achieved'
        ],
        commonMistakes: ['Vague learning process', 'Not showing urgency', 'No measurable outcome']
      },
      followUpQuestions: [
        'What learning strategies work best for you?',
        'How do you stay current in your field?'
      ],
      industrySpecific: false,
      tags: ['learning', 'adaptability', 'growth'],
      isRequired: false,
      priority: 1
    },
    {
      questionText: 'Give me an example of when you went above and beyond what was expected.',
      questionType: 'BEHAVIORAL',
      category: 'GENERAL',
      difficulty: 'BEGINNER',
      expectedDuration: 180,
      context: 'Evaluates initiative, work ethic, and commitment to excellence.',
      hints: {
        structure: 'Describe the baseline expectation, what you did extra, and the impact',
        keyPoints: [
          'Clear baseline',
          'Extra effort taken',
          'Impact on team/company',
          'Recognition received'
        ],
        commonMistakes: ['Unclear expectations', 'Self-serving actions', 'No measurable impact']
      },
      followUpQuestions: [
        'What motivated you to do more?',
        'How did others react to your extra effort?'
      ],
      industrySpecific: false,
      tags: ['initiative', 'excellence', 'work-ethic'],
      isRequired: false,
      priority: 1
    },
    {
      questionText:
        'Describe a time when you had to make a decision without all the information you needed.',
      questionType: 'BEHAVIORAL',
      category: 'PROBLEM_SOLVING',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context: 'Tests decision-making under uncertainty and risk assessment skills.',
      hints: {
        structure: 'Explain the situation, information gaps, decision process, and outcome',
        keyPoints: [
          'Information constraints',
          'Decision framework',
          'Risk assessment',
          'Result validation'
        ],
        commonMistakes: [
          'Not showing decision process',
          'Avoiding responsibility',
          'No follow-up validation'
        ]
      },
      followUpQuestions: ['How do you handle uncertainty?', 'What would you do differently?'],
      industrySpecific: false,
      tags: ['decision-making', 'uncertainty', 'risk-assessment'],
      isRequired: false,
      priority: 1
    }
  ],

  // Communication and interpersonal skills questions
  COMMUNICATION: [
    {
      questionText:
        'Describe a time when you had to explain a complex concept to someone without technical background.',
      questionType: 'BEHAVIORAL',
      category: 'COMMUNICATION',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context:
        'Assesses ability to communicate complex ideas clearly and adapt communication style.',
      hints: {
        structure: 'Use STAR method focusing on your communication approach',
        keyPoints: [
          'Audience analysis',
          'Simplification techniques',
          'Feedback verification',
          'Successful outcome'
        ],
        commonMistakes: ['Using jargon', 'Not checking understanding', 'One-way communication']
      },
      followUpQuestions: [
        'How do you adapt your communication style?',
        'What techniques work best for you?'
      ],
      industrySpecific: false,
      tags: ['communication', 'explanation', 'adaptation'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'Tell me about a time you had to deliver bad news to a client or stakeholder.',
      questionType: 'BEHAVIORAL',
      category: 'COMMUNICATION',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context: 'Evaluates difficult conversation skills and stakeholder management.',
      hints: {
        structure: 'Describe preparation, delivery approach, and follow-up actions',
        keyPoints: [
          'Preparation strategy',
          'Empathetic delivery',
          'Solution focus',
          'Relationship preservation'
        ],
        commonMistakes: ['Avoiding responsibility', 'No solution offered', 'Poor timing']
      },
      followUpQuestions: [
        'How do you prepare for difficult conversations?',
        'What was the outcome?'
      ],
      industrySpecific: false,
      tags: ['difficult-conversations', 'stakeholder-management', 'empathy'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Describe a situation where you had to persuade someone to see your point of view.',
      questionType: 'BEHAVIORAL',
      category: 'COMMUNICATION',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Tests persuasion skills, influence, and ability to build consensus.',
      hints: {
        structure: 'Explain the disagreement, your approach, and the resolution',
        keyPoints: [
          'Understanding their perspective',
          'Building rapport',
          'Logical arguments',
          'Mutual benefit'
        ],
        commonMistakes: ['Being pushy', 'Not listening', 'Win-lose mentality']
      },
      followUpQuestions: [
        'What persuasion techniques work best for you?',
        'How do you handle resistance?'
      ],
      industrySpecific: false,
      tags: ['persuasion', 'influence', 'consensus-building'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Tell me about a time when you received constructive criticism. How did you handle it?',
      questionType: 'BEHAVIORAL',
      category: 'COMMUNICATION',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 150,
      context: 'Assesses receptiveness to feedback and professional growth mindset.',
      hints: {
        structure: 'Describe the feedback, your initial reaction, and how you used it',
        keyPoints: ['Open reception', 'Clarifying questions', 'Action taken', 'Improvement shown'],
        commonMistakes: ['Defensive reaction', 'Not taking action', 'Dismissing feedback']
      },
      followUpQuestions: [
        'How do you seek feedback?',
        "What's the most valuable feedback you've received?"
      ],
      industrySpecific: false,
      tags: ['feedback', 'growth-mindset', 'self-improvement'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Describe a time when you had to present to a large group or senior leadership.',
      questionType: 'BEHAVIORAL',
      category: 'COMMUNICATION',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context:
        'Evaluates presentation skills, confidence, and ability to communicate with authority.',
      hints: {
        structure: 'Explain the context, preparation, delivery, and outcome',
        keyPoints: [
          'Audience analysis',
          'Content preparation',
          'Delivery techniques',
          'Q&A handling'
        ],
        commonMistakes: ['Poor preparation', 'Not engaging audience', 'Avoiding questions']
      },
      followUpQuestions: [
        'How do you handle presentation nerves?',
        'What makes a presentation effective?'
      ],
      industrySpecific: false,
      tags: ['presentations', 'public-speaking', 'leadership-communication'],
      isRequired: false,
      priority: 2
    }
  ],

  // Problem-solving and analytical thinking questions
  PROBLEM_SOLVING: [
    {
      questionText:
        "Walk me through your approach to solving a problem you've never encountered before.",
      questionType: 'BEHAVIORAL',
      category: 'PROBLEM_SOLVING',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Assesses systematic problem-solving approach and analytical thinking.',
      hints: {
        structure: 'Describe your systematic methodology step by step',
        keyPoints: [
          'Problem definition',
          'Information gathering',
          'Analysis approach',
          'Solution validation'
        ],
        commonMistakes: [
          'Jumping to solutions',
          'Not defining the problem',
          'No systematic approach'
        ]
      },
      followUpQuestions: [
        "How do you know when you've found the right solution?",
        'What resources do you typically use?'
      ],
      industrySpecific: false,
      tags: ['problem-solving', 'methodology', 'analytical-thinking'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Describe a time when you identified a process improvement opportunity. What did you do?',
      questionType: 'BEHAVIORAL',
      category: 'PROBLEM_SOLVING',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 200,
      context: 'Evaluates initiative, process thinking, and continuous improvement mindset.',
      hints: {
        structure: 'Explain the inefficiency, your analysis, solution, and impact',
        keyPoints: [
          'Problem identification',
          'Root cause analysis',
          'Solution design',
          'Implementation results'
        ],
        commonMistakes: [
          'Vague improvements',
          'No measurable impact',
          'Not considering stakeholders'
        ]
      },
      followUpQuestions: [
        'How do you identify improvement opportunities?',
        'What was the long-term impact?'
      ],
      industrySpecific: false,
      tags: ['process-improvement', 'efficiency', 'continuous-improvement'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'Tell me about a time when you had to analyze data to make a recommendation.',
      questionType: 'BEHAVIORAL',
      category: 'PROBLEM_SOLVING',
      difficulty: 'ADVANCED',
      expectedDuration: 220,
      context: 'Tests analytical skills, data interpretation, and evidence-based decision making.',
      hints: {
        structure: 'Describe the data, analysis method, insights, and recommendation',
        keyPoints: [
          'Data sources',
          'Analysis approach',
          'Key insights',
          'Actionable recommendations'
        ],
        commonMistakes: [
          'Weak analysis',
          'No clear insights',
          'Recommendations not supported by data'
        ]
      },
      followUpQuestions: [
        'What tools did you use for analysis?',
        'How did you validate your findings?'
      ],
      industrySpecific: false,
      tags: ['data-analysis', 'insights', 'evidence-based-decisions'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Describe a situation where you had to troubleshoot a complex issue with multiple potential causes.',
      questionType: 'BEHAVIORAL',
      category: 'PROBLEM_SOLVING',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context: 'Assesses systematic troubleshooting and root cause analysis skills.',
      hints: {
        structure: 'Explain the issue, your diagnostic approach, and resolution',
        keyPoints: [
          'Issue symptoms',
          'Diagnostic methodology',
          'Hypothesis testing',
          'Root cause identification'
        ],
        commonMistakes: ['Random troubleshooting', 'Not documenting steps', 'Stopping at symptoms']
      },
      followUpQuestions: [
        'How do you prioritize potential causes?',
        'What tools help you troubleshoot?'
      ],
      industrySpecific: false,
      tags: ['troubleshooting', 'root-cause-analysis', 'systematic-approach'],
      isRequired: false,
      priority: 2
    }
  ],

  // Situational and scenario-based questions
  SITUATIONAL_SCENARIOS: [
    {
      questionText:
        "How would you handle a situation where you're assigned a project with an unrealistic deadline?",
      questionType: 'SITUATIONAL',
      category: 'PROBLEM_SOLVING',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context:
        'Tests project management skills, stakeholder communication, and realistic planning.',
      hints: {
        structure: 'Describe assessment, communication, and negotiation approach',
        keyPoints: [
          'Scope analysis',
          'Risk assessment',
          'Stakeholder communication',
          'Alternative solutions'
        ],
        commonMistakes: [
          'Accepting without question',
          'Not communicating risks',
          'No alternative proposals'
        ]
      },
      followUpQuestions: [
        'How do you estimate project timelines?',
        "What if the deadline can't be moved?"
      ],
      industrySpecific: false,
      tags: ['project-management', 'deadline-management', 'stakeholder-communication'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'What would you do if you discovered a mistake in work that had already been delivered to a client?',
      questionType: 'SITUATIONAL',
      category: 'ETHICS',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 150,
      context: 'Evaluates integrity, accountability, and client relationship management.',
      hints: {
        structure: 'Immediate response, communication plan, and prevention measures',
        keyPoints: [
          'Immediate assessment',
          'Transparent communication',
          'Corrective action',
          'Prevention strategy'
        ],
        commonMistakes: ['Hiding the mistake', 'Blaming others', 'No prevention plan']
      },
      followUpQuestions: [
        'How do you prevent such mistakes?',
        'How do you rebuild trust after an error?'
      ],
      industrySpecific: false,
      tags: ['integrity', 'accountability', 'client-relations'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'How would you approach working with a team member who consistently misses deadlines?',
      questionType: 'SITUATIONAL',
      category: 'TEAMWORK',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Tests team management, performance issues, and collaborative problem-solving.',
      hints: {
        structure: 'Assessment, communication, support, and escalation if needed',
        keyPoints: [
          'Understanding root causes',
          'Supportive approach',
          'Clear expectations',
          'Documentation'
        ],
        commonMistakes: [
          'Immediate escalation',
          'Not understanding causes',
          'Taking over their work'
        ]
      },
      followUpQuestions: [
        'When would you escalate to management?',
        'How do you maintain team morale?'
      ],
      industrySpecific: false,
      tags: ['team-management', 'performance-issues', 'support'],
      isRequired: false,
      priority: 2
    }
  ],

  // Technical questions for technical interviews and roles
  TECHNICAL: [
    {
      questionText:
        'Describe a time you had to debug a particularly complex issue in a large codebase. What was your approach, and what tools did you use?',
      questionType: 'TECHNICAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context:
        'Assesses problem-solving skills, debugging abilities, and familiarity with relevant tools.',
      hints: {
        structure: 'Use STAR method: Situation, Task, Action, Result',
        keyPoints: [
          'Systematic debugging approach',
          'Tools and techniques used',
          'Root cause identification',
          'Prevention measures'
        ],
        commonMistakes: [
          'Vague problem description',
          'Not mentioning specific tools',
          'No learning outcome'
        ]
      },
      followUpQuestions: [
        'What debugging tools do you prefer and why?',
        'How do you prevent similar issues?'
      ],
      industrySpecific: true,
      tags: ['debugging', 'problem-solving', 'technical-skills'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Explain the difference between REST and GraphQL APIs. When would you choose one over the other?',
      questionType: 'TECHNICAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 120,
      context: 'Tests knowledge of common API architectures and their trade-offs.',
      hints: {
        structure: 'Compare key differences, then discuss use cases',
        keyPoints: [
          'Data fetching differences',
          'Performance considerations',
          'Use case scenarios'
        ],
        commonMistakes: [
          'Only theoretical knowledge',
          'Not mentioning trade-offs',
          'No real-world examples'
        ]
      },
      followUpQuestions: [
        'Have you worked with both in practice?',
        'What challenges have you faced with either?'
      ],
      industrySpecific: true,
      tags: ['apis', 'architecture', 'technical-knowledge'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        "How do you approach code reviews? What do you look for when reviewing someone else's code?",
      questionType: 'TECHNICAL',
      category: 'TEAMWORK',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 150,
      context: 'Evaluates collaboration skills and code quality standards.',
      hints: {
        structure: 'Describe your systematic approach and criteria',
        keyPoints: [
          'Code quality criteria',
          'Constructive feedback approach',
          'Learning opportunities'
        ],
        commonMistakes: [
          'Only focusing on syntax',
          'Not mentioning collaboration',
          'Being overly critical'
        ]
      },
      followUpQuestions: [
        'How do you handle disagreements in code reviews?',
        "What's the most valuable feedback you've received?"
      ],
      industrySpecific: true,
      tags: ['code-review', 'collaboration', 'quality'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Describe your experience with version control systems. How do you handle merge conflicts?',
      questionType: 'TECHNICAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'BEGINNER',
      expectedDuration: 120,
      context: 'Assesses familiarity with essential development tools and collaboration practices.',
      hints: {
        structure: 'Mention tools used, then describe conflict resolution process',
        keyPoints: ['Version control tools', 'Branching strategies', 'Conflict resolution steps'],
        commonMistakes: [
          'Only mentioning Git basics',
          'Not discussing team workflows',
          'Avoiding conflict scenarios'
        ]
      },
      followUpQuestions: [
        'What branching strategy do you prefer?',
        'How do you prevent conflicts?'
      ],
      industrySpecific: true,
      tags: ['version-control', 'git', 'collaboration'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'How do you stay current with new technologies and industry trends?',
      questionType: 'TECHNICAL',
      category: 'ADAPTABILITY',
      difficulty: 'BEGINNER',
      expectedDuration: 120,
      context: 'Evaluates commitment to continuous learning and professional development.',
      hints: {
        structure: 'List specific resources and learning methods',
        keyPoints: ['Learning resources', 'Practical application', 'Knowledge sharing'],
        commonMistakes: [
          'Vague answers',
          'Not mentioning practical application',
          'No specific examples'
        ]
      },
      followUpQuestions: [
        "What's the most recent technology you've learned?",
        'How do you evaluate new tools?'
      ],
      industrySpecific: true,
      tags: ['learning', 'technology', 'growth'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Walk me through how you would design a system to handle 1 million concurrent users.',
      questionType: 'TECHNICAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'ADVANCED',
      expectedDuration: 300,
      context: 'Tests system design skills, scalability understanding, and architectural thinking.',
      hints: {
        structure: 'Start with requirements, then architecture, scaling strategies, and trade-offs',
        keyPoints: ['Load balancing', 'Database scaling', 'Caching strategies', 'Monitoring'],
        commonMistakes: ['Jumping to solutions', 'Ignoring bottlenecks', 'No trade-off discussion']
      },
      followUpQuestions: [
        'How would you handle database bottlenecks?',
        'What monitoring would you implement?'
      ],
      industrySpecific: true,
      tags: ['system-design', 'scalability', 'architecture'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        "Explain the concept of technical debt and how you've dealt with it in past projects.",
      questionType: 'TECHNICAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context:
        'Assesses understanding of code quality, long-term thinking, and refactoring skills.',
      hints: {
        structure: 'Define technical debt, give examples, explain impact, describe resolution',
        keyPoints: ['Clear definition', 'Real examples', 'Business impact', 'Resolution strategy'],
        commonMistakes: ['Vague definition', 'No concrete examples', 'Not showing business impact']
      },
      followUpQuestions: [
        'How do you prevent technical debt?',
        'How do you prioritize debt vs features?'
      ],
      industrySpecific: true,
      tags: ['technical-debt', 'code-quality', 'refactoring'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Describe your experience with testing. What types of tests do you write and why?',
      questionType: 'TECHNICAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 200,
      context: 'Evaluates testing knowledge, quality mindset, and development practices.',
      hints: {
        structure: 'Explain testing pyramid, give examples of each type, discuss benefits',
        keyPoints: ['Unit tests', 'Integration tests', 'End-to-end tests', 'Testing strategy'],
        commonMistakes: [
          'Only mentioning one type',
          'No testing strategy',
          'Not explaining benefits'
        ]
      },
      followUpQuestions: [
        'How do you handle flaky tests?',
        "What's your approach to test-driven development?"
      ],
      industrySpecific: true,
      tags: ['testing', 'quality-assurance', 'development-practices'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'How do you approach performance optimization in your applications?',
      questionType: 'TECHNICAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'ADVANCED',
      expectedDuration: 220,
      context: 'Tests performance optimization skills and systematic problem-solving approach.',
      hints: {
        structure: 'Describe profiling, identify bottlenecks, optimization strategies, measurement',
        keyPoints: [
          'Performance profiling',
          'Bottleneck identification',
          'Optimization techniques',
          'Measurement'
        ],
        commonMistakes: ['Premature optimization', 'No measurement', 'Vague strategies']
      },
      followUpQuestions: [
        'What tools do you use for profiling?',
        'How do you balance performance vs readability?'
      ],
      industrySpecific: true,
      tags: ['performance', 'optimization', 'profiling'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'Explain your approach to database design and optimization.',
      questionType: 'TECHNICAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 200,
      context: 'Assesses database knowledge, design principles, and optimization skills.',
      hints: {
        structure: 'Discuss normalization, indexing, query optimization, and scaling',
        keyPoints: [
          'Schema design',
          'Indexing strategy',
          'Query optimization',
          'Scaling approaches'
        ],
        commonMistakes: [
          'Only theoretical knowledge',
          'No real-world examples',
          'Ignoring performance'
        ]
      },
      followUpQuestions: ['How do you handle database migrations?', 'When would you denormalize?'],
      industrySpecific: true,
      tags: ['database', 'optimization', 'design'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'Describe your experience with cloud platforms and deployment strategies.',
      questionType: 'TECHNICAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Evaluates modern deployment knowledge and cloud computing understanding.',
      hints: {
        structure: 'Mention platforms used, deployment strategies, monitoring, and scaling',
        keyPoints: ['Cloud platforms', 'CI/CD pipelines', 'Container orchestration', 'Monitoring'],
        commonMistakes: [
          'Only mentioning platforms',
          'No deployment strategy',
          'No monitoring discussion'
        ]
      },
      followUpQuestions: [
        'How do you handle rollbacks?',
        "What's your approach to infrastructure as code?"
      ],
      industrySpecific: true,
      tags: ['cloud', 'deployment', 'devops'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'How do you ensure security in your applications?',
      questionType: 'TECHNICAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context: 'Tests security awareness and implementation of security best practices.',
      hints: {
        structure: 'Discuss authentication, authorization, data protection, and security testing',
        keyPoints: [
          'Authentication methods',
          'Data encryption',
          'Input validation',
          'Security testing'
        ],
        commonMistakes: [
          'Surface-level knowledge',
          'No practical examples',
          'Ignoring common vulnerabilities'
        ]
      },
      followUpQuestions: ['How do you handle sensitive data?', 'What security tools do you use?'],
      industrySpecific: true,
      tags: ['security', 'authentication', 'data-protection'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'Explain your experience with microservices architecture and its trade-offs.',
      questionType: 'TECHNICAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'ADVANCED',
      expectedDuration: 240,
      context: 'Assesses architectural knowledge and understanding of distributed systems.',
      hints: {
        structure: 'Define microservices, discuss benefits, challenges, and when to use',
        keyPoints: [
          'Service boundaries',
          'Communication patterns',
          'Data consistency',
          'Operational complexity'
        ],
        commonMistakes: ['Only benefits', 'No trade-offs', 'Theoretical knowledge only']
      },
      followUpQuestions: [
        'How do you handle service communication?',
        'When would you choose monolith over microservices?'
      ],
      industrySpecific: true,
      tags: ['microservices', 'architecture', 'distributed-systems'],
      isRequired: false,
      priority: 2
    }
  ],

  // Leadership and management questions
  LEADERSHIP: [
    {
      questionText:
        'Describe a time when you had to lead a team through a difficult project or situation.',
      questionType: 'LEADERSHIP',
      category: 'LEADERSHIP',
      difficulty: 'ADVANCED',
      expectedDuration: 240,
      context: 'Assesses leadership skills, team management, and crisis handling abilities.',
      hints: {
        structure: 'Use STAR method focusing on leadership actions',
        keyPoints: [
          'Team challenges',
          'Leadership approach',
          'Communication strategy',
          'Results achieved'
        ],
        commonMistakes: [
          'Not showing actual leadership',
          'Focusing only on personal contributions',
          'No team impact'
        ]
      },
      followUpQuestions: ['How did you motivate the team?', 'What would you do differently?'],
      industrySpecific: false,
      tags: ['leadership', 'team-management', 'crisis-handling'],
      isRequired: false,
      priority: 3
    },
    {
      questionText: 'How do you handle conflicts between team members?',
      questionType: 'LEADERSHIP',
      category: 'COMMUNICATION',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Evaluates conflict resolution skills and team dynamics management.',
      hints: {
        structure: 'Describe your systematic approach to conflict resolution',
        keyPoints: [
          'Active listening',
          'Mediation techniques',
          'Win-win solutions',
          'Prevention strategies'
        ],
        commonMistakes: ['Avoiding conflicts', 'Taking sides', 'Not addressing root causes']
      },
      followUpQuestions: ['Can you give a specific example?', 'How do you prevent conflicts?'],
      industrySpecific: false,
      tags: ['conflict-resolution', 'team-dynamics', 'communication'],
      isRequired: false,
      priority: 3
    },
    {
      questionText:
        'Tell me about a time you had to make a difficult decision with limited information.',
      questionType: 'LEADERSHIP',
      category: 'PROBLEM_SOLVING',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context: 'Assesses decision-making skills under uncertainty and risk management.',
      hints: {
        structure: 'Explain the situation, decision process, and outcome',
        keyPoints: [
          'Information gathering',
          'Risk assessment',
          'Stakeholder consideration',
          'Decision rationale'
        ],
        commonMistakes: [
          'Not showing decision process',
          'Avoiding responsibility',
          'No learning from outcome'
        ]
      },
      followUpQuestions: [
        'How do you handle uncertainty?',
        'What did you learn from this experience?'
      ],
      industrySpecific: false,
      tags: ['decision-making', 'uncertainty', 'risk-management'],
      isRequired: false,
      priority: 3
    },
    {
      questionText: 'How do you motivate and develop your team members?',
      questionType: 'LEADERSHIP',
      category: 'MANAGEMENT',
      difficulty: 'ADVANCED',
      expectedDuration: 180,
      context: 'Evaluates people management and development skills.',
      hints: {
        structure: 'Describe your approach to motivation and development',
        keyPoints: [
          'Individual motivation factors',
          'Development opportunities',
          'Recognition strategies',
          'Performance management'
        ],
        commonMistakes: [
          'One-size-fits-all approach',
          'Only mentioning rewards',
          'No development focus'
        ]
      },
      followUpQuestions: [
        'How do you identify individual motivators?',
        'Can you share a success story?'
      ],
      industrySpecific: false,
      tags: ['motivation', 'team-development', 'people-management'],
      isRequired: false,
      priority: 3
    },
    {
      questionText:
        'Describe a time when you had to deliver bad news to your team or stakeholders.',
      questionType: 'LEADERSHIP',
      category: 'COMMUNICATION',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context:
        'Tests communication skills, transparency, and ability to manage difficult conversations.',
      hints: {
        structure:
          'Explain the situation, your communication approach, and how you supported the team',
        keyPoints: ['Clear communication', 'Empathy shown', 'Support provided', 'Team response'],
        commonMistakes: ['Avoiding responsibility', 'Poor timing', 'No follow-up support']
      },
      followUpQuestions: [
        'How did you prepare for the conversation?',
        'What support did you provide afterward?'
      ],
      industrySpecific: false,
      tags: ['difficult-conversations', 'transparency', 'communication'],
      isRequired: false,
      priority: 3
    },
    {
      questionText:
        'Tell me about a time you had to influence someone without having authority over them.',
      questionType: 'LEADERSHIP',
      category: 'LEADERSHIP',
      difficulty: 'ADVANCED',
      expectedDuration: 220,
      context: 'Evaluates influence skills, persuasion abilities, and collaborative leadership.',
      hints: {
        structure: 'Describe the situation, your influence strategy, and the outcome',
        keyPoints: [
          'Stakeholder analysis',
          'Influence tactics',
          'Relationship building',
          'Mutual benefit'
        ],
        commonMistakes: ['Using manipulation', 'Not showing mutual benefit', 'Forcing compliance']
      },
      followUpQuestions: [
        'What influence tactics work best for you?',
        'How do you build credibility?'
      ],
      industrySpecific: false,
      tags: ['influence', 'persuasion', 'stakeholder-management'],
      isRequired: false,
      priority: 3
    },
    {
      questionText:
        "Describe how you've built and maintained team culture in a remote or hybrid environment.",
      questionType: 'LEADERSHIP',
      category: 'MANAGEMENT',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 200,
      context: 'Tests modern leadership skills and ability to build culture in distributed teams.',
      hints: {
        structure: 'Explain culture vision, specific initiatives, measurement, and adaptation',
        keyPoints: [
          'Culture definition',
          'Remote engagement',
          'Communication strategies',
          'Team bonding'
        ],
        commonMistakes: [
          'Vague culture description',
          'No specific initiatives',
          'Not addressing remote challenges'
        ]
      },
      followUpQuestions: [
        'How do you measure team culture?',
        'What challenges have you faced with remote teams?'
      ],
      industrySpecific: false,
      tags: ['team-culture', 'remote-leadership', 'engagement'],
      isRequired: false,
      priority: 3
    },
    {
      questionText: 'Tell me about a time you had to manage a underperforming team member.',
      questionType: 'LEADERSHIP',
      category: 'MANAGEMENT',
      difficulty: 'ADVANCED',
      expectedDuration: 240,
      context:
        'Assesses performance management skills, coaching abilities, and difficult conversations.',
      hints: {
        structure: 'Describe the performance issues, your intervention approach, and the outcome',
        keyPoints: [
          'Performance documentation',
          'Coaching approach',
          'Support provided',
          'Outcome achieved'
        ],
        commonMistakes: ['Avoiding the issue', 'No clear expectations', 'Not providing support']
      },
      followUpQuestions: [
        'How do you set clear expectations?',
        "When do you know it's time to let someone go?"
      ],
      industrySpecific: false,
      tags: ['performance-management', 'coaching', 'difficult-conversations'],
      isRequired: false,
      priority: 3
    },
    {
      questionText: 'Describe your approach to strategic planning and execution.',
      questionType: 'LEADERSHIP',
      category: 'STRATEGY',
      difficulty: 'EXPERT',
      expectedDuration: 300,
      context: 'Tests strategic thinking, planning abilities, and execution skills.',
      hints: {
        structure:
          'Explain planning process, stakeholder involvement, execution strategy, and measurement',
        keyPoints: [
          'Strategic analysis',
          'Goal setting',
          'Resource allocation',
          'Progress tracking'
        ],
        commonMistakes: ['No clear process', 'Lack of stakeholder input', 'Poor execution planning']
      },
      followUpQuestions: [
        'How do you handle changing priorities?',
        'How do you ensure team alignment?'
      ],
      industrySpecific: false,
      tags: ['strategic-planning', 'execution', 'goal-setting'],
      isRequired: false,
      priority: 3
    },
    {
      questionText: 'Tell me about a time you had to lead through a major organizational change.',
      questionType: 'LEADERSHIP',
      category: 'LEADERSHIP',
      difficulty: 'EXPERT',
      expectedDuration: 260,
      context:
        'Evaluates change management skills, communication during uncertainty, and team support.',
      hints: {
        structure: 'Describe the change, your leadership approach, team support, and results',
        keyPoints: [
          'Change communication',
          'Team concerns addressed',
          'Support provided',
          'Adoption achieved'
        ],
        commonMistakes: ['Not acknowledging concerns', 'Poor communication', 'No support structure']
      },
      followUpQuestions: [
        'How do you handle resistance to change?',
        'What would you do differently?'
      ],
      industrySpecific: false,
      tags: ['change-management', 'organizational-change', 'team-support'],
      isRequired: false,
      priority: 3
    },
    {
      questionText: "Describe how you've developed and mentored other leaders on your team.",
      questionType: 'LEADERSHIP',
      category: 'MANAGEMENT',
      difficulty: 'ADVANCED',
      expectedDuration: 220,
      context: 'Tests leadership development skills, mentoring abilities, and succession planning.',
      hints: {
        structure: 'Explain identification process, development approach, and success stories',
        keyPoints: [
          'Talent identification',
          'Development plans',
          'Mentoring approach',
          'Success metrics'
        ],
        commonMistakes: ['No systematic approach', 'Vague development plans', 'No success examples']
      },
      followUpQuestions: [
        'How do you identify leadership potential?',
        "What's your mentoring philosophy?"
      ],
      industrySpecific: false,
      tags: ['leadership-development', 'mentoring', 'succession-planning'],
      isRequired: false,
      priority: 3
    },
    {
      questionText: 'Tell me about a time you had to make an unpopular decision as a leader.',
      questionType: 'LEADERSHIP',
      category: 'LEADERSHIP',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context:
        'Assesses decision-making courage, communication skills, and ability to stand by decisions.',
      hints: {
        structure:
          'Explain the decision context, your reasoning, communication approach, and follow-up',
        keyPoints: [
          'Decision rationale',
          'Communication strategy',
          'Team reaction',
          'Long-term outcome'
        ],
        commonMistakes: ['Not explaining reasoning', 'Poor communication', 'No follow-up']
      },
      followUpQuestions: [
        'How did you maintain team morale?',
        'Would you make the same decision again?'
      ],
      industrySpecific: false,
      tags: ['difficult-decisions', 'leadership-courage', 'communication'],
      isRequired: false,
      priority: 3
    }
  ],

  // Situational and hypothetical questions
  SITUATIONAL_HYPOTHETICAL: [
    {
      questionText:
        'If you discovered a security vulnerability in production code, how would you handle it?',
      questionType: 'SITUATIONAL',
      category: 'ETHICS',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Tests ethical decision-making and crisis management in critical situations.',
      hints: {
        structure: 'Outline immediate actions, communication, and follow-up',
        keyPoints: [
          'Immediate containment',
          'Stakeholder communication',
          'Documentation',
          'Prevention measures'
        ],
        commonMistakes: ['Delaying action', 'Poor communication', 'Not considering impact']
      },
      followUpQuestions: [
        'Who would you notify first?',
        'How would you prevent this in the future?'
      ],
      industrySpecific: true,
      tags: ['security', 'ethics', 'crisis-management'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'How would you approach a project with an unrealistic deadline set by management?',
      questionType: 'SITUATIONAL',
      category: 'COMMUNICATION',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 150,
      context: 'Evaluates communication skills and ability to manage expectations.',
      hints: {
        structure: 'Describe assessment, communication, and negotiation approach',
        keyPoints: [
          'Realistic assessment',
          'Clear communication',
          'Alternative solutions',
          'Stakeholder management'
        ],
        commonMistakes: [
          'Accepting impossible deadlines',
          'Poor communication',
          'Not offering alternatives'
        ]
      },
      followUpQuestions: ['How do you handle pushback?', 'What if they insist on the deadline?'],
      industrySpecific: false,
      tags: ['deadline-management', 'communication', 'negotiation'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'If a team member consistently misses deadlines, how would you address this?',
      questionType: 'SITUATIONAL',
      category: 'MANAGEMENT',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Tests people management and performance improvement skills.',
      hints: {
        structure: 'Show progressive approach from understanding to action',
        keyPoints: [
          'Root cause analysis',
          'Support and resources',
          'Clear expectations',
          'Performance improvement plan'
        ],
        commonMistakes: [
          'Immediate punishment',
          'Not investigating causes',
          'Avoiding the conversation'
        ]
      },
      followUpQuestions: ['What if the behavior continues?', 'How do you prevent this situation?'],
      industrySpecific: false,
      tags: ['performance-management', 'team-support', 'accountability'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        "How would you handle a situation where a client is demanding a feature that you know will compromise the system's security?",
      questionType: 'SITUATIONAL',
      category: 'ETHICS',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context:
        'Tests ethical decision-making, client management, and ability to balance business needs with technical integrity.',
      hints: {
        structure:
          'Acknowledge client needs, explain security risks, propose alternatives, seek compromise',
        keyPoints: [
          'Client empathy',
          'Risk explanation',
          'Alternative solutions',
          'Stakeholder involvement'
        ],
        commonMistakes: ['Immediate refusal', 'Not explaining risks', 'No alternatives offered']
      },
      followUpQuestions: [
        'How would you escalate if the client insists?',
        'What documentation would you create?'
      ],
      industrySpecific: true,
      tags: ['ethics', 'client-management', 'security'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'If you inherited a codebase with no documentation and poor code quality, how would you approach improving it?',
      questionType: 'SITUATIONAL',
      category: 'TECHNICAL_SKILLS',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 220,
      context:
        'Evaluates systematic thinking, prioritization skills, and approach to legacy systems.',
      hints: {
        structure:
          'Assess current state, prioritize improvements, create plan, implement gradually',
        keyPoints: ['Code assessment', 'Risk analysis', 'Improvement roadmap', 'Team involvement'],
        commonMistakes: ['Rewriting everything', 'No assessment phase', 'Working alone']
      },
      followUpQuestions: [
        'How would you get team buy-in?',
        'How would you prioritize improvements?'
      ],
      industrySpecific: true,
      tags: ['legacy-code', 'improvement', 'technical-debt'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        "How would you handle a situation where you disagree with your manager's technical decision?",
      questionType: 'SITUATIONAL',
      category: 'COMMUNICATION',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context:
        'Tests professional communication, conflict resolution, and ability to challenge authority respectfully.',
      hints: {
        structure:
          'Prepare your case, request private discussion, present alternatives, accept decision',
        keyPoints: [
          'Respectful approach',
          'Data-driven arguments',
          'Alternative solutions',
          'Professional acceptance'
        ],
        commonMistakes: [
          'Public disagreement',
          'Emotional arguments',
          'Not accepting final decision'
        ]
      },
      followUpQuestions: [
        "What if your manager doesn't change their mind?",
        'How do you maintain the relationship?'
      ],
      industrySpecific: false,
      tags: ['disagreement', 'manager-relationship', 'professional-communication'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'If you discovered that a feature you built has a critical bug in production affecting thousands of users, what would you do?',
      questionType: 'SITUATIONAL',
      category: 'PROBLEM_SOLVING',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context:
        'Tests crisis management, responsibility taking, and systematic problem-solving under pressure.',
      hints: {
        structure:
          'Immediate containment, stakeholder notification, root cause analysis, prevention measures',
        keyPoints: [
          'Immediate action',
          'Communication plan',
          'Root cause analysis',
          'Prevention strategy'
        ],
        commonMistakes: ['Panic response', 'Poor communication', 'No prevention planning']
      },
      followUpQuestions: [
        'How would you communicate with users?',
        'What processes would you implement to prevent this?'
      ],
      industrySpecific: true,
      tags: ['crisis-management', 'production-issues', 'responsibility'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'How would you approach a project where the requirements are constantly changing?',
      questionType: 'SITUATIONAL',
      category: 'ADAPTABILITY',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Evaluates adaptability, project management skills, and stakeholder management.',
      hints: {
        structure:
          'Establish change process, frequent communication, flexible architecture, expectation management',
        keyPoints: [
          'Change management',
          'Stakeholder communication',
          'Flexible design',
          'Documentation'
        ],
        commonMistakes: ['No change process', 'Poor communication', 'Rigid architecture']
      },
      followUpQuestions: [
        'How would you manage scope creep?',
        'What tools would you use for tracking changes?'
      ],
      industrySpecific: false,
      tags: ['changing-requirements', 'project-management', 'adaptability'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'If you had to choose between meeting a deadline and ensuring code quality, how would you decide?',
      questionType: 'SITUATIONAL',
      category: 'STRATEGY',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context: 'Tests decision-making framework, business understanding, and long-term thinking.',
      hints: {
        structure:
          'Assess risks, consider alternatives, involve stakeholders, make informed decision',
        keyPoints: [
          'Risk assessment',
          'Stakeholder input',
          'Alternative solutions',
          'Long-term impact'
        ],
        commonMistakes: ['Automatic choice', 'Not involving stakeholders', 'No risk analysis']
      },
      followUpQuestions: [
        'How would you communicate the trade-offs?',
        'What factors would influence your decision?'
      ],
      industrySpecific: true,
      tags: ['trade-offs', 'decision-making', 'quality-vs-speed'],
      isRequired: false,
      priority: 2
    }
  ],

  // Advanced communication questions
  COMMUNICATION_ADVANCED: [
    {
      questionText:
        'Describe a time when you had to explain a complex technical concept to a non-technical audience.',
      questionType: 'COMMUNICATION',
      category: 'COMMUNICATION',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Assesses ability to communicate complex ideas clearly and adapt to audience.',
      hints: {
        structure: 'Describe the situation, your approach, and the outcome',
        keyPoints: [
          'Audience analysis',
          'Simplification techniques',
          'Visual aids or analogies',
          'Feedback and understanding'
        ],
        commonMistakes: [
          'Using technical jargon',
          'Not checking understanding',
          'One-way communication'
        ]
      },
      followUpQuestions: [
        'How did you ensure they understood?',
        'What techniques work best for you?'
      ],
      industrySpecific: false,
      tags: ['technical-communication', 'audience-adaptation', 'clarity'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Tell me about a time when you had to give difficult feedback to a colleague or team member.',
      questionType: 'COMMUNICATION',
      category: 'SOFT_SKILLS',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context:
        'Evaluates ability to provide constructive feedback and handle sensitive conversations.',
      hints: {
        structure: 'Use STAR method focusing on communication approach',
        keyPoints: [
          'Preparation and timing',
          'Constructive approach',
          'Specific examples',
          'Follow-up actions'
        ],
        commonMistakes: ['Being too harsh or too soft', 'Vague feedback', 'No follow-up']
      },
      followUpQuestions: ['How did they respond?', 'What did you learn from this experience?'],
      industrySpecific: false,
      tags: ['feedback', 'difficult-conversations', 'interpersonal-skills'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Describe a time when you had to present a complex project to senior executives.',
      questionType: 'COMMUNICATION',
      category: 'COMMUNICATION',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context:
        'Tests executive communication skills, ability to distill complex information, and presentation skills.',
      hints: {
        structure: 'Describe the context, your preparation, presentation approach, and outcome',
        keyPoints: [
          'Audience analysis',
          'Message simplification',
          'Visual aids',
          'Executive engagement'
        ],
        commonMistakes: ['Too much technical detail', 'Poor preparation', 'Not engaging audience']
      },
      followUpQuestions: [
        'How did you prepare for executive questions?',
        'What would you do differently?'
      ],
      industrySpecific: false,
      tags: ['executive-communication', 'presentations', 'complex-topics'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'Tell me about a time you had to communicate bad news to a client or customer.',
      questionType: 'COMMUNICATION',
      category: 'CUSTOMER_SERVICE',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context:
        'Evaluates customer communication skills, empathy, and ability to maintain relationships during difficulties.',
      hints: {
        structure:
          'Explain the situation, your communication approach, customer response, and resolution',
        keyPoints: [
          'Empathy shown',
          'Clear explanation',
          'Solution offered',
          'Relationship maintained'
        ],
        commonMistakes: ['Avoiding responsibility', 'Poor timing', 'No solution offered']
      },
      followUpQuestions: [
        'How did you maintain the customer relationship?',
        'What did you learn from this experience?'
      ],
      industrySpecific: false,
      tags: ['customer-communication', 'bad-news', 'relationship-management'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        "Describe how you've handled miscommunication that led to project delays or issues.",
      questionType: 'COMMUNICATION',
      category: 'PROBLEM_SOLVING',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 200,
      context:
        'Tests communication problem-solving, accountability, and process improvement skills.',
      hints: {
        structure:
          'Explain the miscommunication, impact, resolution steps, and prevention measures',
        keyPoints: [
          'Root cause analysis',
          'Impact assessment',
          'Resolution actions',
          'Process improvements'
        ],
        commonMistakes: ['Blaming others', 'Not taking responsibility', 'No prevention measures']
      },
      followUpQuestions: [
        'How do you prevent miscommunication?',
        'What communication tools do you prefer?'
      ],
      industrySpecific: false,
      tags: ['miscommunication', 'problem-resolution', 'process-improvement'],
      isRequired: false,
      priority: 2
    }
  ],

  // Company culture and values questions
  COMPANY_CULTURE: [
    {
      questionText: 'How do you contribute to a positive team culture?',
      questionType: 'COMPANY_CULTURE',
      category: 'TEAMWORK',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 150,
      context:
        'Assesses cultural fit, team collaboration, and positive influence on workplace environment.',
      hints: {
        structure: 'Give specific examples of cultural contributions and their impact',
        keyPoints: ['Specific actions taken', 'Team impact', 'Cultural values', 'Consistency'],
        commonMistakes: ['Vague answers', 'No specific examples', 'Self-serving actions']
      },
      followUpQuestions: [
        'What does good team culture look like to you?',
        'How do you handle cultural conflicts?'
      ],
      industrySpecific: false,
      tags: ['team-culture', 'positive-influence', 'collaboration'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Describe a time when you had to adapt to a significant change in company culture or values.',
      questionType: 'COMPANY_CULTURE',
      category: 'ADAPTABILITY',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context:
        'Tests adaptability, openness to change, and ability to embrace new cultural directions.',
      hints: {
        structure: 'Explain the change, your initial reaction, adaptation process, and outcome',
        keyPoints: [
          'Change description',
          'Personal adaptation',
          'Support for others',
          'Positive outcome'
        ],
        commonMistakes: ['Resistance to change', 'Not helping others', 'Negative attitude']
      },
      followUpQuestions: [
        'How do you help others adapt to cultural changes?',
        'What makes cultural change successful?'
      ],
      industrySpecific: false,
      tags: ['cultural-change', 'adaptability', 'change-management'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'How do you handle situations where your personal values conflict with a business decision?',
      questionType: 'COMPANY_CULTURE',
      category: 'ETHICS',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context: 'Evaluates ethical decision-making, value alignment, and professional integrity.',
      hints: {
        structure: 'Describe the conflict, your thought process, actions taken, and resolution',
        keyPoints: [
          'Value conflict',
          'Ethical reasoning',
          'Professional approach',
          'Resolution achieved'
        ],
        commonMistakes: [
          'Avoiding the issue',
          'Compromising core values',
          'Unprofessional approach'
        ]
      },
      followUpQuestions: [
        'How do you determine your non-negotiable values?',
        'When would you consider leaving a company?'
      ],
      industrySpecific: false,
      tags: ['values-conflict', 'ethics', 'integrity'],
      isRequired: false,
      priority: 2
    }
  ],

  // Advanced problem-solving questions
  PROBLEM_SOLVING_ADVANCED: [
    {
      questionText:
        'Walk me through your problem-solving process when facing a complex technical challenge.',
      questionType: 'PROBLEM_SOLVING',
      category: 'ANALYTICAL_THINKING',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 200,
      context:
        'Assesses systematic thinking, analytical skills, and structured problem-solving approach.',
      hints: {
        structure: 'Explain your step-by-step process with a specific example',
        keyPoints: [
          'Problem definition',
          'Analysis approach',
          'Solution evaluation',
          'Implementation'
        ],
        commonMistakes: ['No systematic approach', 'Jumping to solutions', 'Not validating results']
      },
      followUpQuestions: [
        "How do you know when you've found the right solution?",
        'What tools help you analyze problems?'
      ],
      industrySpecific: true,
      tags: ['problem-solving', 'analytical-thinking', 'systematic-approach'],
      isRequired: false,
      priority: 2
    },
    {
      questionText:
        'Describe a time when you had to solve a problem with limited resources or budget.',
      questionType: 'PROBLEM_SOLVING',
      category: 'CREATIVITY',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context: 'Tests creativity, resourcefulness, and ability to work within constraints.',
      hints: {
        structure: 'Explain constraints, creative approach, resource optimization, and results',
        keyPoints: [
          'Constraint identification',
          'Creative solutions',
          'Resource optimization',
          'Measurable results'
        ],
        commonMistakes: ['Not showing creativity', 'Ignoring constraints', 'No measurable outcome']
      },
      followUpQuestions: [
        'How do you foster creativity in problem-solving?',
        "What's your approach to working with constraints?"
      ],
      industrySpecific: false,
      tags: ['resource-constraints', 'creativity', 'optimization'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'Tell me about a time you identified a problem that others had missed.',
      questionType: 'PROBLEM_SOLVING',
      category: 'ANALYTICAL_THINKING',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context:
        'Evaluates attention to detail, analytical skills, and proactive problem identification.',
      hints: {
        structure:
          'Describe how you identified the problem, your analysis, and the impact of solving it',
        keyPoints: [
          'Problem identification',
          'Analysis depth',
          'Solution implementation',
          'Impact achieved'
        ],
        commonMistakes: [
          'Not explaining identification process',
          'No impact measurement',
          'Taking all credit'
        ]
      },
      followUpQuestions: [
        'How do you stay alert to potential problems?',
        'How do you validate your problem identification?'
      ],
      industrySpecific: false,
      tags: ['problem-identification', 'analytical-skills', 'proactive-thinking'],
      isRequired: false,
      priority: 2
    }
  ],

  // Customer service and client-facing questions
  CUSTOMER_SERVICE: [
    {
      questionText:
        'Describe a time when you had to deal with an extremely difficult or angry customer.',
      questionType: 'BEHAVIORAL',
      category: 'CUSTOMER_SERVICE',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 200,
      context: 'Tests customer service skills, emotional intelligence, and conflict resolution.',
      hints: {
        structure: 'Use STAR method focusing on de-escalation and resolution',
        keyPoints: [
          'Customer empathy',
          'De-escalation techniques',
          'Solution finding',
          'Relationship preservation'
        ],
        commonMistakes: ['Taking it personally', 'Not listening', 'No follow-up']
      },
      followUpQuestions: [
        'How do you prevent customer escalations?',
        "What's your approach to building customer relationships?"
      ],
      industrySpecific: false,
      tags: ['customer-service', 'conflict-resolution', 'emotional-intelligence'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'Tell me about a time you went above and beyond for a customer.',
      questionType: 'BEHAVIORAL',
      category: 'CUSTOMER_SERVICE',
      difficulty: 'BEGINNER',
      expectedDuration: 180,
      context: 'Evaluates customer focus, initiative, and service excellence mindset.',
      hints: {
        structure: 'Describe the situation, extra effort, and customer impact',
        keyPoints: [
          'Customer need identification',
          'Extra effort taken',
          'Customer satisfaction',
          'Business impact'
        ],
        commonMistakes: ['Self-serving actions', 'No customer impact', 'Vague examples']
      },
      followUpQuestions: [
        'How do you identify opportunities to exceed expectations?',
        'What motivates you to provide excellent service?'
      ],
      industrySpecific: false,
      tags: ['customer-excellence', 'initiative', 'service-mindset'],
      isRequired: false,
      priority: 2
    }
  ],

  // Sales and business development questions
  SALES: [
    {
      questionText: 'Describe your approach to building relationships with potential clients.',
      questionType: 'BEHAVIORAL',
      category: 'SALES',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context:
        'Assesses relationship building skills, sales methodology, and client development approach.',
      hints: {
        structure: 'Explain your systematic approach with specific examples',
        keyPoints: [
          'Research and preparation',
          'Trust building',
          'Value demonstration',
          'Long-term relationship focus'
        ],
        commonMistakes: ['Pushy approach', 'No relationship focus', 'Generic strategies']
      },
      followUpQuestions: [
        'How do you handle rejection?',
        "What's your approach to qualifying prospects?"
      ],
      industrySpecific: false,
      tags: ['relationship-building', 'sales-process', 'client-development'],
      isRequired: false,
      priority: 2
    },
    {
      questionText: 'Tell me about a time you successfully closed a difficult sale.',
      questionType: 'BEHAVIORAL',
      category: 'SALES',
      difficulty: 'ADVANCED',
      expectedDuration: 220,
      context: 'Tests sales skills, persistence, objection handling, and closing techniques.',
      hints: {
        structure:
          'Describe the challenge, your approach, objections handled, and successful outcome',
        keyPoints: [
          'Challenge identification',
          'Strategy development',
          'Objection handling',
          'Closing technique'
        ],
        commonMistakes: ['Not explaining the difficulty', 'No clear strategy', 'Pushy tactics']
      },
      followUpQuestions: [
        'How do you handle price objections?',
        "What's your follow-up strategy after closing?"
      ],
      industrySpecific: false,
      tags: ['sales-closing', 'objection-handling', 'persistence'],
      isRequired: false,
      priority: 2
    }
  ],

  // Strategy and planning questions
  STRATEGY: [
    {
      questionText:
        'Describe a time when you had to develop a long-term strategy for a project or initiative.',
      questionType: 'BEHAVIORAL',
      category: 'STRATEGY',
      difficulty: 'ADVANCED',
      expectedDuration: 240,
      context: 'Evaluates strategic thinking, planning skills, and long-term vision.',
      hints: {
        structure: 'Explain the situation, analysis, strategy development, and implementation',
        keyPoints: [
          'Situation analysis',
          'Strategic options',
          'Decision rationale',
          'Implementation plan'
        ],
        commonMistakes: ['No clear analysis', 'Vague strategy', 'No implementation details']
      },
      followUpQuestions: [
        'How do you validate strategic assumptions?',
        'How do you adapt strategy when circumstances change?'
      ],
      industrySpecific: false,
      tags: ['strategic-thinking', 'planning', 'long-term-vision'],
      isRequired: false,
      priority: 3
    }
  ],

  // Ethics and integrity questions
  ETHICS: [
    {
      questionText:
        'Describe a situation where you had to make a decision between what was easy and what was right.',
      questionType: 'BEHAVIORAL',
      category: 'ETHICS',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context: 'Tests ethical decision-making, integrity, and moral courage.',
      hints: {
        structure: 'Explain the dilemma, your thought process, decision, and outcome',
        keyPoints: ['Ethical dilemma', 'Decision framework', 'Courage shown', 'Positive outcome'],
        commonMistakes: [
          'Avoiding difficult decisions',
          'No clear ethical framework',
          'Self-serving choices'
        ]
      },
      followUpQuestions: [
        'How do you handle ethical gray areas?',
        'What guides your ethical decision-making?'
      ],
      industrySpecific: false,
      tags: ['ethics', 'integrity', 'moral-courage'],
      isRequired: false,
      priority: 2
    }
  ],

  // Teamwork and collaboration questions
  TEAMWORK: [
    {
      questionText:
        'Tell me about a time when you had to work with a team member whose working style was very different from yours.',
      questionType: 'BEHAVIORAL',
      category: 'TEAMWORK',
      difficulty: 'INTERMEDIATE',
      expectedDuration: 180,
      context:
        'Assesses adaptability, collaboration skills, and ability to work with diverse personalities.',
      hints: {
        structure: 'Describe the differences, your adaptation, and successful collaboration',
        keyPoints: [
          'Style differences',
          'Adaptation approach',
          'Communication strategies',
          'Successful outcome'
        ],
        commonMistakes: ['Not adapting', 'Criticizing their style', 'No positive outcome']
      },
      followUpQuestions: [
        'How do you identify different working styles?',
        "What's your approach to team dynamics?"
      ],
      industrySpecific: false,
      tags: ['collaboration', 'adaptability', 'team-dynamics'],
      isRequired: false,
      priority: 2
    }
  ],

  // Management and supervision questions
  MANAGEMENT: [
    {
      questionText: 'Describe your approach to managing team performance and providing feedback.',
      questionType: 'BEHAVIORAL',
      category: 'MANAGEMENT',
      difficulty: 'ADVANCED',
      expectedDuration: 200,
      context:
        'Evaluates management skills, feedback delivery, and performance improvement strategies.',
      hints: {
        structure: 'Explain your systematic approach with specific examples',
        keyPoints: [
          'Performance monitoring',
          'Feedback delivery',
          'Development planning',
          'Results achieved'
        ],
        commonMistakes: [
          'No systematic approach',
          'Avoiding difficult conversations',
          'No development focus'
        ]
      },
      followUpQuestions: [
        'How do you handle underperformance?',
        "What's your approach to team development?"
      ],
      industrySpecific: false,
      tags: ['performance-management', 'feedback', 'team-development'],
      isRequired: false,
      priority: 3
    }
  ],

  // Analytical thinking questions
  ANALYTICAL_THINKING: [
    {
      questionText:
        'Walk me through how you would analyze a complex business problem with multiple variables.',
      questionType: 'BEHAVIORAL',
      category: 'ANALYTICAL_THINKING',
      difficulty: 'ADVANCED',
      expectedDuration: 220,
      context: 'Tests analytical skills, systematic thinking, and problem decomposition abilities.',
      hints: {
        structure: 'Describe your analytical framework and methodology',
        keyPoints: [
          'Problem decomposition',
          'Data gathering',
          'Analysis approach',
          'Solution validation'
        ],
        commonMistakes: [
          'No systematic approach',
          'Jumping to conclusions',
          'Not validating assumptions'
        ]
      },
      followUpQuestions: [
        'How do you handle incomplete data?',
        'What tools do you use for analysis?'
      ],
      industrySpecific: false,
      tags: ['analytical-skills', 'systematic-thinking', 'problem-decomposition'],
      isRequired: false,
      priority: 2
    }
  ]
};

// Enhanced smart question selection algorithm
function selectQuestionsForContext(
  sessionConfig: any,
  count: number,
  difficulty: string = 'INTERMEDIATE',
  EnhancedQuestionGenerator: any
) {
  try {
    // Use the enhanced question generator for sophisticated selection
    const context = {
      sessionType: sessionConfig.sessionType,
      interviewType: sessionConfig.interviewType,
      careerPath: sessionConfig.careerPath,
      experienceLevel: sessionConfig.experienceLevel,
      specificRole: sessionConfig.specificRole,
      companyType: sessionConfig.companyType,
      industryFocus: sessionConfig.industryFocus,
      focusAreas: sessionConfig.focusAreas || [],
      difficulty,
      count
    };

    const selectedQuestions = EnhancedQuestionGenerator.generateQuestions(QUESTION_BANKS, context);

    if (selectedQuestions.length > 0) {
      console.log(
        `Enhanced generator selected ${selectedQuestions.length} questions for context:`,
        {
          sessionType: context.sessionType,
          interviewType: context.interviewType,
          focusAreas: context.focusAreas,
          difficulty
        }
      );
      return selectedQuestions;
    }
  } catch (error) {
    console.error('Enhanced question generator failed, falling back to legacy algorithm:', error);
  }

  // Fallback to legacy algorithm if enhanced generator fails
  return selectQuestionsForContextLegacy(sessionConfig, count, difficulty);
}

// Legacy question selection algorithm (kept as fallback)
function selectQuestionsForContextLegacy(
  sessionConfig: any,
  count: number,
  difficulty: string = 'INTERMEDIATE'
) {
  const { sessionType, interviewType, focusAreas = [], careerPath, specificRole } = sessionConfig;

  let selectedQuestions: any[] = [];
  let availableQuestions: any[] = [];

  // Always include core behavioral questions (high priority)
  availableQuestions.push(...QUESTION_BANKS.BEHAVIORAL_CORE);

  // Add questions based on session type
  if (
    sessionType === 'TECHNICAL_PRACTICE' ||
    focusAreas.includes('technical') ||
    focusAreas.includes('Technical Skills')
  ) {
    availableQuestions.push(...QUESTION_BANKS.TECHNICAL);
  }

  if (
    sessionType === 'BEHAVIORAL_PRACTICE' ||
    focusAreas.includes('behavioral') ||
    focusAreas.includes('Behavioral Questions')
  ) {
    // Behavioral core already added, but we can add more behavioral-focused questions
    availableQuestions.push(...QUESTION_BANKS.COMMUNICATION);
  }

  // Add questions based on interview type
  if (interviewType === 'PANEL' || interviewType === 'GROUP') {
    availableQuestions.push(...QUESTION_BANKS.COMMUNICATION);
  }

  if (interviewType === 'TECHNICAL_SCREEN') {
    availableQuestions.push(...QUESTION_BANKS.TECHNICAL);
  }

  // Add questions based on focus areas
  if (focusAreas.includes('leadership') || focusAreas.includes('Leadership')) {
    availableQuestions.push(...QUESTION_BANKS.LEADERSHIP);
  }

  if (focusAreas.includes('communication') || focusAreas.includes('Communication')) {
    availableQuestions.push(...QUESTION_BANKS.COMMUNICATION);
  }

  if (focusAreas.includes('problem-solving') || focusAreas.includes('Problem Solving')) {
    availableQuestions.push(...QUESTION_BANKS.SITUATIONAL_SCENARIOS);
    availableQuestions.push(...QUESTION_BANKS.PROBLEM_SOLVING);
  }

  // Add questions based on company culture focus
  if (
    focusAreas.includes('culture') ||
    focusAreas.includes('Cultural Fit') ||
    focusAreas.includes('company-culture')
  ) {
    availableQuestions.push(...QUESTION_BANKS.COMPANY_CULTURE);
  }

  // Add questions based on career path and role
  if (
    careerPath &&
    (careerPath.toLowerCase().includes('manager') || careerPath.toLowerCase().includes('lead'))
  ) {
    availableQuestions.push(...QUESTION_BANKS.LEADERSHIP);
  }

  if (
    specificRole &&
    (specificRole.toLowerCase().includes('senior') ||
      specificRole.toLowerCase().includes('principal'))
  ) {
    availableQuestions.push(...QUESTION_BANKS.LEADERSHIP);
    availableQuestions.push(...QUESTION_BANKS.PROBLEM_SOLVING);
  }

  // Remove duplicates based on questionText
  const uniqueQuestions = availableQuestions.filter(
    (question, index, self) =>
      index === self.findIndex(q => q.questionText === question.questionText)
  );

  // Sort by priority (lower number = higher priority) and difficulty match
  const sortedQuestions = uniqueQuestions.sort((a, b) => {
    // First sort by priority
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    }
    // Then by difficulty match
    const aDiffMatch = a.difficulty === difficulty ? 0 : 1;
    const bDiffMatch = b.difficulty === difficulty ? 0 : 1;
    return aDiffMatch - bDiffMatch;
  });

  // Select questions ensuring we have required ones first
  const requiredQuestions = sortedQuestions.filter(q => q.isRequired);
  const optionalQuestions = sortedQuestions.filter(q => !q.isRequired);

  // Start with required questions
  selectedQuestions.push(...requiredQuestions.slice(0, Math.min(count, requiredQuestions.length)));

  // Fill remaining slots with optional questions
  const remainingSlots = count - selectedQuestions.length;
  if (remainingSlots > 0) {
    selectedQuestions.push(...optionalQuestions.slice(0, remainingSlots));
  }

  // If we still don't have enough questions, cycle through available questions
  while (selectedQuestions.length < count && uniqueQuestions.length > 0) {
    const index = selectedQuestions.length % uniqueQuestions.length;
    const question = { ...uniqueQuestions[index] };

    // Avoid exact duplicates
    if (!selectedQuestions.some(q => q.questionText === question.questionText)) {
      selectedQuestions.push(question);
    } else {
      break; // Prevent infinite loop if we've used all unique questions
    }
  }

  // Adjust difficulty for all selected questions
  return selectedQuestions.map(question => ({
    ...question,
    difficulty: difficulty
  }));
}

// Legacy fallback function for backward compatibility
function generateFallbackQuestions(count: number, difficulty: string = 'INTERMEDIATE') {
  // Use the basic behavioral core questions for simple fallback
  const coreQuestions = QUESTION_BANKS.BEHAVIORAL_CORE;

  const selectedQuestions = [];
  for (let i = 0; i < count; i++) {
    const question = { ...coreQuestions[i % coreQuestions.length] };
    question.difficulty = difficulty;
    selectedQuestions.push(question);
  }

  return selectedQuestions;
}

// Enhanced fallback function that uses session context
function generateContextualFallbackQuestions(
  sessionConfig: any,
  count: number,
  difficulty: string = 'INTERMEDIATE',
  EnhancedQuestionGenerator: any
) {
  try {
    return selectQuestionsForContext(sessionConfig, count, difficulty, EnhancedQuestionGenerator);
  } catch (error) {
    console.error(
      'Error in contextual question selection, falling back to basic questions:',
      error
    );
    return generateFallbackQuestions(count, difficulty);
  }
}

// Validation schema for generating questions
const generateQuestionsSchema = z.object({
  count: z.number().min(1).max(20).default(10),
  questionTypes: z
    .array(
      z.enum([
        'BEHAVIORAL',
        'TECHNICAL',
        'SITUATIONAL',
        'COMPANY_CULTURE',
        'LEADERSHIP',
        'PROBLEM_SOLVING',
        'COMMUNICATION',
        'STRESS_TEST',
        'CASE_STUDY',
        'ROLE_SPECIFIC'
      ])
    )
    .optional(),
  categories: z
    .array(
      z.enum([
        'GENERAL',
        'TECHNICAL_SKILLS',
        'SOFT_SKILLS',
        'LEADERSHIP',
        'PROBLEM_SOLVING',
        'COMMUNICATION',
        'TEAMWORK',
        'ADAPTABILITY',
        'CREATIVITY',
        'ANALYTICAL_THINKING',
        'CUSTOMER_SERVICE',
        'SALES',
        'MANAGEMENT',
        'STRATEGY',
        'ETHICS',
        'INDUSTRY_KNOWLEDGE'
      ])
    )
    .optional(),
  difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional()
});

// GET - Retrieve questions for a session
export async function GET(request: NextRequest, { params }: { params: Promise<{ sessionId: string }> }): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      prisma,
      { withRateLimit, rateLimiters }
    ] = await Promise.all([
      import('@/lib/prisma').then(m => m.default),
      import('@/lib/rate-limit')
    ]);

    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      const error = new Error('Authentication required');
      (error as any).statusCode = 401;
      throw error;
    }

    const userId = session.user.id;
    const { sessionId } = await params;

    // Verify session ownership
    const interviewSession = await prisma.interviewSession.findFirst({
      where: {
        id: sessionId,
        userId
      }
    });

    if (!interviewSession) {
      const error = new Error('Interview session not found');
      (error as any).statusCode = 404;
      throw error;
    }

    // Get questions with user responses
    const questions = await prisma.interviewQuestion.findMany({
      where: { sessionId },
      include: {
        responses: {
          where: { userId },
          select: {
            id: true,
            responseText: true,
            audioUrl: true,
            responseTime: true,
            preparationTime: true,
            aiScore: true,
            isCompleted: true,
            userNotes: true,
            createdAt: true
          }
        }
      },
      orderBy: { questionOrder: 'asc' }
    });

    return NextResponse.json({
      success: true,
      data: questions
    });
  } catch (error: any) {
    console.error('Interview questions GET error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}

// POST - Generate questions for a session using AI
export async function POST(request: NextRequest, { params }: { params: Promise<{ sessionId: string }> }): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      prisma,
      { withRateLimit, rateLimiters },
      { withCSRFProtection },
      { SelfHealingAIService },
      { UnifiedValidationService },
      EnhancedQuestionGenerator
    ] = await Promise.all([
      import('@/lib/prisma').then(m => m.default),
      import('@/lib/rate-limit'),
      import('@/lib/csrf'),
      import('@/lib/self-healing-ai-service'),
      import('@/lib/unified-validation-service'),
      import('@/lib/enhanced-question-generator').then(m => m.default)
    ]);

    return withCSRFProtection(request, async () => {
      // Apply rate limiting
      const rateLimitResult = withRateLimit(rateLimiters.api)(request);
      if (!rateLimitResult.allowed) {
        const error = new Error('Too many requests') as any;
        error.statusCode = 429;
        throw error;
      }
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required');
        (error as any).statusCode = 401;
        throw error;
      }

      const userId = session.user.id;
      const { sessionId } = await params;

      const body = await request.json();
      const validation = generateQuestionsSchema.safeParse(body);

      if (!validation.success) {
        const error = new Error('Invalid request data');
        (error as any).statusCode = 400;
        (error as any).details = validation.error.errors;
        throw error;
      }

      const { count, questionTypes, categories, difficulty } = validation.data;

      // Verify session ownership and get session details
      const interviewSession = await prisma.interviewSession.findFirst({
        where: {
          id: sessionId,
          userId
        }
      });

      if (!interviewSession) {
        const error = new Error('Interview session not found');
        (error as any).statusCode = 404;
        throw error;
      }

      // Check if questions already exist
      const existingQuestions = await prisma.interviewQuestion.findMany({
        where: { sessionId },
        orderBy: { questionOrder: 'asc' }
      });

      if (existingQuestions.length > 0) {
        return NextResponse.json({
          success: true,
          data: {
            questions: existingQuestions,
            metadata: {
              source: 'existing',
              reason: 'Questions already generated for this session',
              timestamp: new Date().toISOString()
            }
          },
          message: `Retrieved ${existingQuestions.length} existing interview questions`
        });
      }

      // Generate questions using self-healing AI service with aggressive fallback
      const aiResult = await SelfHealingAIService.generateInterviewQuestions(
        {
          sessionType: interviewSession.sessionType,
          careerPath: interviewSession.careerPath || undefined,
          experienceLevel: interviewSession.experienceLevel || undefined,
          companyType: interviewSession.companyType || undefined,
          industryFocus: interviewSession.industryFocus || undefined,
          specificRole: interviewSession.specificRole || undefined,
          interviewType: interviewSession.interviewType || undefined,
          focusAreas: interviewSession.focusAreas,
          difficulty: difficulty || interviewSession.difficulty,
          questionTypes,
          categories,
          count,
          totalQuestions: count
        },
        {
          timeout: 120000, // 120 second timeout for AI generation
          maxRetries: 2, // Allow 2 retries for better reliability
          fallbackToStatic: true,
          circuitBreakerThreshold: 3, // Allow more attempts before circuit breaker
          circuitBreakerTimeout: 60000 // 60 second circuit timeout
        }
      );

      // Transform AI result to expected format
      const questionsResult = {
        success: aiResult.success,
        data: aiResult.success
          ? {
              questions: aiResult.data?.questions || [],
              metadata: {
                source: aiResult.source,
                responseTime: aiResult.responseTime,
                retryCount: aiResult.retryCount,
                timestamp: new Date().toISOString(),
                sessionContext: {
                  sessionType: interviewSession.sessionType,
                  interviewType: interviewSession.interviewType,
                  focusAreas: interviewSession.focusAreas
                }
              }
            }
          : null,
        error: aiResult.error
      };

      // Self-healing service handles all fallbacks internally
      if (
        !questionsResult.success ||
        !questionsResult.data?.questions ||
        questionsResult.data.questions.length === 0
      ) {
        // Final fallback if everything fails
        console.log('All AI services failed, using final contextual fallback questions');
        questionsResult.data = {
          questions: generateContextualFallbackQuestions(
            interviewSession,
            count,
            difficulty || interviewSession.difficulty,
            EnhancedQuestionGenerator
          ),
          metadata: {
            source: 'contextual_fallback' as any,
            responseTime: 0,
            retryCount: 0,
            timestamp: new Date().toISOString(),
            sessionContext: {
              sessionType: interviewSession.sessionType,
              interviewType: interviewSession.interviewType,
              focusAreas: interviewSession.focusAreas
            }
          }
        };
      }

      // Use unified validation service for enum validation

      // Save generated questions to database using unified validation service
      const questionsData = questionsResult.data.questions.map((q: any, index: number) => ({
        sessionId,
        questionText: q.questionText,
        questionType: UnifiedValidationService.validateQuestionType(q.questionType),
        category: UnifiedValidationService.validateCategory(q.category),
        difficulty: UnifiedValidationService.validateDifficulty(
          q.difficulty || difficulty || interviewSession.difficulty
        ),
        expectedDuration: q.expectedDuration || 180,
        context: q.context,
        hints: q.hints,
        followUpQuestions: q.followUpQuestions,
        industrySpecific: q.industrySpecific || false,
        questionOrder: index + 1,
        isRequired: q.isRequired !== false,
        tags: q.tags
      }));

      await prisma.interviewQuestion.createMany({
        data: questionsData
      });

      // Update session with total questions count
      await prisma.interviewSession.update({
        where: { id: sessionId },
        data: {
          totalQuestions: questionsData.length,
          lastActiveAt: new Date()
        }
      });

      // Fetch the created questions with full details
      const questions = await prisma.interviewQuestion.findMany({
        where: { sessionId },
        orderBy: { questionOrder: 'asc' }
      });

      return NextResponse.json({
        success: true,
        data: {
          questions,
          metadata: questionsResult.data.metadata
        },
        message: `Generated ${questions.length} interview questions successfully`
      });
    }) as Promise<NextResponse<any>>;
  } catch (error: any) {
    console.error('Interview questions POST error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}
