import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const revalidate = false;

// Validation schema for updating responses
const updateResponseSchema = z.object({
  responseText: z.string().min(10).max(5000).optional(),
  audioUrl: z.string().url().optional(),
  videoUrl: z.string().url().optional(),
  responseTime: z.number().min(0).max(3600).optional(),
  preparationTime: z.number().min(0).max(1800).optional(),
  userNotes: z.string().max(1000).optional(),
  needsReview: z.boolean().optional(),
  requestNewFeedback: z.boolean().default(false)
});

// GET - Retrieve specific response
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string; responseId: string }> }
): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      prisma,
      { withRateLimit, rateLimiters }
    ] = await Promise.all([
      import('@/lib/prisma').then(m => m.default),
      import('@/lib/rate-limit')
    ]);

    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      const error = new Error('Authentication required') as any;
      error.statusCode = 401;
      throw error;
    }

    const userId = session.user.id;
    const { sessionId, responseId } = await params;

    const response = await prisma.interviewResponse.findFirst({
      where: {
        id: responseId,
        sessionId,
        userId
      },
      include: {
        question: {
          select: {
            id: true,
            questionText: true,
            questionType: true,
            category: true,
            difficulty: true,
            expectedDuration: true,
            context: true,
            hints: true,
            followUpQuestions: true,
            questionOrder: true
          }
        },
        session: {
          select: {
            id: true,
            sessionType: true,
            careerPath: true,
            experienceLevel: true,
            status: true
          }
        }
      }
    });

    if (!response) {
      const error = new Error('Response not found') as any;
      error.statusCode = 404;
      throw error;
    }

    return NextResponse.json({
      success: true,
      data: response
    });
  } catch (error: any) {
    console.error('Interview response GET error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}

// PATCH - Update specific response
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string; responseId: string }> }
): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      prisma,
      { withRateLimit, rateLimiters },
      { geminiService }
    ] = await Promise.all([
      import('@/lib/prisma').then(m => m.default),
      import('@/lib/rate-limit'),
      import('@/lib/services/geminiService')
    ]);

    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      const error = new Error('Authentication required') as any;
      error.statusCode = 401;
      throw error;
    }

    const userId = session.user.id;
    const { sessionId, responseId } = await params;

    const body = await request.json();
    const validation = updateResponseSchema.safeParse(body);

    if (!validation.success) {
      const error = new Error('Invalid request data') as any;
      error.statusCode = 400;
      error.details = validation.error.errors;
      throw error;
    }

    const updateData = validation.data;

    // Verify response ownership
    const existingResponse = await prisma.interviewResponse.findFirst({
      where: {
        id: responseId,
        sessionId,
        userId
      },
      include: {
        question: true,
        session: true
      }
    });

    if (!existingResponse) {
      const error = new Error('Response not found') as any;
      error.statusCode = 404;
      throw error;
    }

    // Prepare update data
    const { requestNewFeedback, ...responseUpdateData } = updateData;

    // Update the response
    let updatedResponse = await prisma.interviewResponse.update({
      where: { id: responseId },
      data: {
        ...responseUpdateData,
        updatedAt: new Date()
      },
      include: {
        question: {
          select: {
            id: true,
            questionText: true,
            questionType: true,
            category: true,
            difficulty: true,
            expectedDuration: true,
            context: true,
            hints: true,
            followUpQuestions: true,
            questionOrder: true
          }
        },
        session: {
          select: {
            id: true,
            sessionType: true,
            careerPath: true,
            experienceLevel: true,
            status: true
          }
        }
      }
    });

    // Generate new AI feedback if requested
    if (requestNewFeedback && updateData.responseText) {
      try {
        const feedbackResult = await geminiService.analyzeInterviewResponse({
          questionText: existingResponse.question.questionText,
          questionType: existingResponse.question.questionType,
          questionCategory: existingResponse.question.category,
          responseText: updateData.responseText,
          responseTime: updateData.responseTime || existingResponse.responseTime,
          expectedDuration: existingResponse.question.expectedDuration,
          careerPath: existingResponse.session.careerPath || undefined,
          experienceLevel: existingResponse.session.experienceLevel || undefined,
          context: existingResponse.question.context || undefined
        });

        if (feedbackResult.success) {
          // Update response with new AI analysis
          updatedResponse = await prisma.interviewResponse.update({
            where: { id: responseId },
            data: {
              aiScore: feedbackResult.data.overallScore,
              aiAnalysis: feedbackResult.data.analysis,
              feedback: feedbackResult.data.feedback,
              strengths: feedbackResult.data.strengths,
              improvements: feedbackResult.data.improvements,
              starMethodScore: feedbackResult.data.starMethodScore,
              confidenceLevel: feedbackResult.data.confidenceLevel,
              communicationScore: feedbackResult.data.communicationScore,
              technicalScore: feedbackResult.data.technicalScore
            },
            include: {
              question: {
                select: {
                  id: true,
                  questionText: true,
                  questionType: true,
                  category: true,
                  difficulty: true,
                  expectedDuration: true,
                  context: true,
                  hints: true,
                  followUpQuestions: true,
                  questionOrder: true
                }
              },
              session: {
                select: {
                  id: true,
                  sessionType: true,
                  careerPath: true,
                  experienceLevel: true,
                  status: true
                }
              }
            }
          });
        }
      } catch (feedbackError) {
        console.error('Error generating new AI feedback:', feedbackError);
        // Continue without new feedback - don't fail the update
      }
    }

    // Update session last active time
    await prisma.interviewSession.update({
      where: { id: sessionId },
      data: { lastActiveAt: new Date() }
    });

    return NextResponse.json({
      success: true,
      data: updatedResponse,
      message: 'Response updated successfully'
    });
  } catch (error: any) {
    console.error('Interview response PATCH error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}

// DELETE - Delete specific response
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string; responseId: string }> }
): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      prisma,
      { withRateLimit, rateLimiters }
    ] = await Promise.all([
      import('@/lib/prisma').then(m => m.default),
      import('@/lib/rate-limit')
    ]);

    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      const error = new Error('Authentication required') as any;
      error.statusCode = 401;
      throw error;
    }

    const userId = session.user.id;
    const { sessionId, responseId } = await params;

    // Verify response ownership
    const existingResponse = await prisma.interviewResponse.findFirst({
      where: {
        id: responseId,
        sessionId,
        userId
      }
    });

    if (!existingResponse) {
      const error = new Error('Response not found') as any;
      error.statusCode = 404;
      throw error;
    }

    // Delete the response
    await prisma.interviewResponse.delete({
      where: { id: responseId }
    });

    // Update session completed questions count
    const completedResponses = await prisma.interviewResponse.count({
      where: {
        sessionId,
        userId,
        isCompleted: true
      }
    });

    await prisma.interviewSession.update({
      where: { id: sessionId },
      data: {
        completedQuestions: completedResponses,
        lastActiveAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Response deleted successfully'
    });
  } catch (error: any) {
    console.error('Interview response DELETE error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}
