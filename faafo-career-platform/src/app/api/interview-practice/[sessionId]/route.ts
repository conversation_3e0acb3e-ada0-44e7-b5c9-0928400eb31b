import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { UnifiedAuthenticationService } from '@/lib/unified-authentication-service';
import prisma from '@/lib/prisma';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';

// Validation schema for updating session
const updateSessionSchema = z.object({
  status: z.enum(['IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABANDONED']).optional(),
  timeSpent: z.number().min(0).optional(),
  overallScore: z.number().min(0).max(10).optional(),
  aiInsights: z
    .object({
      strengths: z.array(z.string()).optional(),
      improvements: z.array(z.string()).optional(),
      overallFeedback: z.string().max(2000).optional(),
      score: z.number().min(0).max(10).optional(),
      recommendations: z.array(z.string()).optional()
    })
    .optional()
});

// GET - Retrieve specific interview session
export const GET = withUnifiedErrorHandling(
  async (request: NextRequest, { params }: { params: Promise<{ sessionId: string }> }) => {
    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const { sessionId } = await params;

    // Enhanced user validation using unified service
    const validation = await UnifiedAuthenticationService.validateSession(request, {
      validateUserExists: true,
      checkAccountLock: true,
      refreshSession: true,
      enableSecurityLogging: true
    });

    if (!validation.isValid) {
      const error = new Error(validation.error);
      (error as any).statusCode = validation.statusCode || 401;
      throw error;
    }

    // Temporarily skip session access validation to isolate the issue
    // const sessionValidation = await UnifiedAuthenticationService.validateSessionAccess(
    //   request,
    //   sessionId,
    //   { sessionType: 'interview' }
    // );

    // if (!sessionValidation.isValid) {
    //   const error = new Error(sessionValidation.error);
    //   (error as any).statusCode = sessionValidation.statusCode || 404;
    //   throw error;
    // }

    // Simplified query to avoid complex include issues
    const interviewSession = await prisma.interviewSession.findFirst({
      where: {
        id: sessionId,
        userId: validation.userId
      }
    });

    if (!interviewSession) {
      throw new Error('Interview session not found');
    }

    // For now, return basic session data without complex progress calculation
    return NextResponse.json({
      success: true,
      data: {
        ...interviewSession,
        progress: {
          completed: interviewSession.completedQuestions || 0,
          total: interviewSession.totalQuestions || 0,
          percentage: interviewSession.totalQuestions > 0
            ? Math.round(((interviewSession.completedQuestions || 0) / interviewSession.totalQuestions) * 100)
            : 0
        }
      }
    });
  }
);

// PATCH - Update interview session
export const PATCH = withUnifiedErrorHandling(
  async (request: NextRequest, { params }: { params: Promise<{ sessionId: string }> }) => {
    return withCSRFProtection(request, async () => {
      // Apply rate limiting
      const rateLimitResult = withRateLimit(rateLimiters.api)(request);
      if (!rateLimitResult.allowed) {
        const error = new Error('Too many requests') as any;
        error.statusCode = 429;
        throw error;
      }
      const { sessionId } = await params;

      // Enhanced session validation using unified service
      const sessionValidation = await UnifiedAuthenticationService.validateSessionAccess(
        request,
        sessionId,
        { sessionType: 'interview' }
      );

      if (!sessionValidation.isValid) {
        const error = new Error(sessionValidation.error);
        (error as any).statusCode = sessionValidation.statusCode || 400;
        throw error;
      }

      const body = await request.json();
      const validation = updateSessionSchema.safeParse(body);

      if (!validation.success) {
        const error = new Error('Invalid request data');
        (error as any).statusCode = 400;
        (error as any).details = validation.error.errors;
        throw error;
      }

      const updateData = validation.data;

      // Validate status transition if status is being updated
      if (updateData.status) {
        const currentSession = await prisma.interviewSession.findUnique({
          where: { id: sessionId },
          select: { status: true }
        });

        if (currentSession) {
          const transitionValidation = UnifiedAuthenticationService.validateSessionStateTransition(
            currentSession.status,
            updateData.status,
            'interview'
          );

          if (!transitionValidation.isValid) {
            const error = new Error(transitionValidation.error);
            (error as any).statusCode = 400;
            throw error;
          }
        }
      }

      // Update session
      const updatedSession = await prisma.interviewSession.update({
        where: { id: sessionId },
        data: {
          ...updateData,
          lastActiveAt: new Date(),
          ...(updateData.status === 'COMPLETED' ? { completedAt: new Date() } : {})
        },
        include: {
          questions: {
            select: {
              id: true,
              questionType: true,
              category: true,
              difficulty: true,
              questionOrder: true
            },
            orderBy: { questionOrder: 'asc' }
          },
          responses: {
            where: { userId: sessionValidation.userId },
            select: {
              id: true,
              questionId: true,
              isCompleted: true,
              aiScore: true
            }
          }
        }
      });

      return NextResponse.json({
        success: true,
        data: updatedSession,
        message: 'Interview session updated successfully'
      });
    });
  }
);

// DELETE - Delete interview session
export const DELETE = withUnifiedErrorHandling(
  async (request: NextRequest, { params }: { params: Promise<{ sessionId: string }> }) => {
    return withCSRFProtection(request, async () => {
      // Apply rate limiting
      const rateLimitResult = withRateLimit(rateLimiters.api)(request);
      if (!rateLimitResult.allowed) {
        const error = new Error('Too many requests') as any;
        error.statusCode = 429;
        throw error;
      }
      const { sessionId } = await params;

      // Enhanced session validation using unified service
      const validation = await UnifiedAuthenticationService.validateSessionAccess(
        request,
        sessionId,
        { sessionType: 'interview' }
      );

      if (!validation.isValid) {
        const error = new Error(validation.error);
        (error as any).statusCode = validation.statusCode || 400;
        throw error;
      }

      // Delete session (cascade will handle questions and responses)
      await prisma.interviewSession.delete({
        where: { id: sessionId }
      });

      return NextResponse.json({
        success: true,
        message: 'Interview session deleted successfully'
      });
    });
  }
);
