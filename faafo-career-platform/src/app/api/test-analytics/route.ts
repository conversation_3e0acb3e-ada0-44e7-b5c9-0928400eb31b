import { NextResponse } from 'next/server';

import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';

// Test importing analytics service
let analyticsServiceError: any = null;
try {
  const { analyticsService: _analyticsService } = require('@/lib/analytics-service');
  console.log('Analytics service imported successfully');
} catch (error) {
  analyticsServiceError = error;
  console.error('Failed to import analytics service:', error);
}

export const GET = withUnifiedErrorHandling(async () => {
  // Test basic analytics queries one by one
  console.log('Testing basic user count...');
  const userCount = await prisma.user.count();
  console.log('User count:', userCount);

  console.log('Testing learning resource count...');
  const resourceCount = await prisma.learningResource.count();
  console.log('Resource count:', resourceCount);

  console.log('Testing forum post count...');
  const postCount = await prisma.forumPost.count();
  console.log('Post count:', postCount);

  console.log('Testing career path count...');
  const careerPathCount = await prisma.careerPath.count();
  console.log('Career path count:', careerPathCount);

  return NextResponse.json({
    message: 'Basic analytics queries successful',
    analyticsServiceError: analyticsServiceError ? analyticsServiceError.message : null,
    userCount,
    resourceCount,
    postCount,
    careerPathCount
  });
});
