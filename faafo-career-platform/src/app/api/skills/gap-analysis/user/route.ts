// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import prisma from '@/lib/prisma';

interface UserGapAnalysesResponse {
  success: boolean;
  data: {
    analyses: Array<{
      id: string;
      targetCareerPath: string;
      status: string;
      completionPercentage: number;
      createdAt: string;
      lastUpdated: string;
      expiresAt: string;
    }>;
    activeAnalysis?: {
      id: string;
      skillGaps: number;
      completedMilestones: number;
      totalMilestones: number;
      nextMilestone: {
        skills: string[];
        dueDate: string;
      };
    };
  };
}

async function handleGetUserGapAnalyses(_request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const userId = session.user.id;
  // Get all gap analyses for the user
  const analyses = await prisma.skillGapAnalysis.findMany({
    where: {
      userId
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  const analysesData = analyses.map(analysis => ({
    id: analysis.id,
    targetCareerPath: analysis.targetCareerPathName,
    status: analysis.status,
    completionPercentage: analysis.completionPercentage,
    createdAt: analysis.createdAt.toISOString(),
    lastUpdated: analysis.lastUpdated.toISOString(),
    expiresAt: analysis.expiresAt.toISOString()
  }));

  // Find active analysis
  const activeAnalysis = analyses.find(a => a.status === 'ACTIVE');
  let activeAnalysisData;

  if (activeAnalysis) {
    const progressTracking = activeAnalysis.progressTracking as any;
    const milestones = progressTracking?.milestones || [];
    const completedMilestones = progressTracking?.completedMilestones || [];
    const skillGaps = Array.isArray(activeAnalysis.skillGaps) ? activeAnalysis.skillGaps.length : 0;

    // Find next milestone
    const nextMilestone = milestones.find((m: any) => !completedMilestones.includes(m.month));

    activeAnalysisData = {
      id: activeAnalysis.id,
      skillGaps,
      completedMilestones: completedMilestones.length,
      totalMilestones: milestones.length,
      nextMilestone: nextMilestone
        ? {
            skills: nextMilestone.skills || [],
            dueDate: calculateMilestoneDueDate(activeAnalysis.createdAt, nextMilestone.month)
          }
        : {
            skills: [],
            dueDate: new Date().toISOString()
          }
    };
  }

  const responseData: UserGapAnalysesResponse = {
    success: true,
    data: {
      analyses: analysesData,
      activeAnalysis: activeAnalysisData
    }
  };

  return NextResponse.json(responseData);
}

function calculateMilestoneDueDate(startDate: Date, milestoneMonth: number): string {
  const dueDate = new Date(startDate);

  // Fix month overflow issue: properly handle end-of-month dates
  const targetMonth = dueDate.getMonth() + milestoneMonth;
  const targetYear = dueDate.getFullYear() + Math.floor(targetMonth / 12);
  const normalizedMonth = targetMonth % 12;

  // Get the last day of the target month to avoid overflow
  const lastDayOfTargetMonth = new Date(targetYear, normalizedMonth + 1, 0).getDate();
  const targetDay = Math.min(dueDate.getDate(), lastDayOfTargetMonth);

  dueDate.setFullYear(targetYear, normalizedMonth, targetDay);
  return dueDate.toISOString();
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Apply rate limiting
  const rateLimitResult = withRateLimit(rateLimiters.api)(request);
  if (!rateLimitResult.allowed) {
    const error = new Error('Too many requests') as any;
    error.statusCode = 429;
    throw error;
  }

  return handleGetUserGapAnalyses(request);
});
