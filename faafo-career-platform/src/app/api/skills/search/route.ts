// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import prisma from '@/lib/prisma';
import { skillGapPerformanceMonitor } from '@/lib/performance/skill-gap-performance';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

interface SkillSearchResult {
  id: string;
  name: string;
  category: string;
  description?: string;
  marketData?: {
    averageSalary?: number;
    demandLevel?: string;
    growthRate?: number;
  };
}

interface SkillSearchResponse {
  success: boolean;
  skills: SkillSearchResult[];
  total: number;
  query: string;
}

export const GET = withUnifiedErrorHandling(
  async (request: NextRequest): Promise<NextResponse<SkillSearchResponse>> => {
    // Get user session for performance tracking
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q')?.trim();
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50);
    const category = searchParams.get('category');

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        skills: [],
        total: 0,
        query: query || ''
      });
    }

    // Build search conditions
    const searchConditions: any = {
      OR: [
        {
          name: {
            contains: query,
            
          }
        },
        {
          description: {
            contains: query,
            
          }
        },
        {
          category: {
            contains: query,
            
          }
        }
      ]
    };

    // Add category filter if specified
    if (category) {
      searchConditions.AND = [
        {
          category: {
            equals: category,
            
          }
        }
      ];
    }

    // Use performance monitoring for skill search
    const result = await skillGapPerformanceMonitor.monitorSkillSearch(
      query,
      async () => {
        // Search skills in database
        const skills = await prisma.skill.findMany({
          where: searchConditions,
          include: {
            marketData: {
              where: { isActive: true },
              orderBy: { dataDate: 'desc' },
              take: 1
            }
          },
          take: limit,
          orderBy: [
            {
              name: 'asc'
            }
          ]
        });

        // Format response
        const formattedSkills: SkillSearchResult[] = skills.map(skill => ({
          id: skill.id,
          name: skill.name,
          category: skill.category || 'General',
          description: skill.description || undefined,
          marketData: skill.marketData[0]
            ? {
                averageSalary: skill.marketData[0].averageSalaryImpact || undefined,
                demandLevel: skill.marketData[0].demandLevel?.toString() || undefined,
                growthRate: undefined // growthTrend is an enum, not a number
              }
            : undefined
        }));

        // If no results found in database, provide some common skills as fallback
        if (formattedSkills.length === 0) {
          return getCommonSkillSuggestions(query);
        }

        return formattedSkills;
      },
      userId || undefined
    );

    return NextResponse.json({
      success: true,
      skills: result,
      total: result.length,
      query
    });
  }
);

// Fallback common skills when database search returns no results
function getCommonSkillSuggestions(query: string): SkillSearchResult[] {
  const commonSkills = [
    { name: 'JavaScript', category: 'Programming Languages' },
    { name: 'Python', category: 'Programming Languages' },
    { name: 'React', category: 'Frontend Frameworks' },
    { name: 'Node.js', category: 'Backend Technologies' },
    { name: 'TypeScript', category: 'Programming Languages' },
    { name: 'SQL', category: 'Database Technologies' },
    { name: 'Git', category: 'Development Tools' },
    { name: 'Docker', category: 'DevOps Tools' },
    { name: 'AWS', category: 'Cloud Platforms' },
    { name: 'Project Management', category: 'Soft Skills' },
    { name: 'Communication', category: 'Soft Skills' },
    { name: 'Leadership', category: 'Soft Skills' },
    { name: 'Problem Solving', category: 'Soft Skills' },
    { name: 'Data Analysis', category: 'Analytics' },
    { name: 'Machine Learning', category: 'AI/ML' },
    { name: 'UI/UX Design', category: 'Design' },
    { name: 'Agile Methodology', category: 'Project Management' },
    { name: 'REST APIs', category: 'Backend Technologies' },
    { name: 'MongoDB', category: 'Database Technologies' },
    { name: 'CSS', category: 'Frontend Technologies' }
  ];

  const queryLower = query.toLowerCase();
  return commonSkills
    .filter(
      skill =>
        skill.name.toLowerCase().includes(queryLower) ||
        skill.category.toLowerCase().includes(queryLower)
    )
    .slice(0, 10)
    .map((skill, index) => ({
      id: `common-${index}`,
      name: skill.name,
      category: skill.category,
      description: `${skill.name} - ${skill.category}`
    }));
}
