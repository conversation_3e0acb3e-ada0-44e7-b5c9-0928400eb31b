import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';

/**
 * Clear all sessions endpoint - removes all session data from database and cookies
 * SECURITY: This is a nuclear option for development when sessions get completely stuck
 * PROTECTED: Only available in development mode with admin authentication
 */
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  // SECURITY CHECK 1: Only allow in development environment
  if (process.env['NODE_ENV'] === 'production') {
    console.error('🚨 SECURITY ALERT: Attempt to access development endpoint in production');
    return NextResponse.json(
      { error: 'Endpoint not available in production' },
      { status: 404 }
    );
  }

  // SECURITY CHECK 2: Require authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.email) {
    console.error('🚨 SECURITY ALERT: Unauthenticated access to clear-all-sessions');
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
  }

  // SECURITY CHECK 3: Require admin privileges (check if user exists)
  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    select: { id: true, email: true }
  });

  if (!user) {
    console.error('🚨 SECURITY ALERT: User not found for clear-all-sessions', {
      userEmail: session.user.email
    });
    return NextResponse.json(
      { error: 'Admin privileges required' },
      { status: 403 }
    );
  }

  console.log('🔒 Clear all sessions requested by admin:', {
    adminEmail: user.email,
    environment: process.env['NODE_ENV'],
    timestamp: new Date().toISOString()
  });

  // Clear all NextAuth sessions from database
  const deletedSessions = await prisma.session.deleteMany({});
  console.log(`Deleted ${deletedSessions.count} sessions from database`);

  // Clear all NextAuth accounts (optional - be careful with this)
  // const deletedAccounts = await prisma.account.deleteMany({});
  // console.log(`Deleted ${deletedAccounts.count} accounts from database`);

  // Clear all verification tokens
  const deletedTokens = await prisma.verificationToken.deleteMany({});
  console.log(`Deleted ${deletedTokens.count} verification tokens from database`);

  // Get all cookies
  const cookieStore = cookies();
  const allCookies = cookieStore.getAll();

  // Create response with cleared cookies
  const response = NextResponse.json({
    success: true,
    data: {
      message: 'All sessions cleared from database and cookies',
      deletedSessions: deletedSessions.count,
      deletedTokens: deletedTokens.count,
      clearedCookies: allCookies.length
    }
  });

  // Clear all NextAuth related cookies
  const nextAuthCookies = [
    'next-auth.session-token',
    'next-auth.csrf-token',
    'next-auth.callback-url',
    '__Secure-next-auth.session-token',
    '__Host-next-auth.csrf-token',
    'next-auth.pkce.code_verifier',
    'authjs.session-token',
    'authjs.csrf-token',
    'authjs.callback-url'
  ];

  // Clear NextAuth cookies
  nextAuthCookies.forEach(cookieName => {
    response.cookies.set(cookieName, '', {
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: false, // Set to false for localhost
      sameSite: 'lax'
    });

    response.cookies.set(cookieName, '', {
      expires: new Date(0),
      path: '/api/auth',
      httpOnly: true,
      secure: false,
      sameSite: 'lax'
    });
  });

  // Clear all existing cookies
  allCookies.forEach(cookie => {
    response.cookies.set(cookie.name, '', {
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: false,
      sameSite: 'lax'
    });
  });

  console.log(`Clear all sessions completed successfully`);

  return response;
});

export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  // SECURITY CHECK 1: Only allow in development environment
  if (process.env['NODE_ENV'] === 'production') {
    return NextResponse.json(
      { error: 'Endpoint not available in production' },
      { status: 404 }
    );
  }

  // SECURITY CHECK 2: Require authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.email) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
  }

  // SECURITY CHECK 3: Require admin privileges
  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    select: { id: true, email: true }
  });

  if (!user) {
    return NextResponse.json(
      { error: 'User not found' },
      { status: 403 }
    );
  }

  // Get session count for information (only for authenticated admins)
  const sessionCount = await prisma.session.count();
  const tokenCount = await prisma.verificationToken.count();

  return NextResponse.json({
    message: 'Use POST method to clear all sessions',
    endpoint: '/api/auth/clear-all-sessions',
    method: 'POST',
    currentSessions: sessionCount,
    currentTokens: tokenCount,
    environment: process.env['NODE_ENV'],
    adminUser: user.email
  });
});
