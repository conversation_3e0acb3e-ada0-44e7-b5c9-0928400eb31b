// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';

export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const session = await getServerSession(authOptions);
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get('userId') || session?.user?.id;
  const includeAchievements = searchParams.get('includeAchievements') === 'true';

  if (!userId) {
    const error = new Error('User ID is required') as any;
    error.statusCode = 401;
    throw error;
  }

  // Verify user access
  if (session?.user?.id !== userId) {
    const error = new Error('Unauthorized access') as any;
    error.statusCode = 403;
    throw error;
  }

  // Use Promise.all to fetch all data concurrently and prevent N+1 queries
  // Note: Using existing database schema - some tables may not exist yet
  const [userProfile, userAssessments, userForumPosts] = await Promise.all([
    // Get user's profile
    prisma.profile.findUnique({
      where: { userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        updatedAt: true
      }
    }),

    // Get user's assessments
    prisma.assessment.findMany({
      where: { userId },
      select: {
        id: true,
        status: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    }),

    // Get user's forum activity
    prisma.forumPost.findMany({
      where: { authorId: userId },
      select: {
        id: true,
        title: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    })
  ]);

  // Calculate progress statistics based on existing data
  const totalAssessments = userAssessments.length;
  const completedAssessments = userAssessments.filter(a => a.status === 'COMPLETED');
  const completedAssessmentsCount = completedAssessments.length;
  const inProgressAssessments = userAssessments.filter(a => a.status === 'IN_PROGRESS').length;

  // Forum activity statistics
  const totalForumPosts = userForumPosts.length;

  // Calculate user engagement metrics
  const accountAge = userProfile?.createdAt
    ? Math.floor((Date.now() - new Date(userProfile.createdAt).getTime()) / (1000 * 60 * 60 * 24))
    : 0;

  // Calculate streak based on assessment completion dates
  const uniqueDates = Array.from(
    new Set(completedAssessments.map(assessment => assessment.updatedAt.toDateString()))
  ).sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

  let streakDays = 0;
  if (uniqueDates.length > 0) {
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();

    // Check if user has activity today or yesterday to start counting streak
    if (uniqueDates.includes(today) || uniqueDates.includes(yesterday)) {
      let checkDate = new Date();
      while (uniqueDates.includes(checkDate.toDateString())) {
        streakDays++;
        checkDate = new Date(checkDate.getTime() - 24 * 60 * 60 * 1000);
      }
    }
  }

  // Weekly goal and progress (simplified)
  const weeklyGoal = 3; // Default goal for assessments
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  const weeklyProgress = userAssessments.filter(
    a => a.updatedAt >= oneWeekAgo && a.status === 'COMPLETED'
  ).length;

  // Format recent activity from assessments and forum posts
  const recentActivity = [
    ...userAssessments.slice(0, 3).map(assessment => ({
      id: assessment.id,
      resourceTitle: `Assessment ${assessment.id.slice(-8)}`,
      status: assessment.status,
      date: assessment.updatedAt.toISOString(),
      type: 'assessment'
    })),
    ...userForumPosts.slice(0, 2).map(post => ({
      id: post.id,
      resourceTitle: post.title,
      status: 'COMPLETED',
      date: post.createdAt.toISOString(),
      type: 'forum_post'
    }))
  ]
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5);

  // Goal statistics (placeholder - no goal table exists yet)
  const totalGoals = 0;
  const activeGoals = 0;
  const completedGoals = 0;
  const pausedGoals = 0;
  const cancelledGoals = 0;

  // Achievement statistics (placeholder - no achievement table exists yet)
  const totalAchievements = 0;
  const unlockedAchievements = 0;
  const totalPoints = 0;

  const progressData = {
    // Assessment-based metrics (replacing resource metrics)
    totalResources: totalAssessments,
    completedResources: completedAssessmentsCount,
    inProgressResources: inProgressAssessments,
    bookmarkedResources: 0, // No bookmarking in current schema

    // User engagement metrics
    averageRating: 4.5, // Placeholder rating
    totalRatings: totalAssessments,
    streakDays,
    weeklyGoal,
    weeklyProgress,
    recentActivity: recentActivity || [], // Ensure it's always an array

    // Additional metrics
    totalForumPosts,
    accountAge,

    // Goal statistics
    totalGoals,
    activeGoals,
    completedGoals,
    pausedGoals,
    cancelledGoals,

    // Achievement statistics
    totalAchievements,
    unlockedAchievements,
    totalPoints,

    // Always include achievements array (ProgressTracker expects this)
    achievements: [], // Empty for now since no achievement system exists yet
    recentAchievements: includeAchievements ? [] : undefined
  };

  return NextResponse.json({
    success: true,
    data: progressData
  });
});

export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      const error = new Error('Unauthorized') as any;
      error.statusCode = 401;
      throw error;
    }

    const body = await request.json();
    const { weeklyGoal } = body;

    if (!weeklyGoal || weeklyGoal < 1 || weeklyGoal > 50) {
      const error = new Error('Weekly goal must be between 1 and 50') as any;
      error.statusCode = 400;
      throw error;
    }

    // For now, we'll store this in user profile or a separate settings table
    // This is a simplified implementation

    return NextResponse.json({
      success: true,
      message: 'Weekly goal updated successfully',
      data: { weeklyGoal }
    });
  });
});
