import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const revalidate = false;

// TypeScript interfaces for API responses
interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  summary: {
    total: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
  };
  services?: HealthCheckResult[];
}

interface HealthActionResponse {
  message: string;
}

interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime?: number;
  error?: string;
  details?: any;
}

async function checkGeminiHealth(geminiService: any): Promise<HealthCheckResult> {
  const startTime = Date.now();

  try {
    const isHealthy = await geminiService.healthCheck();
    const responseTime = Date.now() - startTime;

    return {
      service: 'gemini',
      status: isHealthy ? 'healthy' : 'unhealthy',
      responseTime,
      details: {
        apiKey: !!process.env['GOOGLE_GEMINI_API_KEY'],
        model: 'gemini-pro'
      }
    };
  } catch (error) {
    return {
      service: 'gemini',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : String(error),
      details: {
        apiKey: !!process.env['GOOGLE_GEMINI_API_KEY']
      }
    };
  }
}

async function checkCacheHealth(consolidatedCache: any): Promise<HealthCheckResult> {
  const startTime = Date.now();

  try {
    const isHealthy = await consolidatedCache.healthCheck();
    const stats = await consolidatedCache.getStats();
    const responseTime = Date.now() - startTime;

    return {
      service: 'cache',
      status: isHealthy ? 'healthy' : 'unhealthy',
      responseTime,
      details: stats
    };
  } catch (error) {
    return {
      service: 'cache',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

async function checkEnvironmentHealth(): Promise<HealthCheckResult> {
  const requiredEnvVars = ['GOOGLE_GEMINI_API_KEY', 'DATABASE_URL', 'NEXTAUTH_SECRET'];

  const optionalEnvVars = ['REDIS_URL', 'AI_CACHE_TTL'];

  const missing = requiredEnvVars.filter(key => !process.env[key]);
  const optional = optionalEnvVars.filter(key => !process.env[key]);

  return {
    service: 'environment',
    status: missing.length === 0 ? 'healthy' : 'unhealthy',
    details: {
      required: {
        total: requiredEnvVars.length,
        configured: requiredEnvVars.length - missing.length,
        missing
      },
      optional: {
        total: optionalEnvVars.length,
        configured: optionalEnvVars.length - optional.length,
        missing: optional
      },
      nodeEnv: process.env['NODE_ENV']
    }
  };
}

async function performComprehensiveHealthCheck(geminiService: any, consolidatedCache: any): Promise<{
  overall: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  services: HealthCheckResult[];
  summary: {
    total: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
  };
}> {
  // const startTime = Date.now();

  // Run all health checks in parallel
  const [geminiHealth, cacheHealth, envHealth] = await Promise.all([
    checkGeminiHealth(geminiService),
    checkCacheHealth(consolidatedCache),
    checkEnvironmentHealth()
  ]);

  const services = [geminiHealth, cacheHealth, envHealth];

  // Calculate overall health
  const healthyCount = services.filter(s => s.status === 'healthy').length;
  const unhealthyCount = services.filter(s => s.status === 'unhealthy').length;
  const degradedCount = services.filter(s => s.status === 'degraded').length;

  let overall: 'healthy' | 'unhealthy' | 'degraded';
  if (unhealthyCount > 0) {
    overall = 'unhealthy';
  } else if (degradedCount > 0) {
    overall = 'degraded';
  } else {
    overall = 'healthy';
  }

  // const totalTime = Date.now() - startTime;

  return {
    overall,
    timestamp: new Date().toISOString(),
    services: services.map(service => ({
      ...service,
      responseTime: service.responseTime || 0
    })),
    summary: {
      total: services.length,
      healthy: healthyCount,
      unhealthy: unhealthyCount,
      degraded: degradedCount
    }
  };
}

// GET endpoint for health check
export async function GET(request: NextRequest): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      { consolidatedCache },
      { geminiService },
      { withRateLimit, rateLimiters }
    ] = await Promise.all([
      import('@/lib/services/consolidated-cache-service'),
      import('@/lib/services/geminiService'),
      import('@/lib/rate-limit')
    ]);

    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }

  const { searchParams } = new URL(request.url);
  const detailed = searchParams.get('detailed') === 'true';
  const service = searchParams.get('service');

  if (service) {
    // Check specific service
    let result: HealthCheckResult;

    switch (service) {
      case 'gemini':
        result = await checkGeminiHealth(geminiService);
        break;
      case 'cache':
        result = await checkCacheHealth(consolidatedCache);
        break;
      case 'environment':
        result = await checkEnvironmentHealth();
        break;
      default:
        const error = new Error('Invalid service. Available: gemini, cache, environment') as any;
        error.statusCode = 400;
        throw error;
    }

    return NextResponse.json(result);
  }

  // Comprehensive health check
  const healthCheck = await performComprehensiveHealthCheck(geminiService, consolidatedCache);

  // Return appropriate status code based on health
  const statusCode =
    healthCheck.overall === 'healthy' ? 200 : healthCheck.overall === 'degraded' ? 200 : 503;

  const responseData: HealthCheckResponse = {
    status: healthCheck.overall,
    timestamp: healthCheck.timestamp,
    summary: healthCheck.summary
  };

  if (detailed) {
    responseData.services = healthCheck.services;
  }

  // For unhealthy status, throw error to get proper status code
  if (healthCheck.overall === 'unhealthy') {
    const error = new Error('Health check failed') as any;
    error.statusCode = 503;
    error.data = responseData;
    throw error;
  }

  return NextResponse.json(responseData, { status: statusCode });
  } catch (error: any) {
    console.error('AI health check error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}

// POST endpoint for triggering cache warmup or service initialization
export async function POST(request: NextRequest): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      { consolidatedCache },
      { geminiService },
      { withRateLimit, rateLimiters }
    ] = await Promise.all([
      import('@/lib/services/consolidated-cache-service'),
      import('@/lib/services/geminiService'),
      import('@/lib/rate-limit')
    ]);

    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'warmup_cache':
        // Perform cache warmup operations
        await consolidatedCache.healthCheck();
        return NextResponse.json({ message: 'Cache warmup initiated' });

      case 'test_ai':
        // Test AI service with a simple request
        const testResult = await geminiService.healthCheck();
        return NextResponse.json({
          message: testResult ? 'AI service test passed' : 'AI service test failed'
        });

      case 'clear_cache':
        // Clear all AI-related cache (use with caution)
        await consolidatedCache.clear();
        return NextResponse.json({ message: 'Cache cleared successfully' });

      default:
        const error = new Error(
          'Invalid action. Available: warmup_cache, test_ai, clear_cache'
        ) as any;
        error.statusCode = 400;
        throw error;
    }
  } catch (error: any) {
    console.error('AI health POST error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}
