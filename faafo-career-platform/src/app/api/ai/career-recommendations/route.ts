import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const revalidate = false;

// TypeScript interfaces
interface CareerRecommendationsResponse {
  recommendations: any[];
  metadata: {
    generatedAt: string;
    basedOnAssessment: boolean;
    assessmentId?: string;
    skillsCount: number;
    hasPreferences: boolean;
  };
}

interface CachedRecommendationsResponse {
  data: CareerRecommendationsResponse;
  cached: boolean;
  message?: string;
}

interface ClearCacheResponse {
  message: string;
}

// Validation schema
const careerRecommendationsSchema = z.object({
  assessmentId: z.string().uuid().optional(),
  currentSkills: z
    .array(z.string())
    .min(1, 'At least one skill is required')
    .max(50, 'Too many skills'),
  preferences: z
    .object({
      workEnvironment: z.enum(['remote', 'hybrid', 'office', 'flexible']).optional(),
      industryPreferences: z.array(z.string()).optional(),
      salaryExpectations: z
        .object({
          min: z.number().min(0).optional(),
          max: z.number().min(0).optional()
        })
        .optional(),
      workLifeBalance: z.enum(['high', 'medium', 'low']).optional(),
      riskTolerance: z.enum(['high', 'medium', 'low']).optional(),
      careerStage: z.enum(['entry', 'mid', 'senior', 'executive']).optional()
    })
    .optional(),
  includeAssessmentData: z.boolean().optional().default(true)
});

async function getAssessmentData(userId: string, prisma: any, assessmentId?: string) {
  let assessment;

  if (assessmentId) {
    assessment = await prisma.assessment.findFirst({
      where: {
        id: assessmentId,
        userId: userId,
        status: 'COMPLETED'
      },
      include: {
        responses: true
      }
    });
  } else {
    // Get the most recent completed assessment
    assessment = await prisma.assessment.findFirst({
      where: {
        userId: userId,
        status: 'COMPLETED'
      },
      include: {
        responses: true
      },
      orderBy: {
        completedAt: 'desc'
      }
    });
  }

  if (!assessment) {
    return null;
  }

  // Transform responses into a more usable format
  const assessmentData = {
    id: assessment.id,
    completedAt: assessment.completedAt,
    responses: assessment.responses.reduce(
      (acc, response) => {
        acc[response.questionKey] = response.answerValue;
        return acc;
      },
      {} as Record<string, any>
    )
  };

  return assessmentData;
}

async function handleCareerRecommendations(
  request: NextRequest
): Promise<NextResponse<CareerRecommendationsResponse>> {
  // Dynamic imports to prevent build-time analysis
  const [
    { consolidatedCache },
    { withRateLimit, rateLimiters },
    prisma,
    { optimizedAIService }
  ] = await Promise.all([
    import('@/lib/services/consolidated-cache-service'),
    import('@/lib/rate-limit'),
    import('@/lib/prisma').then(m => m.default),
    import('@/lib/optimized-ai-service')
  ]);

  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const userId = session.user.id;

  const body = await request.json();
  const validation = careerRecommendationsSchema.safeParse(body);

  if (!validation.success) {
    const error = new Error('Invalid request data') as any;
    error.statusCode = 400;
    error.details = validation.error.errors;
    throw error;
  }

  const { assessmentId, currentSkills, preferences, includeAssessmentData } = validation.data;

  // Get assessment data if requested
  let assessmentData = null;
  if (includeAssessmentData) {
    assessmentData = await getAssessmentData(userId, prisma, assessmentId);
  }

  // Generate cache key based on input parameters
  const cacheKeyParams = [
    assessmentId || 'no-assessment',
    currentSkills.sort().join(','),
    JSON.stringify(preferences || {}),
    includeAssessmentData.toString()
  ];
  const cacheKey = `ai:career_recommendations:${userId}:${cacheKeyParams.join('|')}`;

  // Check cache first
  const cached = await consolidatedCache.get<any>(cacheKey);
  if (cached) {
    return NextResponse.json(cached as CareerRecommendationsResponse);
  }

  // Prepare data for AI analysis
  // const analysisData = {
  //   assessmentData: assessmentData?.responses || {},
  //   assessmentId: assessmentData?.id,
  //   assessmentCompletedAt: assessmentData?.completedAt,
  //   currentSkills,
  //   preferences: preferences || {},
  //   userId // For context but not for AI processing
  // };

  // Generate AI recommendations using optimized service
  const recommendationsResult = await optimizedAIService.generateCareerRecommendations(
    currentSkills,
    preferences || {},
    {
      userId,
      enableDeduplication: true,
      enableSemanticMatch: true,
      priority: 'medium'
    }
  );

  if (!recommendationsResult.success) {
    const error = new Error(
      recommendationsResult.error || 'Failed to generate career recommendations'
    ) as any;
    error.statusCode = 500;
    throw error;
  }

  // Enhance recommendations with additional data
  const enhancedData = {
    ...recommendationsResult.data,
    metadata: {
      generatedAt: new Date().toISOString(),
      basedOnAssessment: !!assessmentData,
      assessmentId: assessmentData?.id,
      skillsCount: currentSkills.length,
      hasPreferences: !!preferences && Object.keys(preferences).length > 0
    }
  };

  // Cache the result for 6 hours (recommendations can change based on market conditions)
  await consolidatedCache.set(cacheKey, enhancedData, {
    ttl: 6 * 60 * 60 * 1000,
    tags: ['career_recommendations', userId]
  });

  // Track usage analytics
  console.log(
    `Career recommendations generated for user ${userId}, assessment: ${assessmentId || 'none'}`
  );

  return NextResponse.json(enhancedData);
}

// GET endpoint for retrieving user's recent recommendations
export async function GET(request: NextRequest): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      { withUnifiedErrorHandling },
      { withRateLimit, rateLimiters },
      prisma,
      { consolidatedCache }
    ] = await Promise.all([
      import('@/lib/unified-api-error-handler'),
      import('@/lib/rate-limit'),
      import('@/lib/prisma').then(m => m.default),
      import('@/lib/services/consolidated-cache-service')
    ]);

    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }

    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      const error = new Error('Authentication required') as any;
      error.statusCode = 401;
      throw error;
    }

    const userId = session.user.id;
    const { searchParams } = new URL(request.url);
    const assessmentId = searchParams.get('assessmentId');

    // Get user's recent assessment if no specific ID provided
    let targetAssessmentId = assessmentId;
    if (!targetAssessmentId) {
      const recentAssessment = await prisma.assessment.findFirst({
        where: {
          userId: userId,
          status: 'COMPLETED'
        },
        orderBy: {
          completedAt: 'desc'
        },
        select: {
          id: true
        }
      });
      targetAssessmentId = recentAssessment?.id || 'no-assessment';
    }

    // Try to find cached recommendations
    // Note: This is a simplified approach - in production you might want to store cache keys
    const possibleCacheKey = `ai:career_recommendations:${userId}:${targetAssessmentId}`;
    const cached = await consolidatedCache.get<any>(possibleCacheKey);

    if (cached) {
      return NextResponse.json({
        success: true,
        data: cached,
        cached: true
      });
    }

    const error = new Error(
      'No cached recommendations found. Please generate new recommendations.'
    ) as any;
    error.statusCode = 404;
    error.code = 'NO_CACHE';
    throw error;
  } catch (error: any) {
    console.error('GET career recommendations error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}

// POST endpoint for generating new recommendations
export async function POST(request: NextRequest): Promise<NextResponse<any>> {
  try {
    return await handleCareerRecommendations(request);
  } catch (error: any) {
    console.error('Career recommendations error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}

// DELETE endpoint for clearing cached recommendations
export async function DELETE(request: NextRequest): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      { withRateLimit, rateLimiters },
      { consolidatedCache }
    ] = await Promise.all([
      import('@/lib/rate-limit'),
      import('@/lib/services/consolidated-cache-service')
    ]);

    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }

    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      const error = new Error('Authentication required') as any;
      error.statusCode = 401;
      throw error;
    }

    const userId = session.user.id;
    const { searchParams } = new URL(request.url);
    const assessmentId = searchParams.get('assessmentId');

    // Clear specific or all recommendations cache for user
    if (assessmentId) {
      const cacheKey = `ai:career_recommendations:${userId}:${assessmentId}`;
      await consolidatedCache.delete(cacheKey);
    } else {
      // Clear all recommendation caches for user using tag-based invalidation
      await consolidatedCache.invalidateByTags(['career_recommendations', userId]);
    }

    return NextResponse.json({ message: 'Recommendation cache cleared' });
  } catch (error: any) {
    console.error('DELETE career recommendations error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}
