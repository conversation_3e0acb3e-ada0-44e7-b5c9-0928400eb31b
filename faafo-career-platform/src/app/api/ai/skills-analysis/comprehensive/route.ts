import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const revalidate = false;

// Transformation function to ensure consistent data format
function transformSkillGapsData(data: any, UnifiedTimeCalculator: any): ComprehensiveSkillsAnalysisResponse['data'] {
  // Transform and validate skill gaps data
  const transformedSkillGaps = (data.skillGaps || []).map((gap: any, index: number) => {
    // Ensure skillName property exists (handle both 'skill' and 'skillName' properties)
    const skillName = gap.skillName || gap.skill || `Skill ${index + 1}`;

    // Calculate safe learning time using unified calculator (FIXED L001)
    let estimatedLearningTime = Number(gap.estimatedLearningTime);
    if (isNaN(estimatedLearningTime) || estimatedLearningTime <= 0) {
      // Use unified time calculator for consistent calculations
      const levelGap = Math.max(1, (gap.targetLevel || 5) - (gap.currentLevel || 0));
      const timeEstimate = UnifiedTimeCalculator.calculateLearningTime({
        gapSize: levelGap,
        skillName: skillName,
        difficulty: 5 // Default difficulty
      });
      estimatedLearningTime = timeEstimate.hours;
    }

    return {
      skillId: gap.skillId || `skill_${skillName.toLowerCase().replace(/\s+/g, '_')}_${index}`,
      skillName: skillName,
      currentLevel: Math.max(0, Math.min(10, Number(gap.currentLevel) || 0)),
      targetLevel: Math.max(1, Math.min(10, Number(gap.targetLevel) || 5)),
      gapSeverity: gap.gapSeverity || 'MEDIUM',
      priority: Math.max(1, Math.min(100, Number(gap.priority) || 50)),
      estimatedLearningTime: Math.round(estimatedLearningTime),
      marketDemand: gap.marketDemand || 'MODERATE',
      salaryImpact: Number(gap.salaryImpact) || 0
    };
  });

  // Calculate total learning hours from skill gaps
  const totalEstimatedHours = transformedSkillGaps.reduce((total: number, gap: any) => total + gap.estimatedLearningTime, 0);

  return {
    analysisId: data.analysisId,
    skillGaps: transformedSkillGaps,
    learningPlan: {
      totalEstimatedHours: totalEstimatedHours,
      milestones: data.learningPlan?.milestones || [],
      recommendedResources: data.learningPlan?.recommendedResources || []
    },
    careerReadiness: data.careerReadiness || {
      currentScore: 0,
      targetScore: 100,
      improvementPotential: 100,
      timeToTarget: 12
    },
    marketInsights: data.marketInsights
  };
}

// Enhanced validation schema for comprehensive analysis
const comprehensiveSkillsAnalysisSchema = z.object({
  currentSkills: z
    .array(
      z.object({
        skillId: z.string().optional(),
        skillName: z.string().min(1, 'Skill name is required'),
        selfRating: z.number().min(1).max(10),
        confidenceLevel: z.number().min(1).max(10),
        lastUsed: z.string().optional(),
        yearsOfExperience: z.number().min(0).max(50).optional()
      })
    )
    .min(0, 'Skills array cannot be negative')
    .max(50, 'Too many skills'), // Allow empty array

  targetCareerPath: z.object({
    careerPathId: z.string().optional(),
    careerPathName: z.string().min(2, 'Career path name is required'),
    targetLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'])
  }),

  preferences: z.object({
    timeframe: z.enum(['THREE_MONTHS', 'SIX_MONTHS', 'ONE_YEAR', 'TWO_YEARS', 'CUSTOM']),
    hoursPerWeek: z.number().min(1).max(80),
    learningStyle: z.array(z.string()).optional().default([]),
    budget: z.enum(['FREE', 'FREEMIUM', 'PAID', 'ANY']).default('ANY'),
    focusAreas: z.array(z.string()).optional().default([])
  }),

  includeMarketData: z.boolean().default(true),
  includePersonalizedPaths: z.boolean().default(true)
});

type ComprehensiveSkillsAnalysisRequest = z.infer<typeof comprehensiveSkillsAnalysisSchema>;

interface ComprehensiveSkillsAnalysisResponse {
  success: boolean;
  data: {
    analysisId: string;
    skillGaps: Array<{
      skillId: string;
      skillName: string;
      currentLevel: number;
      targetLevel: number;
      gapSeverity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
      priority: number;
      estimatedLearningTime: number;
      marketDemand?: string;
      salaryImpact?: number;
    }>;
    learningPlan: {
      totalEstimatedHours: number;
      milestones: Array<{
        month: number;
        skills: string[];
        estimatedHours: number;
        learningPaths: string[];
      }>;
      recommendedResources: Array<{
        resourceId: string;
        resourceType: string;
        priority: string;
        skillsAddressed: string[];
        estimatedHours: number;
      }>;
    };
    careerReadiness: {
      currentScore: number;
      targetScore: number;
      improvementPotential: number;
      timeToTarget: number;
    };
    marketInsights?: {
      industryTrends: Array<{
        skill: string;
        trend: string;
        demandLevel: string;
      }>;
      salaryProjections: {
        currentEstimate: number;
        targetEstimate: number;
        improvementPotential: number;
      };
    };
  };
  cached: boolean;
  generatedAt: string;
}

// async function getUserSkillAssessments(userId: string) {
// try {
//   const assessments = await prisma.skillAssessment.findMany({
//     where: {
//       userId,
//       isActive: true,
//     },
//     include: {
//       skill: true,
//     },
//     orderBy: {
//       assessmentDate: 'desc',
//     },
//   });

//   return assessments.map(assessment => ({
//     skillId: assessment.skillId,
//     skillName: assessment.skill.name,
//     selfRating: assessment.selfRating,
//     confidenceLevel: assessment.confidenceLevel,
//     lastAssessed: assessment.assessmentDate,
//     assessmentType: assessment.assessmentType,
//   }));
// } catch (error) {
//   console.error('Error fetching user skill assessments:', error);
//   return [];
// }
// }

async function getSkillsFromCareerAssessment(userId: string, prisma: any) {
  try {
    const assessment = await prisma.assessment.findFirst({
      where: {
        userId,
        status: 'COMPLETED'
      },
      include: {
        responses: true
      },
      orderBy: {
        completedAt: 'desc'
      }
    });

    if (!assessment) {
      return [];
    }

    const skillsFromAssessment: Array<{
      skillName: string;
      selfRating: number;
      confidenceLevel: number;
      lastUsed: string;
    }> = [];

    // Extract skills from assessment responses
    assessment.responses.forEach(response => {
      try {
        const value =
          typeof response.answerValue === 'string'
            ? JSON.parse(response.answerValue)
            : response.answerValue;

        // Look for skills-related questions
        if (response.questionKey.toLowerCase().includes('skill') && Array.isArray(value)) {
          value.forEach((skill: string) => {
            if (typeof skill === 'string' && skill.trim()) {
              skillsFromAssessment.push({
                skillName: skill.trim(),
                selfRating: 6, // Default moderate rating
                confidenceLevel: 6, // Default moderate confidence
                lastUsed: assessment.completedAt?.toISOString() || new Date().toISOString()
              });
            }
          });
        }

        // Look for experience level indicators
        if (
          response.questionKey.toLowerCase().includes('experience') &&
          typeof value === 'string'
        ) {
          const experienceLevel = value.toLowerCase();
          let defaultRating = 5;
          if (experienceLevel.includes('senior') || experienceLevel.includes('expert')) {
            defaultRating = 8;
          } else if (experienceLevel.includes('mid') || experienceLevel.includes('intermediate')) {
            defaultRating = 6;
          } else if (experienceLevel.includes('junior') || experienceLevel.includes('beginner')) {
            defaultRating = 4;
          }

          // Update ratings for existing skills
          skillsFromAssessment.forEach(skill => {
            skill.selfRating = defaultRating;
            skill.confidenceLevel = defaultRating;
          });
        }
      } catch (error) {
        console.error('Error parsing assessment response:', error);
      }
    });

    return skillsFromAssessment;
  } catch (error) {
    console.error('Error fetching skills from career assessment:', error);
    return [];
  }
}

async function getEnhancedCareerPathData(prisma: any, careerPathId?: string, careerPathName?: string) {
  try {
    const startTime = Date.now();

    const whereClause = careerPathId
      ? { id: careerPathId }
      : careerPathName
        ? {
            OR: [
              { name: { contains: careerPathName } },
              { slug: careerPathName.toLowerCase().replace(/\s+/g, '-') }
            ]
          }
        : {};

    // Use optimized select with selective loading for better performance
    const careerPath = await prisma.careerPath.findFirst({
      where: whereClause,
      select: {
        id: true,
        name: true,
        slug: true,
        overview: true,
        // Optimized related skills with minimal market data
        relatedSkills: {
          select: {
            id: true,
            name: true,
            category: true,
            description: true,
            marketData: {
              where: { isActive: true },
              orderBy: { dataDate: 'desc' },
              take: 1,
              select: {
                id: true,
                averageSalaryImpact: true,
                demandLevel: true,
                growthTrend: true,
                dataDate: true
              }
            }
          },
          take: 20 // Limit to prevent excessive data loading
        },
        // Optimized learning resources with selective fields
        learningResources: {
          where: { isActive: true },
          select: {
            id: true,
            title: true,
            type: true,
            skillLevel: true,
            cost: true,
            duration: true,
            url: true,
            skills: {
              select: {
                id: true,
                name: true
              },
              take: 5 // Limit skills per resource
            },
            ratings: {
              select: {
                rating: true
              },
              take: 100 // Limit ratings for average calculation
            }
          },
          take: 15, // Limit learning resources
          orderBy: [{ skillLevel: 'asc' }, { cost: 'asc' }]
        },
        // Optimized learning paths with essential data only
        learningPaths: {
          where: { isActive: true },
          select: {
            id: true,
            title: true,
            difficulty: true,
            estimatedHours: true,
            skills: {
              select: {
                id: true,
                name: true
              },
              take: 10 // Limit skills per path
            },
            _count: {
              select: {
                steps: true
              }
            }
          },
          take: 10, // Limit learning paths
          orderBy: { difficulty: 'asc' }
        }
      }
    });

    const queryTime = Date.now() - startTime;

    // Log performance metrics for monitoring
    if (queryTime > 1000) {
      console.warn(
        `Slow getEnhancedCareerPathData query: ${queryTime}ms for ${careerPathId || careerPathName}`
      );
    }

    if (!careerPath) return null;

    // Optimized data transformation with performance monitoring
    const transformStartTime = Date.now();

    const result = {
      id: careerPath.id,
      name: careerPath.name,
      slug: careerPath.slug,
      overview: careerPath.overview,
      requiredSkills: careerPath.relatedSkills.map(skill => ({
        id: skill.id,
        name: skill.name,
        category: skill.category,
        description: skill.description,
        marketData: skill.marketData[0] || null
      })),
      learningResources: careerPath.learningResources.map(resource => {
        // Optimized rating calculation
        const averageRating =
          resource.ratings.length > 0
            ? Math.round(
                (resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length) *
                  10
              ) / 10
            : 0;

        return {
          id: resource.id,
          title: resource.title,
          type: resource.type,
          skillLevel: resource.skillLevel,
          cost: resource.cost,
          duration: resource.duration,
          url: resource.url,
          averageRating,
          ratingCount: resource.ratings.length,
          skills: resource.skills?.map(skill => skill.name) || []
        };
      }),
      learningPaths: careerPath.learningPaths.map(path => ({
        id: path.id,
        title: path.title,
        difficulty: path.difficulty,
        estimatedHours: path.estimatedHours,
        stepCount: path._count.steps,
        skills: path.skills?.map(skill => skill.name) || []
      })),
      performance: {
        queryTime,
        transformTime: Date.now() - transformStartTime,
        totalSkills: careerPath.relatedSkills.length,
        totalResources: careerPath.learningResources.length,
        totalPaths: careerPath.learningPaths.length
      }
    };

    return result;
  } catch (error) {
    console.error('Error fetching enhanced career path data:', error);
    return null;
  }
}

// async function createSkillGapAnalysis(
// userId: string,
// request: ComprehensiveSkillsAnalysisRequest,
// analysisData: any,
// careerPathData: any
// ) {
// try {
//   const expiresAt = new Date();
//   expiresAt.setMonth(expiresAt.getMonth() + 3); // Analysis valid for 3 months

//   const skillGapAnalysis = await prisma.skillGapAnalysis.create({
//     data: {
//       userId,
//       targetCareerPathId: request.targetCareerPath.careerPathId || null,
//       targetCareerPathName: request.targetCareerPath.careerPathName,
//       experienceLevel: request.targetCareerPath.targetLevel,
//       timeframe: request.preferences.timeframe,
//       analysisData: analysisData,
//       skillGaps: analysisData.skillGaps || [],
//       learningPlan: analysisData.learningPlan || {},
//       marketData: analysisData.marketInsights || null,
//       progressTracking: {
//         milestones: analysisData.learningPlan?.milestones || [],
//         completedMilestones: [],
//         currentPhase: 'planning',
//       },
//       status: 'ACTIVE',
//       completionPercentage: 0,
//       expiresAt,
//     },
//   });

//   return skillGapAnalysis;
// } catch (error) {
//   console.error('Error creating skill gap analysis:', error);
//   throw error;
// }
// }

async function handleComprehensiveSkillsAnalysis(
  _request: NextRequest
): Promise<NextResponse<ComprehensiveSkillsAnalysisResponse['data']>> {
  // Dynamic imports to prevent build-time analysis
  const [
    { consolidatedCache },
    { geminiService },
    prisma,
    { skillGapPerformanceMonitor },
    { edgeCaseHandlerService },
    { UnifiedTimeCalculator },
    { requestBatchingService },
    { concurrentDatabaseService }
  ] = await Promise.all([
    import('@/lib/services/consolidated-cache-service'),
    import('@/lib/services/geminiService'),
    import('@/lib/prisma').then(m => m.default),
    import('@/lib/performance/skill-gap-performance'),
    import('@/lib/skills/EdgeCaseHandlerService'),
    import('@/lib/unified-time-calculator'),
    import('@/lib/services/request-batching-service'),
    import('@/lib/services/concurrent-database-service')
  ]);

  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const userId = session.user.id;

  const body = await _request.json();
  const validation = comprehensiveSkillsAnalysisSchema.safeParse(body);

  if (!validation.success) {
    const error = new Error('Invalid request data') as any;
    error.statusCode = 400;
    error.details = validation.error.errors;
    throw error;
  }

  const requestData = validation.data;

  // Use enhanced caching with multi-level cache and compression (v2 for data transformation fix)
  const cacheKey = `ai:skills_analysis:${userId}:comprehensive_v2_${requestData.targetCareerPath.careerPathName}_${requestData.targetCareerPath.targetLevel}_${requestData.preferences.timeframe}`;

  const cacheTags = [
    'skill_analysis',
    'career_path',
    requestData.includeMarketData ? 'market_data' : '',
    userId
  ].filter(Boolean);

  // Check enhanced cache first (L1 + L2 + shared cache)
  const cached = await consolidatedCache.get<any>(cacheKey);
  if (cached) {
    console.log('🔄 Using cached data, applying transformation...');
    console.log('📊 Cached skill gaps before transformation:', cached.skillGaps?.slice(0, 2));
    // Apply transformation to cached data as well to ensure consistency
    const transformedCachedData = transformSkillGapsData(cached, UnifiedTimeCalculator);
    console.log('✅ Cached skill gaps after transformation:', transformedCachedData.skillGaps?.slice(0, 2));
    return NextResponse.json(transformedCachedData);
  }

  // Use request batching service for optimized processing
  try {
    const batchResult = await requestBatchingService.batchComprehensiveAnalysis(
      userId,
      requestData,
      'medium' // priority
    );

    if (batchResult.success && batchResult.data) {
      // Cache the result with enhanced caching
      await consolidatedCache.set(cacheKey, batchResult.data, { ttl: 1800000, tags: cacheTags }); // 30 minutes

      return NextResponse.json(batchResult.data);
    } else {
      throw new Error(batchResult.error || 'Batch processing failed');
    }
  } catch (batchError) {
    console.warn('Batch processing failed, falling back to individual processing:', batchError);

    // Fallback to individual processing if batch fails
    return await handleIndividualAnalysis(userId, requestData, cacheKey, cacheTags, {
      consolidatedCache,
      geminiService,
      prisma,
      skillGapPerformanceMonitor,
      edgeCaseHandlerService,
      UnifiedTimeCalculator,
      requestBatchingService,
      concurrentDatabaseService
    });
  }
}

async function handleIndividualAnalysis(
  userId: string,
  requestData: ComprehensiveSkillsAnalysisRequest,
  cacheKey: string,
  cacheTags: string[],
  services: {
    consolidatedCache: any;
    geminiService: any;
    prisma: any;
    skillGapPerformanceMonitor: any;
    edgeCaseHandlerService: any;
    UnifiedTimeCalculator: any;
    requestBatchingService: any;
    concurrentDatabaseService: any;
  }
): Promise<NextResponse<ComprehensiveSkillsAnalysisResponse['data']>> {
  const { consolidatedCache, geminiService, prisma, skillGapPerformanceMonitor, edgeCaseHandlerService, UnifiedTimeCalculator, requestBatchingService, concurrentDatabaseService } = services;
  // Use concurrent database operations for optimized data fetching
  const [userAssessments, careerPathData] = await Promise.all([
    concurrentDatabaseService.fetchUserAssessmentsOptimized(userId),
    requestData.targetCareerPath.careerPathId
      ? concurrentDatabaseService.fetchCareerPathDataOptimized(
          requestData.targetCareerPath.careerPathId
        )
      : getEnhancedCareerPathData(prisma, undefined, requestData.targetCareerPath.careerPathName)
  ]);

  // Get skills from career assessment if no skill assessments exist
  const careerAssessmentSkills =
    userAssessments.length === 0 ? await getSkillsFromCareerAssessment(userId, prisma) : [];

  // Fallback career path data if not found
  const finalCareerPathData = careerPathData || {
    id: 'fallback-career-path-id',
    name: requestData.targetCareerPath.careerPathName,
    requiredSkills: [],
    learningResources: [],
    learningPaths: []
  };

  // Helper function to normalize skill objects for AI analysis
  const normalizeSkillObject = (skill: any) => {
    const normalized: {
      skillName: string;
      selfRating: number;
      confidenceLevel: number;
      lastUsed?: string;
      yearsOfExperience?: number;
    } = {
      skillName: skill.skillName,
      selfRating: skill.selfRating,
      confidenceLevel: skill.confidenceLevel
    };

    // Only include lastUsed if it's a valid string
    if (skill.lastUsed && typeof skill.lastUsed === 'string') {
      normalized.lastUsed = skill.lastUsed;
    }

    // Only include yearsOfExperience if it's a valid number
    if (skill.yearsOfExperience && typeof skill.yearsOfExperience === 'number') {
      normalized.yearsOfExperience = skill.yearsOfExperience;
    }

    return normalized;
  };

  // Prepare data for AI analysis with optimized skill deduplication
  const allCurrentSkills = [
    ...requestData.currentSkills.map(normalizeSkillObject),
    ...userAssessments
      .filter(assessment => assessment.skill) // Only include assessments with skill data
      .map(assessment =>
        normalizeSkillObject({
          skillName: assessment.skill?.name || assessment.skillName,
          selfRating: assessment.selfRating,
          confidenceLevel: assessment.confidenceLevel,
          lastUsed:
            assessment.createdAt?.toISOString() ||
            assessment.lastAssessed?.toISOString() ||
            new Date().toISOString()
        })
      ),
    ...careerAssessmentSkills.map(normalizeSkillObject)
  ];

  // Optimized skill deduplication with performance tracking
  const uniqueSkills = allCurrentSkills.reduce(
    (acc, skill) => {
      const existing = acc.find(s => s.skillName.toLowerCase() === skill.skillName.toLowerCase());
      if (!existing) {
        acc.push(skill);
      } else if (skill.selfRating && skill.selfRating > (existing.selfRating || 0)) {
        // Keep the higher rating if duplicate
        Object.assign(existing, skill);
      }
      return acc;
    },
    [] as typeof allCurrentSkills
  );

  // Perform comprehensive AI analysis with optimized concurrent processing
  const responseData = await skillGapPerformanceMonitor.monitorSkillAnalysis(
    requestData,
    async () => {
      // Use concurrent processing for AI analysis and database operations
      const [edgeCaseResult, analysisResult] = await Promise.allSettled([
        edgeCaseHandlerService.handleLearningPathGeneration({
          userId,
          currentSkills: uniqueSkills.map(skill => ({
            skill: skill.skillName,
            level: skill.selfRating,
            confidence: skill.confidenceLevel
          })),
          targetRole: requestData.targetCareerPath.careerPathName,
          timeframe:
            requestData.preferences.timeframe === 'THREE_MONTHS'
              ? 3
              : requestData.preferences.timeframe === 'SIX_MONTHS'
                ? 6
                : requestData.preferences.timeframe === 'ONE_YEAR'
                  ? 12
                  : requestData.preferences.timeframe === 'TWO_YEARS'
                    ? 24
                    : 12,
          learningStyle: 'balanced',
          availability: requestData.preferences.hoursPerWeek,
          budget:
            requestData.preferences.budget === 'FREE'
              ? 0
              : requestData.preferences.budget === 'FREEMIUM'
                ? 100
                : requestData.preferences.budget === 'PAID'
                  ? 1000
                  : 500
        }),
        geminiService.analyzeComprehensiveSkillGap(
          uniqueSkills,
          {
            careerPathName: requestData.targetCareerPath.careerPathName,
            targetLevel: requestData.targetCareerPath.targetLevel,
            ...(requestData.targetCareerPath.careerPathId && {
              careerPathId: requestData.targetCareerPath.careerPathId
            })
          },
          requestData.preferences as any,
          finalCareerPathData,
          userId
        )
      ]);

      // Process results with fallback handling
      let finalAnalysisResult;

      if (analysisResult.status === 'fulfilled' && analysisResult.value.success) {
        finalAnalysisResult = analysisResult.value;
      } else if (edgeCaseResult.status === 'fulfilled' && edgeCaseResult.value.success) {
        console.warn('Primary AI analysis failed, using EdgeCaseHandler result');
        finalAnalysisResult = { success: true, data: edgeCaseResult.value.data };
      } else {
        // Both failed, use fallback data
        const fallbackData =
          edgeCaseResult.status === 'fulfilled' ? edgeCaseResult.value.fallbackData : null;
        if (fallbackData) {
          return {
            skillGaps: [],
            learningPlan: fallbackData,
            careerReadiness: {
              currentScore: 0,
              targetScore: 100,
              improvementPotential: 100,
              timeToTarget: 12
            },
            marketInsights: undefined,
            edgeCaseHandlerUsed: true,
            fallbackDataUsed: true
          };
        }
        throw new Error('All analysis methods failed');
      }

      // Use optimized concurrent database operations for analysis creation
      const skillGapAnalysis = await concurrentDatabaseService.createSkillGapAnalysisOptimized(
        userId,
        requestData,
        finalAnalysisResult.data,
        finalCareerPathData
      );

      // Transform and validate data using reusable function
      console.log('🔄 Processing fresh data, applying transformation...');
      console.log('📊 Fresh skill gaps before transformation:', finalAnalysisResult.data.skillGaps?.slice(0, 2));
      const responseData = transformSkillGapsData({
        analysisId: skillGapAnalysis.id,
        skillGaps: finalAnalysisResult.data.skillGaps,
        learningPlan: finalAnalysisResult.data.learningPlan,
        careerReadiness: finalAnalysisResult.data.careerReadiness,
        marketInsights: requestData.includeMarketData
          ? finalAnalysisResult.data.marketInsights
          : undefined
      }, UnifiedTimeCalculator);
      console.log('✅ Fresh skill gaps after transformation:', responseData.skillGaps?.slice(0, 2));

      return responseData;
    },
    userId
  );

  // Cache the result using enhanced caching service
  await consolidatedCache.set(cacheKey, responseData, { ttl: 1800000, tags: cacheTags }); // 30 minutes

  // Track usage analytics
  console.log(
    `Individual comprehensive skills analysis completed for user ${userId}, career: ${requestData.targetCareerPath.careerPathName}`
  );

  return NextResponse.json(responseData);
}

// POST endpoint for comprehensive analysis (Now free for all users)
export async function POST(request: NextRequest): Promise<NextResponse<any>> {
  try {
    // Dynamic imports to prevent build-time analysis
    const [
      { withRateLimit, rateLimiters },
      { withCSRFProtection }
    ] = await Promise.all([
      import('@/lib/rate-limit'),
      import('@/lib/csrf')
    ]);

    return withCSRFProtection(request, async () => {
      // Apply rate limiting
      const rateLimitResult = withRateLimit(rateLimiters.api)(request);
      if (!rateLimitResult.allowed) {
        const error = new Error('Too many requests') as any;
        error.statusCode = 429;
        throw error;
      }

      return await handleComprehensiveSkillsAnalysis(request);
    }) as Promise<NextResponse<ComprehensiveSkillsAnalysisResponse['data']>>;
  } catch (error: any) {
    console.error('Comprehensive skills analysis POST error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.statusCode || 500 }
    );
  }
}
