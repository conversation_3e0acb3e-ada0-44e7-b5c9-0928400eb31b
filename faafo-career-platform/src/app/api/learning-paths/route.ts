// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
import { z } from 'zod';

import { isUserAdmin } from '@/lib/auth-utils';

// Validation schemas
const createLearningPathSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().min(1, 'Description is required').max(2000, 'Description too long'),
  difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']),
  estimatedHours: z
    .number()
    .min(1, 'Estimated hours must be at least 1')
    .max(1000, 'Estimated hours too high'),
  category: z.enum([
    'CYBERSECURITY',
    'DATA_SCIENCE',
    'BLOCKCHAIN',
    'PROJECT_MANAGEMENT',
    'DIGITAL_MARKETING',
    'FINANCIAL_LITERACY',
    'LANGUAGE_LEARNING',
    'ARTIFICIAL_INTELLIGENCE',
    'WEB_DEVELOPMENT',
    'MOBILE_DEVELOPMENT',
    'CLOUD_COMPUTING',
    'ENTREPRENEURSHIP',
    'UX_UI_DESIGN',
    'PRODUCT_MANAGEMENT',
    'DEVOPS'
  ]),
  prerequisites: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  imageUrl: z.string().url().optional(),
  skillIds: z.array(z.string().uuid()).optional(),
  careerPathIds: z.array(z.string().uuid()).optional()
});

const querySchema = z.object({
  category: z.string().optional(),
  difficulty: z.string().optional(),
  search: z.string().optional(),
  skillId: z.string().optional(),
  careerPathId: z.string().optional(),
  page: z
    .string()
    .optional()
    .transform(val => (val ? parseInt(val) : 1)),
  limit: z
    .string()
    .optional()
    .transform(val => (val ? parseInt(val) : 10)),
  includeSteps: z
    .string()
    .optional()
    .transform(val => val === 'true'),
  includeProgress: z
    .string()
    .optional()
    .transform(val => val === 'true')
});

// GET - Retrieve learning paths
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Apply rate limiting
  const rateLimitResult = withRateLimit(rateLimiters.api)(request);
  if (!rateLimitResult.allowed) {
    const error = new Error('Too many requests') as any;
    error.statusCode = 429;
    throw error;
  }
  const { searchParams } = new URL(request.url);
  const query = querySchema.parse(Object.fromEntries(searchParams));

  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;

  // Build cache key
  const cacheKey = `learning_paths:${JSON.stringify(query)}:${userId || 'anonymous'}`;

  // Check cache first
  const cached = await consolidatedCache.get<any>(cacheKey);
  if (cached) {
    return NextResponse.json({
      success: true,
      data: cached,
      cached: true
    });
  }

  // Build where clause
  const where: any = {
    isActive: true
  };

  if (query.category) {
    where.category = query.category;
  }

  if (query.difficulty) {
    where.difficulty = query.difficulty;
  }

  if (query.search) {
    where.OR = [
      { title: { contains: query.search,  } },
      { description: { contains: query.search,  } }
    ];
  }

  if (query.skillId) {
    where.skills = {
      some: { id: query.skillId }
    };
  }

  if (query.careerPathId) {
    where.careerPaths = {
      some: { id: query.careerPathId }
    };
  }

  // Calculate pagination
  const skip = (query.page - 1) * query.limit;

  // Build include clause
  const include: any = {
    skills: true,
    careerPaths: {
      select: {
        id: true,
        name: true,
        slug: true
      }
    },
    _count: {
      select: {
        steps: true,
        userPaths: true
      }
    }
  };

  if (query.includeSteps) {
    include.steps = {
      orderBy: { stepOrder: 'asc' },
      include: {
        resource: {
          select: {
            id: true,
            title: true,
            type: true,
            url: true
          }
        }
      }
    };
  }

  if (query.includeProgress && userId) {
    include.userPaths = {
      where: { userId },
      select: {
        id: true,
        status: true,
        progressPercent: true,
        completedSteps: true,
        totalSteps: true,
        lastAccessedAt: true,
        startedAt: true,
        completedAt: true
      }
    };
  }

  // Execute queries
  const [learningPaths, totalCount] = await Promise.all([
    prisma.learningPath.findMany({
      where,
      include,
      orderBy: [{ createdAt: 'desc' }],
      skip,
      take: query.limit
    }),
    prisma.learningPath.count({ where })
  ]);

  // Transform data
  const transformedPaths = learningPaths.map(path => ({
    ...path,
    stepCount: (path as any)._count?.steps || 0,
    enrollmentCount: (path as any)._count?.userPaths || 0,
    userProgress: (path as any).userPaths?.[0] || null,
    _count: undefined,
    userPaths: undefined
  }));

  const result = {
    learningPaths: transformedPaths,
    pagination: {
      page: query.page,
      limit: query.limit,
      total: totalCount,
      pages: Math.ceil(totalCount / query.limit)
    },
    filters: {
      category: query.category,
      difficulty: query.difficulty,
      search: query.search
    }
  };

  // Cache for 10 minutes
  await consolidatedCache.set(cacheKey, result, { ttl: 10 * 60 * 1000, tags: ['learning_paths'] });

  return NextResponse.json({
    success: true,
    data: result
  });
});

// POST - Create new learning path (admin only for now)
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Apply rate limiting
  const rateLimitResult = withRateLimit(rateLimiters.api)(request);
  if (!rateLimitResult.allowed) {
    const error = new Error('Too many requests') as any;
    error.statusCode = 429;
    throw error;
  }
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return NextResponse.json({ success: false, error: 'Authentication required' }, { status: 401 });
  }

  // For now, only allow admins to create learning paths
  // In the future, this could be expanded to allow user-generated content
  const isAdmin = await isUserAdmin(session.user.id);
  if (!isAdmin) {
    return NextResponse.json({ success: false, error: 'Admin access required' }, { status: 403 });
  }

  const body = await request.json();
  const validation = createLearningPathSchema.safeParse(body);

  if (!validation.success) {
    return NextResponse.json(
      {
        success: false,
        error: 'Invalid request data',
        details: validation.error.errors
      },
      { status: 400 }
    );
  }

  const {
    title,
    description,
    difficulty,
    estimatedHours,
    category,
    prerequisites,
    tags,
    imageUrl,
    skillIds,
    careerPathIds
  } = validation.data;

  // Generate slug
  const slug = title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();

  // Check if slug already exists
  const existingPath = await prisma.learningPath.findUnique({
    where: { slug }
  });

  if (existingPath) {
    return NextResponse.json(
      { success: false, error: 'A learning path with this title already exists' },
      { status: 409 }
    );
  }

  // Create learning path
  const learningPath = await prisma.learningPath.create({
    data: {
      title,
      description,
      slug,
      difficulty,
      estimatedHours,
      category,
      prerequisites: prerequisites || [],
      tags: tags || [],
      imageUrl,
      createdBy: session.user.id,
      skills: skillIds
        ? {
            connect: skillIds.map(id => ({ id }))
          }
        : undefined,
      careerPaths: careerPathIds
        ? {
            connect: careerPathIds.map(id => ({ id }))
          }
        : undefined
    },
    include: {
      skills: true,
      careerPaths: {
        select: {
          id: true,
          name: true,
          slug: true
        }
      },
      _count: {
        select: {
          steps: true,
          userPaths: true
        }
      }
    }
  });

  // Clear cache
  await consolidatedCache.invalidateByTags(['learning_paths']);

  return NextResponse.json(
    {
      success: true,
      data: {
        ...learningPath,
        stepCount: learningPath._count.steps,
        enrollmentCount: learningPath._count.userPaths,
        _count: undefined
      },
      message: 'Learning path created successfully'
    },
    { status: 201 }
  );
});
