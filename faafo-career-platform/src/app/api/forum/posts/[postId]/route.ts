// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { isUserAdmin } from '@/lib/auth-utils';

// GET - Retrieve specific forum post with optimized query (fixes N+1)
export const GET = withUnifiedErrorHandling(
  async (request: NextRequest, { params }: { params: Promise<{ postId: string }> }) => {
    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const { postId } = await params;
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    if (!postId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Post ID is required'
        },
        { status: 400 }
      );
    }

    // Optimized single query with all necessary includes (prevents N+1)
    const post = await prisma.forumPost.findUnique({
      where: {
        id: postId,
        isHidden: false // Only show visible posts
      },
      include: {
        author: {
          select: {
            id: true,
            email: true,
            name: true,
            image: true,
            profile: {
              select: {
                profilePictureUrl: true,
                forumReputation: true,
                forumPostCount: true,
                forumReplyCount: true,
                currentCareerPath: true,
                progressLevel: true
              }
            }
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        },
        _count: {
          select: {
            replies: true,
            reactions: true,
            bookmarks: true
          }
        },
        // Load user's specific reactions and bookmarks if authenticated
        reactions: userId
          ? {
              where: {
                userId: userId
              },
              select: {
                type: true
              }
            }
          : {
              select: {
                type: true,
                userId: true
              }
            },
        bookmarks: userId
          ? {
              where: {
                userId: userId
              },
              select: {
                id: true
              }
            }
          : false,
        // Load recent replies with optimized includes
        replies: {
          where: {
            isHidden: false
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
                profile: {
                  select: {
                    profilePictureUrl: true,
                    forumReputation: true
                  }
                }
              }
            },
            _count: {
              select: {
                reactions: true
              }
            },
            reactions: userId
              ? {
                  where: {
                    userId: userId
                  },
                  select: {
                    type: true
                  }
                }
              : {
                  select: {
                    type: true,
                    userId: true
                  }
                }
          },
          orderBy: {
            createdAt: 'asc'
          },
          take: 50 // Limit replies to prevent huge payloads
        }
      }
    });

    if (!post) {
      return NextResponse.json(
        {
          success: false,
          error: 'Post not found'
        },
        { status: 404 }
      );
    }

    // Increment view count asynchronously (don't wait for it)
    if (userId && userId !== post.authorId) {
      prisma.forumPost
        .update({
          where: { id: postId },
          data: { viewCount: { increment: 1 } }
        })
        .catch(error => {
          console.error('Failed to increment view count:', error);
        });
    }

    return NextResponse.json({
      success: true,
      data: post
    });
  }
);

// PUT - Update forum post
export const PUT = withUnifiedErrorHandling(
  async (request: NextRequest, { params }: { params: Promise<{ postId: string }> }) => {
    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required'
        },
        { status: 401 }
      );
    }

    const { postId } = await params;
    const body = await request.json();
    const { title, content, categoryId } = body;

    if (!title?.trim() || !content?.trim()) {
      return NextResponse.json(
        {
          success: false,
          error: 'Title and content are required'
        },
        { status: 400 }
      );
    }

    // Check if user owns the post
    const existingPost = await prisma.forumPost.findUnique({
      where: { id: postId },
      select: { authorId: true }
    });

    if (!existingPost) {
      return NextResponse.json(
        {
          success: false,
          error: 'Post not found'
        },
        { status: 404 }
      );
    }

    if (existingPost.authorId !== session.user.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'You can only edit your own posts'
        },
        { status: 403 }
      );
    }

    // Update the post
    const updatedPost = await prisma.forumPost.update({
      where: { id: postId },
      data: {
        title: title.trim(),
        content: content.trim(),
        categoryId: categoryId || null,
        updatedAt: new Date()
      },
      include: {
        author: {
          select: {
            id: true,
            email: true,
            name: true,
            profile: {
              select: {
                profilePictureUrl: true,
                forumReputation: true
              }
            }
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        _count: {
          select: {
            replies: true,
            reactions: true,
            bookmarks: true
          }
        }
      }
    });

    return NextResponse.json({
      ...updatedPost,
      message: 'Post updated successfully'
    });
  }
);

// DELETE - Delete forum post
export const DELETE = withUnifiedErrorHandling(
  async (request: NextRequest, { params }: { params: Promise<{ postId: string }> }) => {
    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required'
        },
        { status: 401 }
      );
    }

    const { postId } = await params;

    // Check if user owns the post or is admin
    const existingPost = await prisma.forumPost.findUnique({
      where: { id: postId },
      select: {
        authorId: true
      }
    });

    if (!existingPost) {
      return NextResponse.json(
        {
          success: false,
          error: 'Post not found'
        },
        { status: 404 }
      );
    }

    const isAdmin = await isUserAdmin(session.user.id);
    const isOwner = existingPost.authorId === session.user.id;

    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        {
          success: false,
          error: 'You can only delete your own posts'
        },
        { status: 403 }
      );
    }

    // Soft delete by hiding the post
    await prisma.forumPost.update({
      where: { id: postId },
      data: {
        isHidden: true,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      message: 'Post deleted successfully'
    });
  }
);
