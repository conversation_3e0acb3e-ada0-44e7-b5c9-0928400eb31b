// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
import { NextResponse, NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';

interface ForumPostsResponse {
  success: boolean;
  data: {
    posts: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

// GET handler to retrieve forum posts
export const GET = withUnifiedErrorHandling(
  async (request: NextRequest): Promise<NextResponse<ForumPostsResponse>> => {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    const whereClause: any = {
      isHidden: false
    };

    if (categoryId) {
      whereClause.categoryId = categoryId;
    }

    // Get session for user-specific data
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    const posts = await prisma.forumPost.findMany({
      where: whereClause,
      include: {
        author: {
          select: {
            id: true,
            email: true,
            name: true,
            image: true,
            profile: {
              select: {
                profilePictureUrl: true,
                forumReputation: true,
                forumPostCount: true,
                currentCareerPath: true,
                progressLevel: true
              }
            }
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        },
        _count: {
          select: {
            replies: true,
            reactions: true,
            bookmarks: true
          }
        },
        // Only load user's own reactions if authenticated (reduces payload)
        reactions: userId
          ? {
              where: {
                userId: userId
              },
              select: {
                type: true
              }
            }
          : false,
        // Only load user's bookmarks if authenticated
        bookmarks: userId
          ? {
              where: {
                userId: userId
              },
              select: {
                id: true
              }
            }
          : false
      },
      orderBy: [{ isPinned: 'desc' }, { createdAt: 'desc' }],
      skip,
      take: limit
    });

    const totalPosts = await prisma.forumPost.count({
      where: whereClause
    });

    return NextResponse.json({
      success: true,
      data: {
        posts,
        pagination: {
          page,
          limit,
          total: totalPosts,
          pages: Math.ceil(totalPosts / limit)
        }
      }
    });
  }
);

// POST handler to create a new forum post
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !session.user.id) {
      const error = new Error('Unauthorized') as any;
      error.statusCode = 401;
      throw error;
    }

    const { title, content, categoryId } = await request.json();

    if (!title || !content) {
      const error = new Error('Title and content are required') as any;
      error.statusCode = 400;
      throw error;
    }

    if (title.length > 200) {
      const error = new Error('Title must be 200 characters or less') as any;
      error.statusCode = 400;
      throw error;
    }

    if (content.length > 5000) {
      const error = new Error('Content must be 5000 characters or less') as any;
      error.statusCode = 400;
      throw error;
    }

    // Validate category if provided
    if (categoryId) {
      const category = await prisma.forumCategory.findUnique({
        where: { id: categoryId }
      });
      if (!category) {
        const error = new Error('Invalid category') as any;
        error.statusCode = 400;
        throw error;
      }
    }

    // Use transaction to create post and update user profile
    const result = await prisma.$transaction(async tx => {
      const newPost = await tx.forumPost.create({
        data: {
          title: title.trim(),
          content: content.trim(),
          authorId: session.user?.id!,
          categoryId: categoryId || null
        },
        include: {
          author: {
            select: {
              id: true,
              email: true,
              name: true,
              profile: {
                select: {
                  profilePictureUrl: true,
                  forumReputation: true,
                  forumPostCount: true,
                  currentCareerPath: true,
                  progressLevel: true
                }
              }
            }
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          _count: {
            select: {
              replies: true,
              reactions: true,
              bookmarks: true
            }
          }
        }
      });

      // Update user's forum post count and reputation
      await tx.profile.upsert({
        where: { userId: session.user?.id! },
        update: {
          forumPostCount: { increment: 1 },
          forumReputation: { increment: 1 } // 1 point for creating a post
        },
        create: {
          userId: session.user?.id!,
          forumPostCount: 1,
          forumReputation: 1
        }
      });

      // Update category post count if category is specified
      if (categoryId) {
        await tx.forumCategory.update({
          where: { id: categoryId },
          data: {
            postCount: { increment: 1 },
            lastPostAt: new Date(),
            lastPostBy: session.user?.id!
          }
        });
      }

      return newPost;
    });

    return NextResponse.json(result);
  });
});
