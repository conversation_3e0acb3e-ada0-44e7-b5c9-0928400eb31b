import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';

interface ForumCategoriesResponse {
  id: string;
  name: string;
  description: string;
  postCount: number;
  replyCount: number;
  color: string;
  children: any[];
}

export const GET = withUnifiedErrorHandling(
  async (_request: NextRequest): Promise<NextResponse<ForumCategoriesResponse[]>> => {
    // Fetch categories from database with post counts
    const categories = await prisma.forumCategory.findMany({
      include: {
        _count: {
          select: {
            posts: true
          }
        },
        posts: {
          include: {
            _count: {
              select: {
                replies: true
              }
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Transform to match the expected response format
    const formattedCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description || '',
      postCount: category._count.posts,
      replyCount: category.posts.reduce((total, post) => total + post._count.replies, 0),
      color: category.color || 'blue',
      children: []
    }));

    return NextResponse.json(formattedCategories);
  }
);
