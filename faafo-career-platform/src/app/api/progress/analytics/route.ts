// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';

// Force dynamic rendering for this route

interface ProgressAnalyticsResponse {
  goalStats: {
    total: number;
    completed: number;
    active: number;
    completionRate: number;
    averageTimeToComplete: number;
  };
  categoryBreakdown: Array<{
    category: string;
    count: number;
    completed: number;
    percentage: number;
  }>;
  monthlyProgress: Array<{
    month: string;
    goalsCreated: number;
    goalsCompleted: number;
    learningHours: number;
  }>;
  streakData: {
    currentStreak: number;
    longestStreak: number;
    totalActiveDays: number;
  };
  achievements: {
    total: number;
    recent: Array<{
      id: string;
      title: string;
      unlockedAt: string;
      points: number;
    }>;
  };
  insights: Array<{
    type: string;
    title: string;
    description: string;
    action?: string;
  }>;
}

// GET /api/progress/analytics - Get user progress analytics
export const GET = withUnifiedErrorHandling(
  async (
    _request: NextRequest
  ): Promise<NextResponse<{ success: true; data: ProgressAnalyticsResponse }>> => {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      const error = new Error('Authentication required') as any;
      error.statusCode = 401;
      throw error;
    }

    // Note: Date range filtering not implemented yet - showing all data
    // TODO: Implement date filtering for analytics queries
    // const { searchParams } = new URL(request.url);
    // const range = searchParams.get('range') || '6months';

    const now = new Date(); // Still needed for monthly progress calculations

    // Fetch user goals (all goals, not filtered by date)
    const goals = await prisma.userGoal.findMany({
      where: {
        userId: session.user.id
      },
      orderBy: { createdAt: 'desc' }
    });

    // Fetch user achievements (all achievements, not filtered by date)
    const userAchievements = await prisma.userAchievement.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        achievement: {
          select: {
            id: true,
            title: true,
            points: true
          }
        }
      },
      orderBy: { unlockedAt: 'desc' }
    });

    // Fetch assessments for streak calculation (all assessments, not filtered by date)
    const assessments = await prisma.assessment.findMany({
      where: {
        userId: session.user.id,
        status: 'COMPLETED'
      },
      orderBy: { updatedAt: 'desc' }
    });

    // Calculate goal statistics
    const totalGoals = goals.length;
    const completedGoals = goals.filter(goal => goal.status === 'COMPLETED').length;
    const activeGoals = goals.filter(goal => goal.status === 'ACTIVE').length;
    const completionRate = totalGoals > 0 ? Math.round((completedGoals / totalGoals) * 100) : 0;

    // Calculate average time to complete (using updatedAt as completion time)
    const completedGoalsWithTime = goals.filter(goal => goal.status === 'COMPLETED');
    const averageTimeToComplete =
      completedGoalsWithTime.length > 0
        ? Math.round(
            completedGoalsWithTime.reduce((sum, goal) => {
              const timeDiff = goal.updatedAt.getTime() - goal.createdAt.getTime();
              return sum + timeDiff / (1000 * 60 * 60 * 24); // Convert to days
            }, 0) / completedGoalsWithTime.length
          )
        : 0;

    // Calculate category breakdown
    const categoryBreakdown = goals.reduce(
      (acc, goal) => {
        const category = goal.category;
        if (!acc[category]) {
          acc[category] = { count: 0, completed: 0 };
        }
        acc[category].count++;
        if (goal.status === 'COMPLETED') {
          acc[category].completed++;
        }
        return acc;
      },
      {} as Record<string, { count: number; completed: number }>
    );

    const categoryBreakdownArray = Object.entries(categoryBreakdown).map(([category, data]) => ({
      category,
      count: data.count,
      completed: data.completed,
      percentage: data.count > 0 ? Math.round((data.completed / data.count) * 100) : 0
    }));

    // Calculate monthly progress
    const monthlyProgress = [];
    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);

      const monthGoalsCreated = goals.filter(
        goal => goal.createdAt >= monthStart && goal.createdAt <= monthEnd
      ).length;

      const monthGoalsCompleted = goals.filter(
        goal =>
          goal.status === 'COMPLETED' && goal.updatedAt >= monthStart && goal.updatedAt <= monthEnd
      ).length;

      const monthLearningHours =
        assessments.filter(
          assessment => assessment.updatedAt >= monthStart && assessment.updatedAt <= monthEnd
        ).length * 2; // Estimate 2 hours per assessment

      monthlyProgress.push({
        month: monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        goalsCreated: monthGoalsCreated,
        goalsCompleted: monthGoalsCompleted,
        learningHours: monthLearningHours
      });
    }

    // Calculate streak data
    const uniqueDates = Array.from(
      new Set(assessments.map(assessment => assessment.updatedAt.toDateString()))
    ).sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

    let currentStreak = 0;
    let longestStreak = 0;
    let tempStreak = 0;

    // Calculate current streak (matching progress tracker logic)
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();

    if (uniqueDates.includes(today) || uniqueDates.includes(yesterday)) {
      let checkDate = new Date();
      while (uniqueDates.includes(checkDate.toDateString())) {
        currentStreak++;
        checkDate = new Date(checkDate.getTime() - 24 * 60 * 60 * 1000);
      }
    }

    // Calculate longest streak
    for (let i = 0; i < uniqueDates.length; i++) {
      if (i === 0) {
        tempStreak = 1;
      } else {
        const currentDate = new Date(uniqueDates[i]!);
        const previousDate = new Date(uniqueDates[i - 1]!);
        const dayDiff = (previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);

        if (dayDiff === 1) {
          tempStreak++;
        } else {
          longestStreak = Math.max(longestStreak, tempStreak);
          tempStreak = 1;
        }
      }
    }
    longestStreak = Math.max(longestStreak, tempStreak);

    // Generate insights
    const insights = [];

    if (completionRate >= 80) {
      insights.push({
        type: 'success',
        title: 'Excellent Goal Achievement!',
        description: `You have a ${completionRate}% goal completion rate. Keep up the great work!`
      });
    } else if (completionRate < 50) {
      insights.push({
        type: 'warning',
        title: 'Goal Completion Needs Attention',
        description: `Your goal completion rate is ${completionRate}%. Consider setting more achievable goals or breaking large goals into smaller tasks.`,
        action: 'Try setting smaller, more specific goals to build momentum.'
      });
    }

    if (currentStreak >= 7) {
      insights.push({
        type: 'success',
        title: 'Amazing Learning Streak!',
        description: `You've been consistently learning for ${currentStreak} days. This is building great habits!`
      });
    } else if (currentStreak === 0) {
      insights.push({
        type: 'info',
        title: 'Time to Get Back on Track',
        description:
          "You haven't logged any learning activity recently. Even 15 minutes a day can make a difference!",
        action: 'Set a small daily learning goal to rebuild your streak.'
      });
    }

    if (activeGoals === 0) {
      insights.push({
        type: 'info',
        title: 'No Active Goals',
        description:
          "You don't have any active goals. Setting clear objectives can help guide your learning journey.",
        action: 'Create a new goal to focus your learning efforts.'
      });
    } else if (activeGoals > 5) {
      insights.push({
        type: 'warning',
        title: 'Many Active Goals',
        description: `You have ${activeGoals} active goals. Consider focusing on fewer goals for better results.`,
        action: 'Try focusing on 2-3 key goals at a time.'
      });
    }

    const analyticsData = {
      goalStats: {
        total: totalGoals,
        completed: completedGoals,
        active: activeGoals,
        completionRate,
        averageTimeToComplete
      },
      categoryBreakdown: categoryBreakdownArray,
      monthlyProgress,
      streakData: {
        currentStreak,
        longestStreak,
        totalActiveDays: uniqueDates.length
      },
      achievements: {
        total: userAchievements.length,
        recent: userAchievements.slice(0, 5).map(ua => ({
          id: ua.achievement.id,
          title: ua.achievement.title,
          unlockedAt: ua.unlockedAt.toISOString(),
          points: ua.achievement.points
        }))
      },
      insights
    };

    return NextResponse.json({
      success: true as const,
      data: analyticsData
    });
  }
);
