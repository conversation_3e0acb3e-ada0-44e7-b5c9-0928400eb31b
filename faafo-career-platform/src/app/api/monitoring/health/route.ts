import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
import { errorAnalytics } from '@/lib/monitoring/error-analytics';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const revalidate = false;

/**
 * Production Health Monitoring Endpoint
 * 
 * Provides comprehensive health checks for all system components
 */

interface HealthCheck {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  details?: any;
  error?: string;
}

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  checks: HealthCheck[];
  metrics: {
    errorRate: number;
    responseTime: number;
    memoryUsage: number;
    cacheHitRate: number;
  };
}

/**
 * Check database connectivity and performance with emergency caching
 */
async function checkDatabase(): Promise<HealthCheck> {
  const startTime = Date.now();

  try {
    // Import emergency cache
    const { cachedDbOps } = await import('@/lib/database/emergency-cache');

    // Test basic connectivity with optimized query
    await prisma.$queryRaw`SELECT 1`;

    // Use cached user count to reduce database load
    const userCount = await cachedDbOps.getUserCount(prisma);

    const responseTime = Date.now() - startTime;

    // More aggressive thresholds for better performance
    const status = responseTime < 50 ? 'healthy' :
                   responseTime < 200 ? 'degraded' : 'unhealthy';

    return {
      name: 'database',
      status,
      responseTime,
      details: {
        userCount,
        connectionPool: 'active',
        cached: true,
        optimized: true
      }
    };
  } catch (error) {
    return {
      name: 'database',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Check cache system health
 */
async function checkCache(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    // Test cache write/read
    const testKey = `health_check_${Date.now()}`;
    const testValue = { timestamp: Date.now() };
    
    await consolidatedCache.set(testKey, testValue, { ttl: 5000 });
    const retrieved = await consolidatedCache.get(testKey);
    
    const responseTime = Date.now() - startTime;
    const metrics = consolidatedCache.getMetrics();
    
    // Clean up test key
    await consolidatedCache.delete(testKey);
    
    const isHealthy = retrieved && responseTime < 50;
    
    return {
      name: 'cache',
      status: isHealthy ? 'healthy' : responseTime < 200 ? 'degraded' : 'unhealthy',
      responseTime,
      details: {
        hitRate: metrics.hitRate,
        memoryUsage: metrics.memoryUsage,
        totalRequests: metrics.totalRequests
      }
    };
  } catch (error) {
    return {
      name: 'cache',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Check external API dependencies
 */
async function checkExternalAPIs(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    // Test external API connectivity (placeholder)
    // In a real implementation, you'd test actual external services
    const checks = await Promise.allSettled([
      // Example: fetch('https://api.external-service.com/health'),
      Promise.resolve({ ok: true }) // Placeholder
    ]);
    
    const responseTime = Date.now() - startTime;
    const allHealthy = checks.every(check => 
      check.status === 'fulfilled' && check.value
    );
    
    return {
      name: 'external_apis',
      status: allHealthy ? 'healthy' : 'degraded',
      responseTime,
      details: {
        checkedServices: checks.length,
        healthyServices: checks.filter(c => c.status === 'fulfilled').length
      }
    };
  } catch (error) {
    return {
      name: 'external_apis',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Check system resources
 */
function checkSystemResources(): HealthCheck {
  const startTime = Date.now();
  
  try {
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();
    
    // Calculate memory usage percentage (rough estimate)
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    
    const status = memoryUsagePercent < 80 ? 'healthy' : 
                   memoryUsagePercent < 95 ? 'degraded' : 'unhealthy';
    
    return {
      name: 'system_resources',
      status,
      responseTime: Date.now() - startTime,
      details: {
        memoryUsage: {
          used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          percentage: Math.round(memoryUsagePercent)
        },
        uptime: Math.round(uptime),
        nodeVersion: process.version
      }
    };
  } catch (error) {
    return {
      name: 'system_resources',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Get error analytics metrics
 */
function getErrorMetrics(): any {
  try {
    const metrics = errorAnalytics.getErrorMetrics(3600000); // Last hour
    
    return {
      errorRate: metrics.errorRate,
      totalErrors: metrics.totalErrors,
      criticalErrors: metrics.criticalErrors,
      affectedUsers: metrics.userImpact.affectedUsers
    };
  } catch (error) {
    return {
      errorRate: 0,
      totalErrors: 0,
      criticalErrors: 0,
      affectedUsers: 0,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Main health check endpoint
 */
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const startTime = Date.now();
  
  try {
    // Run all health checks in parallel
    const [
      databaseCheck,
      cacheCheck,
      externalAPIsCheck,
      systemResourcesCheck
    ] = await Promise.all([
      checkDatabase(),
      checkCache(),
      checkExternalAPIs(),
      checkSystemResources()
    ]);
    
    const checks = [
      databaseCheck,
      cacheCheck,
      externalAPIsCheck,
      systemResourcesCheck
    ];
    
    // Determine overall system status
    const hasUnhealthy = checks.some(check => check.status === 'unhealthy');
    const hasDegraded = checks.some(check => check.status === 'degraded');
    
    const overallStatus = hasUnhealthy ? 'unhealthy' : 
                         hasDegraded ? 'degraded' : 'healthy';
    
    // Get error metrics
    const errorMetrics = getErrorMetrics();
    
    // Calculate average response time
    const avgResponseTime = checks.reduce((sum, check) => sum + check.responseTime, 0) / checks.length;
    
    // Get cache metrics
    const cacheMetrics = consolidatedCache.getMetrics();
    
    const healthData: SystemHealth = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env['npm_package_version'] || '1.0.0',
      environment: process.env['NODE_ENV'] || 'development',
      checks,
      metrics: {
        errorRate: errorMetrics.errorRate,
        responseTime: Math.round(avgResponseTime),
        memoryUsage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100),
        cacheHitRate: Math.round(cacheMetrics.hitRate * 100)
      }
    };
    
    // Set appropriate HTTP status code
    const httpStatus = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;
    
    return NextResponse.json(healthData, { status: httpStatus });
    
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error),
      uptime: process.uptime(),
      environment: process.env['NODE_ENV'] || 'development'
    }, { status: 503 });
  }
});

/**
 * Detailed health check with extended diagnostics
 */
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  try {
    const body = await request.json();
    const { includeDetails = false, timeWindow = 3600000 } = body;
    
    // Get basic health data
    const healthResponse = await GET(request);
    const healthData = await healthResponse.json();
    
    if (!includeDetails) {
      return NextResponse.json(healthData);
    }
    
    // Add extended diagnostics
    const extendedData = {
      ...healthData,
      diagnostics: {
        errorAnalytics: errorAnalytics.getDashboardData(timeWindow),
        systemInfo: {
          platform: process.platform,
          arch: process.arch,
          nodeVersion: process.version,
          pid: process.pid
        },
        environmentVariables: {
          nodeEnv: process.env['NODE_ENV'],
          hasDatabase: !!process.env['DATABASE_URL'],
          hasRedis: !!process.env['REDIS_URL'],
          hasSentry: !!process.env['NEXT_PUBLIC_SENTRY_DSN']
        }
      }
    };
    
    return NextResponse.json(extendedData);
    
  } catch (error) {
    console.error('Extended health check failed:', error);
    return NextResponse.json({
      error: 'Extended health check failed',
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
});
