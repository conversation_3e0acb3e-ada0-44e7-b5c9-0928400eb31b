// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';

// GET /api/learning-resources/bookmarks - Get user's bookmarked learning resources
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  const skip = (page - 1) * limit;

  // Get user's bookmarked learning resources
  const bookmarks = await prisma.userLearningProgress.findMany({
    where: {
      userId: session.user.id,
      status: 'BOOKMARKED'
    },
    include: {
      resource: {
        include: {
          ratings: {
            select: {
              rating: true
            }
          },
          careerPaths: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          skills: {
            select: {
              id: true,
              name: true
            }
          }
        }
      }
    },
    orderBy: {
      updatedAt: 'desc'
    },
    skip,
    take: limit
  });

  // Get total count for pagination
  const totalCount = await prisma.userLearningProgress.count({
    where: {
      userId: session.user.id,
      status: 'BOOKMARKED'
    }
  });

  // Format the response
  const formattedBookmarks = bookmarks.map(bookmark => {
    const resource = bookmark.resource;
    const averageRating = resource.ratings.length > 0
      ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length
      : 0;

    return {
      id: bookmark.id,
      bookmarkedAt: bookmark.updatedAt,
      resource: {
        ...resource,
        averageRating: Math.round(averageRating * 10) / 10,
        totalRatings: resource.ratings.length,
        isBookmarked: true
      }
    };
  });

  return NextResponse.json({
    bookmarks: formattedBookmarks,
    pagination: {
      page,
      limit,
      total: totalCount,
      totalPages: Math.ceil(totalCount / limit),
      hasNext: page * limit < totalCount,
      hasPrev: page > 1
    }
  });
});

// POST /api/learning-resources/bookmarks - Add bookmark
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }

    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      const error = new Error('Authentication required') as any;
      error.statusCode = 401;
      throw error;
    }

    const body = await request.json();
    const { resourceId } = body;

    if (!resourceId) {
      const error = new Error('Resource ID is required') as any;
      error.statusCode = 400;
      throw error;
    }

    // Check if resource exists
    const resource = await prisma.learningResource.findUnique({
      where: { id: resourceId }
    });

    if (!resource) {
      const error = new Error('Learning resource not found') as any;
      error.statusCode = 404;
      throw error;
    }

    // Check if bookmark already exists
    const existingProgress = await prisma.userLearningProgress.findUnique({
      where: {
        userId_resourceId: {
          userId: session.user.id,
          resourceId
        }
      }
    });

    // If already bookmarked, remove the bookmark (toggle behavior)
    if (existingProgress && existingProgress.status === 'BOOKMARKED') {
      await prisma.userLearningProgress.update({
        where: {
          userId_resourceId: {
            userId: session.user.id,
            resourceId
          }
        },
        data: {
          status: 'NOT_STARTED',
          updatedAt: new Date()
        }
      });

      return NextResponse.json({
        message: 'Resource bookmark removed successfully',
        bookmark: null,
        bookmarked: false
      });
    }

    // Create or update bookmark
    const bookmark = await prisma.userLearningProgress.upsert({
      where: {
        userId_resourceId: {
          userId: session.user.id,
          resourceId
        }
      },
      update: {
        status: 'BOOKMARKED',
        updatedAt: new Date()
      },
      create: {
        userId: session.user.id,
        resourceId,
        status: 'BOOKMARKED'
      },
      include: {
        resource: {
          select: {
            id: true,
            title: true
          }
        }
      }
    });

    return NextResponse.json({
      message: 'Resource bookmarked successfully',
      bookmark,
      bookmarked: true
    });
  });
});

// DELETE /api/learning-resources/bookmarks - Remove bookmark
export const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }

    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      const error = new Error('Authentication required') as any;
      error.statusCode = 401;
      throw error;
    }

    const body = await request.json();
    const { resourceId } = body;

    if (!resourceId) {
      const error = new Error('Resource ID is required') as any;
      error.statusCode = 400;
      throw error;
    }

    // Find the bookmark
    const bookmark = await prisma.userLearningProgress.findUnique({
      where: {
        userId_resourceId: {
          userId: session.user.id,
          resourceId
        }
      }
    });

    if (!bookmark || bookmark.status !== 'BOOKMARKED') {
      const error = new Error('Bookmark not found') as any;
      error.statusCode = 404;
      throw error;
    }

    // Update status to NOT_STARTED instead of deleting
    await prisma.userLearningProgress.update({
      where: {
        userId_resourceId: {
          userId: session.user.id,
          resourceId
        }
      },
      data: {
        status: 'NOT_STARTED',
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      message: 'Bookmark removed successfully',
      bookmarked: false
    });
  });
});
