#!/usr/bin/env tsx
/**
 * Cache warming script for Skill Gap Analyzer
 * Preloads popular skills and common data to improve performance
 */

import { skillGapPerformanceMonitor } from '@/lib/performance/skill-gap-performance';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
import prisma from '@/lib/prisma';

interface PopularSkill {
  id: string;
  name: string;
  category: string;
  searchCount: number;
  marketData?: any;
}

async function getPopularSkills(): Promise<PopularSkill[]> {
  try {
    // Get skills with market data and high demand
    const skills = await prisma.skill.findMany({
      include: {
        marketData: {
          where: { isActive: true },
          orderBy: { dataDate: 'desc' },
          take: 1
        },
        _count: {
          select: {
            assessments: true,
            learningResources: true
          }
        }
      },
      orderBy: [{ assessments: { _count: 'desc' } }, { name: 'asc' }],
      take: 50
    });

    return skills.map(skill => ({
      id: skill.id,
      name: skill.name,
      category: skill.category || 'General',
      searchCount: skill._count.assessments,
      marketData: skill.marketData[0] || null
    }));
  } catch (error) {
    console.error('Error fetching popular skills:', error);
    return [];
  }
}

async function warmSkillSearchCache(): Promise<void> {
  console.log('🔥 Warming skill search cache...');

  const popularSkills = await getPopularSkills();
  const searchTerms = [
    // Programming languages
    'javascript',
    'python',
    'java',
    'typescript',
    'c++',
    'c#',
    'go',
    'rust',
    // Frontend frameworks
    'react',
    'vue',
    'angular',
    'svelte',
    'next.js',
    'nuxt',
    // Backend technologies
    'node.js',
    'express',
    'django',
    'flask',
    'spring',
    'laravel',
    // Databases
    'sql',
    'mysql',
    'postgresql',
    'mongodb',
    'redis',
    'elasticsearch',
    // Cloud platforms
    'aws',
    'azure',
    'gcp',
    'docker',
    'kubernetes',
    'terraform',
    // Soft skills
    'leadership',
    'communication',
    'project management',
    'agile'
  ];

  let warmedCount = 0;

  for (const term of searchTerms) {
    try {
      const cacheKey = `skill_search:${term.toLowerCase()}`;

      // Check if already cached
      const existing = await consolidatedCache.get<any>(cacheKey);
      if (existing) {
        continue;
      }

      // Find matching skills
      const matchingSkills = popularSkills.filter(
        skill =>
          skill.name.toLowerCase().includes(term.toLowerCase()) ||
          skill.category.toLowerCase().includes(term.toLowerCase())
      );

      if (matchingSkills.length > 0) {
        await consolidatedCache.set(cacheKey, matchingSkills, {
          ttl: 300000,
          tags: ['skill_search']
        }); // 5 minutes
        warmedCount++;
        console.log(`  ✓ Cached search results for "${term}" (${matchingSkills.length} results)`);
      }
    } catch (error) {
      console.error(`  ✗ Failed to cache search for "${term}":`, error);
    }
  }

  console.log(`🔥 Skill search cache warming completed: ${warmedCount} terms cached`);
}

async function warmPopularSkillData(): Promise<void> {
  console.log('🔥 Warming popular skill data cache...');

  const popularSkills = await getPopularSkills();
  let warmedCount = 0;

  for (const skill of popularSkills.slice(0, 20)) {
    // Top 20 skills
    try {
      const cacheKey = `skill_data:${skill.name.toLowerCase()}`;

      // Check if already cached
      const existing = await consolidatedCache.get<any>(cacheKey);
      if (existing) {
        continue;
      }

      const skillData = {
        id: skill.id,
        name: skill.name,
        category: skill.category,
        popular: true,
        searchCount: skill.searchCount,
        marketData: skill.marketData,
        warmedAt: Date.now()
      };

      await consolidatedCache.set(cacheKey, skillData, { ttl: 7200000, tags: ['skill_data'] }); // 2 hours
      warmedCount++;
      console.log(`  ✓ Cached data for "${skill.name}"`);
    } catch (error) {
      console.error(`  ✗ Failed to cache data for "${skill.name}":`, error);
    }
  }

  console.log(`🔥 Popular skill data warming completed: ${warmedCount} skills cached`);
}

async function warmCommonCareerPaths(): Promise<void> {
  console.log('🔥 Warming common career paths cache...');

  try {
    const careerPaths = await prisma.careerPath.findMany({
      include: {
        relatedSkills: {
          take: 10
        },
        _count: {
          select: {
            skillGapAnalyses: true
          }
        }
      },
      orderBy: [{ skillGapAnalyses: { _count: 'desc' } }, { name: 'asc' }],
      take: 10
    });

    let warmedCount = 0;

    for (const careerPath of careerPaths) {
      try {
        const cacheKey = `career_path:${careerPath.name.toLowerCase().replace(/\s+/g, '-')}`;

        // Check if already cached
        const existing = await consolidatedCache.get<any>(cacheKey);
        if (existing) {
          continue;
        }

        const pathData = {
          id: careerPath.id,
          name: careerPath.name,
          description: careerPath.overview,
          requiredSkills: careerPath.relatedSkills.map(skill => ({
            id: skill.id,
            name: skill.name,
            category: skill.category
          })),
          analysisCount: careerPath._count.skillGapAnalyses,
          warmedAt: Date.now()
        };

        await consolidatedCache.set(cacheKey, pathData, { ttl: 3600000, tags: ['career_path'] }); // 1 hour
        warmedCount++;
        console.log(`  ✓ Cached career path "${careerPath.name}"`);
      } catch (error) {
        console.error(`  ✗ Failed to cache career path "${careerPath.name}":`, error);
      }
    }

    console.log(`🔥 Career paths warming completed: ${warmedCount} paths cached`);
  } catch (error) {
    console.error('Error warming career paths cache:', error);
  }
}

async function warmSkillAssessmentTemplates(): Promise<void> {
  console.log('🔥 Warming skill assessment templates cache...');

  const templates = [
    {
      type: 'beginner_assessment',
      skills: ['HTML', 'CSS', 'JavaScript', 'Git'],
      description: 'Basic web development skills assessment'
    },
    {
      type: 'frontend_assessment',
      skills: ['React', 'TypeScript', 'CSS', 'JavaScript', 'HTML'],
      description: 'Frontend developer skills assessment'
    },
    {
      type: 'backend_assessment',
      skills: ['Node.js', 'Python', 'SQL', 'REST APIs', 'Docker'],
      description: 'Backend developer skills assessment'
    },
    {
      type: 'fullstack_assessment',
      skills: ['React', 'Node.js', 'TypeScript', 'SQL', 'AWS'],
      description: 'Full-stack developer skills assessment'
    }
  ];

  let warmedCount = 0;

  for (const template of templates) {
    try {
      const cacheKey = `assessment_template:${template.type}`;

      // Check if already cached
      const existing = await consolidatedCache.get<any>(cacheKey);
      if (existing) {
        continue;
      }

      await consolidatedCache.set(cacheKey, template, {
        ttl: 1800000,
        tags: ['assessment_template']
      }); // 30 minutes
      warmedCount++;
      console.log(`  ✓ Cached assessment template "${template.type}"`);
    } catch (error) {
      console.error(`  ✗ Failed to cache template "${template.type}":`, error);
    }
  }

  console.log(`🔥 Assessment templates warming completed: ${warmedCount} templates cached`);
}

async function performHealthCheck(): Promise<void> {
  console.log('🏥 Performing health check...');

  try {
    const isHealthy = await skillGapPerformanceMonitor.healthCheck();
    const performanceStatus = skillGapPerformanceMonitor.getPerformanceStatus();

    console.log(`  Cache Health: ${isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);
    console.log(
      `  Performance Health: ${performanceStatus.isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`
    );

    if (performanceStatus.healthScore) {
      console.log(`  Health Score: ${performanceStatus.healthScore}%`);
    }
  } catch (error) {
    console.error('  ❌ Health check failed:', error);
  }
}

async function main(): Promise<void> {
  console.log('🚀 Starting Skill Gap Analyzer cache warming...\n');

  const startTime = Date.now();

  try {
    // Warm different cache layers
    await warmSkillSearchCache();
    console.log('');

    await warmPopularSkillData();
    console.log('');

    await warmCommonCareerPaths();
    console.log('');

    await warmSkillAssessmentTemplates();
    console.log('');

    // Use the performance monitor's cache warming
    await skillGapPerformanceMonitor.warmPopularSkillsCache();
    console.log('');

    // Perform health check
    await performHealthCheck();

    const duration = Date.now() - startTime;
    console.log(`\n✅ Cache warming completed successfully in ${duration}ms`);
  } catch (error) {
    console.error('\n❌ Cache warming failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { main as warmSkillCache };
