'use client';

import { useEffect } from 'react';
import Head from 'next/head';

interface IndexingOptimizerProps {
  title: string;
  description: string;
  canonical?: string;
  keywords?: string[];
  structuredData?: object;
  noindex?: boolean;
  priority?: 'high' | 'medium' | 'low';
}

/**
 * IndexingOptimizer Component
 * Optimizes pages for better Google indexing by adding comprehensive meta tags,
 * structured data, and indexing hints
 */
export function IndexingOptimizer({
  title,
  description,
  canonical,
  keywords = [],
  structuredData,
  noindex = false,
  priority = 'medium'
}: IndexingOptimizerProps) {
  const baseUrl = process.env['NEXT_PUBLIC_BASE_URL'] || 'https://www.faafocareer.com';
  const fullCanonical = canonical || (typeof window !== 'undefined' ? window.location.href : baseUrl);

  useEffect(() => {
    // Add structured data to page
    if (structuredData) {
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.text = JSON.stringify(structuredData);
      script.id = 'structured-data';
      
      // Remove existing structured data
      const existing = document.getElementById('structured-data');
      if (existing) {
        existing.remove();
      }
      
      document.head.appendChild(script);
    }

    // Add indexing hints based on priority
    const indexingHints = document.createElement('meta');
    indexingHints.name = 'robots';
    
    if (noindex) {
      indexingHints.content = 'noindex, nofollow';
    } else {
      const robotsContent = ['index', 'follow'];
      
      // Add priority-based hints
      if (priority === 'high') {
        robotsContent.push('max-snippet:-1', 'max-image-preview:large', 'max-video-preview:-1');
      }
      
      indexingHints.content = robotsContent.join(', ');
    }
    
    // Remove existing robots meta
    const existingRobots = document.querySelector('meta[name="robots"]');
    if (existingRobots) {
      existingRobots.remove();
    }
    
    document.head.appendChild(indexingHints);

    // Add last modified hint for better crawling
    const lastModified = document.createElement('meta');
    lastModified.httpEquiv = 'last-modified';
    lastModified.content = new Date().toISOString();
    document.head.appendChild(lastModified);

    // Cleanup function
    return () => {
      const structuredDataScript = document.getElementById('structured-data');
      if (structuredDataScript) {
        structuredDataScript.remove();
      }
    };
  }, [structuredData, noindex, priority]);

  return (
    <Head>
      {/* Essential meta tags for indexing */}
      <title>{title}</title>
      <meta name="description" content={description} />
      
      {/* Keywords for better relevance */}
      {keywords.length > 0 && (
        <meta name="keywords" content={keywords.join(', ')} />
      )}
      
      {/* Canonical URL to prevent duplicate content */}
      <link rel="canonical" href={fullCanonical} />
      
      {/* Open Graph for social sharing and indexing */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:url" content={fullCanonical} />
      <meta property="og:type" content="website" />
      <meta property="og:site_name" content="FAAFO Career Platform" />
      
      {/* Twitter Card for better social indexing */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:site" content="@faafo_platform" />
      
      {/* Additional indexing hints */}
      <meta name="author" content="FAAFO Career Platform" />
      <meta name="publisher" content="FAAFO Career Platform" />
      <meta name="language" content="en" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Mobile optimization for better indexing */}
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="mobile-web-app-capable" content="yes" />
      
      {/* Preconnect to important domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* DNS prefetch for faster loading */}
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    </Head>
  );
}

// Pre-defined structured data schemas for common page types
export const commonSchemas = {
  organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    'name': 'FAAFO Career Platform',
    'url': 'https://www.faafocareer.com',
    'logo': 'https://www.faafocareer.com/logo-faafo.png',
    'description': 'Free career development platform helping professionals transition to their dream careers',
    'sameAs': [
      'https://twitter.com/faafo_platform',
      'https://linkedin.com/company/faafo-career'
    ]
  },
  
  website: {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    'name': 'FAAFO Career Platform',
    'url': 'https://www.faafocareer.com',
    'description': 'Free career assessment, skill gap analysis & interview practice platform',
    'potentialAction': {
      '@type': 'SearchAction',
      'target': 'https://www.faafocareer.com/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  },
  
  webApplication: {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    'name': 'FAAFO Career Tools',
    'description': 'Comprehensive suite of career development tools',
    'url': 'https://www.faafocareer.com/tools',
    'applicationCategory': 'BusinessApplication',
    'operatingSystem': 'Web Browser'
  },
  
  educationalOrganization: {
    '@context': 'https://schema.org',
    '@type': 'EducationalOrganization',
    'name': 'FAAFO Career Resources',
    'description': 'Free educational resources for career development',
    'url': 'https://www.faafocareer.com/resources'
  }
};

// Helper function to generate page-specific structured data
export function generatePageSchema(pageType: string, pageData: any = {}) {
  const baseSchema = commonSchemas[pageType as keyof typeof commonSchemas];
  
  if (!baseSchema) {
    return commonSchemas.website;
  }
  
  return {
    ...baseSchema,
    ...pageData
  };
}

// Hook for easy integration
export function useIndexingOptimization(config: IndexingOptimizerProps) {
  useEffect(() => {
    // Force Google to recrawl the page by updating last-modified
    const event = new CustomEvent('pageUpdated', {
      detail: { timestamp: new Date().toISOString() }
    });
    window.dispatchEvent(event);
  }, [config.title, config.description]);
}

export default IndexingOptimizer;
