'use client';

import { useEffect } from 'react';
import Head from 'next/head';

interface EnhancedSEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  canonicalUrl?: string;
  structuredData?: object[];
  noindex?: boolean;
}

/**
 * Enhanced SEO component with advanced optimization features
 */
export default function EnhancedSEO({
  title,
  description,
  keywords = [],
  canonicalUrl,
  structuredData = [],
  noindex = false
}: EnhancedSEOProps) {
  useEffect(() => {
    // Add structured data to page
    if (structuredData.length > 0) {
      structuredData.forEach((data, index) => {
        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.id = `structured-data-${index}`;
        script.innerHTML = JSON.stringify(data);
        
        // Remove existing script if it exists
        const existing = document.getElementById(`structured-data-${index}`);
        if (existing) {
          existing.remove();
        }
        
        document.head.appendChild(script);
      });
    }

    // Cleanup function
    return () => {
      structuredData.forEach((_, index) => {
        const script = document.getElementById(`structured-data-${index}`);
        if (script) {
          script.remove();
        }
      });
    };
  }, [structuredData]);

  return (
    <Head>
      {title && <title>{title}</title>}
      {description && <meta name="description" content={description} />}
      {keywords.length > 0 && <meta name="keywords" content={keywords.join(', ')} />}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Enhanced meta tags for better SEO */}
      <meta name="author" content="FAAFO Career Platform Team" />
      <meta name="publisher" content="FAAFO Career Platform" />
      <meta name="copyright" content="© 2024 FAAFO Career Platform" />
      <meta name="language" content="en-US" />
      <meta name="revisit-after" content="7 days" />
      <meta name="distribution" content="global" />
      <meta name="rating" content="general" />
      <meta name="referrer" content="origin-when-cross-origin" />
      
      {/* Robots meta tag */}
      <meta 
        name="robots" 
        content={noindex ? 'noindex, nofollow' : 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1'} 
      />
      
      {/* Google-specific meta tags */}
      <meta name="googlebot" content={noindex ? 'noindex, nofollow' : 'index, follow'} />
      <meta name="google" content="notranslate" />
      
      {/* Additional SEO meta tags */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      
      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://www.googletagmanager.com" />
    </Head>
  );
}

/**
 * Generate comprehensive structured data for the homepage
 */
export function generateHomepageStructuredData() {
  const baseUrl = process.env['NEXT_PUBLIC_BASE_URL'] || 'https://faafo-career.com';
  
  return [
    // Website schema
    {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'FAAFO Career Platform',
      alternateName: 'FAAFO',
      url: baseUrl,
      description: 'Free career development platform helping professionals transition to fulfilling careers through assessments, skill analysis, and interview practice.',
      potentialAction: {
        '@type': 'SearchAction',
        target: `${baseUrl}/search?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      },
      publisher: {
        '@type': 'Organization',
        name: 'FAAFO Career Platform',
        url: baseUrl,
        logo: `${baseUrl}/images/logo.png`,
        sameAs: [
          'https://twitter.com/faafo_platform',
          'https://linkedin.com/company/faafo-career',
          'https://github.com/faafo-career'
        ]
      }
    },
    
    // Organization schema
    {
      '@context': 'https://schema.org',
      '@type': 'EducationalOrganization',
      name: 'FAAFO Career Platform',
      url: baseUrl,
      logo: `${baseUrl}/images/logo.png`,
      description: 'Leading career development platform offering free tools and resources for career transitions.',
      foundingDate: '2024',
      areaServed: 'Worldwide',
      serviceType: 'Career Development',
      offers: [
        {
          '@type': 'Offer',
          name: 'Free Career Assessment',
          description: 'Comprehensive career assessment to discover your ideal career path',
          price: '0',
          priceCurrency: 'USD',
          availability: 'https://schema.org/InStock',
          url: `${baseUrl}/assessment`
        },
        {
          '@type': 'Offer',
          name: 'Skill Gap Analysis',
          description: 'Identify skill gaps and get personalized learning recommendations',
          price: '0',
          priceCurrency: 'USD',
          availability: 'https://schema.org/InStock',
          url: `${baseUrl}/skills/gap-analyzer`
        },
        {
          '@type': 'Offer',
          name: 'Interview Practice',
          description: 'AI-powered interview practice with personalized feedback',
          price: '0',
          priceCurrency: 'USD',
          availability: 'https://schema.org/InStock',
          url: `${baseUrl}/interview-practice`
        }
      ],
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '******-FAAFO-HELP',
        contactType: 'Customer Service',
        email: '<EMAIL>',
        availableLanguage: 'English'
      }
    },
    
    // FAQ schema for common questions
    {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: [
        {
          '@type': 'Question',
          name: 'Is FAAFO Career Platform really free?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Yes! FAAFO Career Platform is completely free to use. All our tools including career assessment, skill gap analysis, and interview practice are available at no cost.'
          }
        },
        {
          '@type': 'Question',
          name: 'How accurate is the career assessment?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Our career assessment uses advanced AI algorithms and is based on validated psychological frameworks. It has helped over 10,000 professionals successfully transition to new careers.'
          }
        },
        {
          '@type': 'Question',
          name: 'How long does a career change typically take?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Career transitions typically take 3-12 months depending on your target field, current skills, and preparation level. Our platform helps accelerate this process with personalized guidance.'
          }
        }
      ]
    }
  ];
}

/**
 * Generate breadcrumb structured data
 */
export function generateBreadcrumbStructuredData(breadcrumbs: Array<{name: string, url: string}>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url
    }))
  };
}

/**
 * Generate article structured data for blog posts
 */
export function generateArticleStructuredData({
  title,
  description,
  url,
  datePublished,
  dateModified,
  authorName = 'FAAFO Career Team',
  image
}: {
  title: string;
  description: string;
  url: string;
  datePublished: string;
  dateModified?: string;
  authorName?: string;
  image?: string;
}) {
  const baseUrl = process.env['NEXT_PUBLIC_BASE_URL'] || 'https://faafo-career.com';
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description: description,
    url: url,
    datePublished: datePublished,
    dateModified: dateModified || datePublished,
    author: {
      '@type': 'Person',
      name: authorName,
      url: `${baseUrl}/about`
    },
    publisher: {
      '@type': 'Organization',
      name: 'FAAFO Career Platform',
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/images/logo.png`
      }
    },
    image: image ? {
      '@type': 'ImageObject',
      url: image,
      width: 1200,
      height: 630
    } : undefined,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': url
    }
  };
}
