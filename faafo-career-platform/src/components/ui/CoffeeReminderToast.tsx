'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Coffee, X, Heart, Zap, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CoffeeReminderEvent {
  stats: {
    totalCalls: number;
    totalCost: number;
    sessionsWithHighUsage: number;
  };
  features: Array<{ name: string; count: number }>;
  message: string;
}

interface CoffeeReminderToastProps {
  className?: string;
}

export default function CoffeeReminderToast({ className }: CoffeeReminderToastProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [reminderData, setReminderData] = useState<CoffeeReminderEvent | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const handleCoffeeReminder = (event: CustomEvent<CoffeeReminderEvent>) => {
      setReminderData(event.detail);
      setIsVisible(true);
      setIsAnimating(true);
      
      // Auto-hide after 15 seconds
      setTimeout(() => {
        handleDismiss();
      }, 15000);
    };

    window.addEventListener('faafo:coffee-reminder', handleCoffeeReminder as EventListener);

    return () => {
      window.removeEventListener('faafo:coffee-reminder', handleCoffeeReminder as EventListener);
    };
  }, []);

  const handleDismiss = () => {
    setIsAnimating(false);
    setTimeout(() => {
      setIsVisible(false);
      setReminderData(null);
    }, 300);
  };

  const handleCoffeeSupport = () => {
    window.open('https://ko-fi.com/faafo', '_blank');
    handleDismiss();
  };

  const getPersonalityMessage = () => {
    if (!reminderData) return '';
    
    const { stats } = reminderData;
    const messages = [
      "The AI servers are getting a workout! 💪",
      "You're really putting this platform through its paces! 🏃‍♂️",
      "Someone's been busy with career planning! 📈",
      "The algorithms are working overtime for you! ⚡",
      "You're getting serious value from this thing! 🎯"
    ];
    
    return messages[stats.sessionsWithHighUsage % messages.length];
  };

  if (!isVisible || !reminderData) return null;

  return (
    <div className={cn(
      "fixed bottom-4 right-4 z-50 max-w-sm transition-all duration-300 ease-in-out",
      isAnimating ? "translate-y-0 opacity-100" : "translate-y-full opacity-0",
      className
    )}>
      <Card className="border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950 dark:to-orange-950 shadow-lg">
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
                <Coffee className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-amber-900 dark:text-amber-100">
                  Coffee Break? ☕
                </h3>
                <Badge variant="secondary" className="text-xs">
                  {reminderData.stats.totalCalls} AI calls today
                </Badge>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="h-6 w-6 p-0 text-amber-600 hover:text-amber-800"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          <div className="space-y-3">
            <p className="text-sm text-amber-800 dark:text-amber-200">
              {reminderData.message}
            </p>
            
            <p className="text-xs text-amber-700 dark:text-amber-300">
              {getPersonalityMessage()}
            </p>

            {reminderData.features.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {reminderData.features.slice(0, 2).map((feature, index) => (
                  <Badge 
                    key={index} 
                    variant="outline" 
                    className="text-xs border-amber-300 text-amber-700"
                  >
                    {feature.name} ({feature.count}x)
                  </Badge>
                ))}
              </div>
            )}

            <div className="bg-amber-100 dark:bg-amber-900 p-3 rounded-lg border border-amber-200 dark:border-amber-800">
              <p className="text-xs text-amber-800 dark:text-amber-200 mb-2">
                <strong>100% optional!</strong> Platform stays free either way. 
                But if you're feeling generous... 🤷‍♂️
              </p>
              
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleCoffeeSupport}
                  className="flex items-center gap-1 text-xs border-amber-300 hover:bg-amber-200"
                >
                  <Coffee className="w-3 h-3" />
                  Buy Coffee
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleDismiss}
                  className="flex items-center gap-1 text-xs"
                >
                  <Heart className="w-3 h-3" />
                  Maybe Later
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between text-xs text-amber-600 dark:text-amber-400">
              <span className="flex items-center gap-1">
                <TrendingUp className="w-3 h-3" />
                Making progress!
              </span>
              <span className="flex items-center gap-1">
                <Zap className="w-3 h-3" />
                AI powered
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
