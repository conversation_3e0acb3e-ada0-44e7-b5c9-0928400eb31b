'use client';

import React from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useResponsiveDesign } from '@/hooks/useResponsiveDesign';

export interface MobileIconProps {
  icon: LucideIcon;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  'aria-hidden'?: boolean;
  'aria-label'?: string;
}

/**
 * Mobile-optimized icon component that automatically adjusts size and alignment
 * for better mobile experience
 */
export function MobileIcon({
  icon: Icon,
  size = 'md',
  className,
  'aria-hidden': ariaHidden = true,
  'aria-label': ariaLabel,
  ...props
}: MobileIconProps) {
  const { isMobile } = useResponsiveDesign();

  // Slightly larger icons on mobile for better touch targets
  const sizes = {
    sm: isMobile ? 'h-5 w-5' : 'h-4 w-4',
    md: isMobile ? 'h-6 w-6' : 'h-5 w-5',
    lg: isMobile ? 'h-7 w-7' : 'h-6 w-6',
    xl: isMobile ? 'h-8 w-8' : 'h-7 w-7'
  };

  const sizeClasses = sizes[size];

  return (
    <Icon
      className={cn(
        'mobile-icon-align', // Custom CSS class for mobile alignment
        sizeClasses,
        className
      )}
      aria-hidden={ariaHidden}
      aria-label={ariaLabel}
      {...props}
    />
  );
}

export interface MobileIconButtonProps {
  icon: LucideIcon;
  onClick?: () => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'ghost' | 'outline';
  className?: string;
  'aria-label': string;
  disabled?: boolean;
  children?: React.ReactNode;
}

/**
 * Mobile-optimized icon button with proper touch targets
 */
export function MobileIconButton({
  icon: Icon,
  onClick,
  size = 'md',
  variant = 'default',
  className,
  'aria-label': ariaLabel,
  disabled = false,
  children,
  ...props
}: MobileIconButtonProps) {
  const { isMobile } = useResponsiveDesign();

  const baseClasses = 'mobile-button-safe inline-flex items-center justify-center rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';

  const sizeClasses = {
    sm: isMobile ? 'min-h-[44px] min-w-[44px] p-2' : 'h-9 w-9 p-2',
    md: isMobile ? 'min-h-[48px] min-w-[48px] p-3' : 'h-10 w-10 p-2.5',
    lg: isMobile ? 'min-h-[52px] min-w-[52px] p-4' : 'h-11 w-11 p-3'
  };

  const variantClasses = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary',
    ghost: 'hover:bg-accent hover:text-accent-foreground focus:ring-accent',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-accent'
  };

  const buttonClasses = cn(
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    disabled && 'opacity-50 cursor-not-allowed',
    className
  );

  const iconSizes = {
    sm: isMobile ? 'h-5 w-5' : 'h-4 w-4',
    md: isMobile ? 'h-6 w-6' : 'h-5 w-5',
    lg: isMobile ? 'h-7 w-7' : 'h-6 w-6'
  };

  const iconSize = iconSizes[size];

  return (
    <button
      onClick={onClick}
      className={buttonClasses}
      aria-label={ariaLabel}
      disabled={disabled}
      {...props}
    >
      <Icon className={cn('mobile-icon-align', iconSize)} aria-hidden="true" />
      {children && <span className="ml-2">{children}</span>}
    </button>
  );
}

export interface MobileIconTextProps {
  icon: LucideIcon;
  text: string;
  iconPosition?: 'left' | 'right';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Mobile-optimized icon with text component
 */
export function MobileIconText({
  icon: Icon,
  text,
  iconPosition = 'left',
  size = 'md',
  className
}: MobileIconTextProps) {
  const { isMobile } = useResponsiveDesign();

  const getIconSize = () => {
    const sizes = {
      sm: isMobile ? 'h-4 w-4' : 'h-3.5 w-3.5',
      md: isMobile ? 'h-5 w-5' : 'h-4 w-4',
      lg: isMobile ? 'h-6 w-6' : 'h-5 w-5'
    };

    return sizes[size];
  };

  const getTextSize = () => {
    const sizes = {
      sm: isMobile ? 'text-sm' : 'text-xs',
      md: isMobile ? 'text-base' : 'text-sm',
      lg: isMobile ? 'text-lg' : 'text-base'
    };

    return sizes[size];
  };

  const spacing = isMobile ? 'gap-3' : 'gap-2';

  return (
    <div className={cn('inline-flex items-center', spacing, className)}>
      {iconPosition === 'left' && (
        <Icon className={cn('mobile-icon-align', getIconSize())} aria-hidden="true" />
      )}
      <span className={cn('mobile-text-contrast', getTextSize())}>{text}</span>
      {iconPosition === 'right' && (
        <Icon className={cn('mobile-icon-align', getIconSize())} aria-hidden="true" />
      )}
    </div>
  );
}
