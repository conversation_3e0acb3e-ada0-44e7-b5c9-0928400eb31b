'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { useResponsiveDesign } from '@/hooks/useResponsiveDesign';

export interface MobileContainerProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

/**
 * Mobile-optimized container with enhanced spacing and alignment
 */
export function MobileContainer({
  children,
  className,
  padding = 'md',
  maxWidth = 'full'
}: MobileContainerProps) {
  const { isMobile, isTablet } = useResponsiveDesign();

  const getPaddingClasses = () => {
    if (padding === 'none') return '';

    const paddingMap = {
      sm: isMobile ? 'px-4 py-2' : 'px-6 py-3',
      md: isMobile ? 'px-4 py-4' : isTablet ? 'px-6 py-6' : 'px-8 py-8',
      lg: isMobile ? 'px-6 py-6' : isTablet ? 'px-8 py-8' : 'px-12 py-12'
    };

    return paddingMap[padding];
  };

  const getMaxWidthClasses = () => {
    const maxWidthMap = {
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      full: 'max-w-full'
    };

    return maxWidthMap[maxWidth];
  };

  return (
    <div
      className={cn(
        'mobile-safe w-full mx-auto',
        getPaddingClasses(),
        getMaxWidthClasses(),
        className
      )}
    >
      {children}
    </div>
  );
}

export interface MobileCardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
}

/**
 * Mobile-optimized card component with enhanced touch targets
 */
export function MobileCard({
  children,
  className,
  padding = 'md',
  shadow = 'md'
}: MobileCardProps) {
  const { isMobile } = useResponsiveDesign();

  const getPaddingClasses = () => {
    const paddingMap = {
      sm: isMobile ? 'p-4' : 'p-3',
      md: isMobile ? 'p-6' : 'p-4',
      lg: isMobile ? 'p-8' : 'p-6'
    };

    return paddingMap[padding];
  };

  const getShadowClasses = () => {
    const shadowMap = {
      none: '',
      sm: 'shadow-sm',
      md: 'shadow-md',
      lg: 'shadow-lg'
    };

    return shadowMap[shadow];
  };

  return (
    <div
      className={cn(
        'card-safe bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700',
        'mobile-card-spacing',
        getPaddingClasses(),
        getShadowClasses(),
        className
      )}
    >
      {children}
    </div>
  );
}

export interface MobileButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  spacing?: 'sm' | 'md' | 'lg';
}

/**
 * Mobile-optimized button group with proper spacing
 */
export function MobileButtonGroup({
  children,
  className,
  orientation = 'horizontal',
  spacing = 'md'
}: MobileButtonGroupProps) {
  const { isMobile } = useResponsiveDesign();

  const getOrientationClasses = () => {
    if (isMobile && orientation === 'horizontal') {
      // Stack buttons vertically on mobile for better touch targets
      return 'flex flex-col';
    }
    
    return orientation === 'horizontal' ? 'flex flex-row' : 'flex flex-col';
  };

  const getSpacingClasses = () => {
    const isVertical = isMobile || orientation === 'vertical';
    
    const spacingMap = {
      sm: isVertical ? 'gap-2' : 'gap-1',
      md: isVertical ? 'gap-3' : 'gap-2',
      lg: isVertical ? 'gap-4' : 'gap-3'
    };

    return spacingMap[spacing];
  };

  return (
    <div
      className={cn(
        'mobile-button-spacing',
        getOrientationClasses(),
        getSpacingClasses(),
        isMobile && 'w-full',
        className
      )}
    >
      {children}
    </div>
  );
}

export interface MobileGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
}

/**
 * Mobile-optimized grid with responsive columns
 */
export function MobileGrid({
  children,
  className,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md'
}: MobileGridProps) {
  const { isMobile, isTablet, isDesktop } = useResponsiveDesign();

  const getGridClasses = () => {
    let gridCols = 'grid-cols-1'; // Default fallback

    if (isMobile && cols.mobile) {
      gridCols = `grid-cols-${cols.mobile}`;
    } else if (isTablet && cols.tablet) {
      gridCols = `grid-cols-${cols.tablet}`;
    } else if (isDesktop && cols.desktop) {
      gridCols = `grid-cols-${cols.desktop}`;
    }

    return gridCols;
  };

  const getGapClasses = () => {
    const gapMap = {
      sm: isMobile ? 'gap-3' : 'gap-2',
      md: isMobile ? 'gap-4' : 'gap-3',
      lg: isMobile ? 'gap-6' : 'gap-4'
    };

    return gapMap[gap];
  };

  return (
    <div
      className={cn(
        'grid-safe grid',
        getGridClasses(),
        getGapClasses(),
        className
      )}
    >
      {children}
    </div>
  );
}

export interface MobileStackProps {
  children: React.ReactNode;
  className?: string;
  spacing?: 'sm' | 'md' | 'lg';
  align?: 'start' | 'center' | 'end' | 'stretch';
}

/**
 * Mobile-optimized vertical stack component
 */
export function MobileStack({
  children,
  className,
  spacing = 'md',
  align = 'stretch'
}: MobileStackProps) {
  const { isMobile } = useResponsiveDesign();

  const getSpacingClasses = () => {
    const spacingMap = {
      sm: isMobile ? 'space-y-3' : 'space-y-2',
      md: isMobile ? 'space-y-4' : 'space-y-3',
      lg: isMobile ? 'space-y-6' : 'space-y-4'
    };

    return spacingMap[spacing];
  };

  const getAlignClasses = () => {
    const alignMap = {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch'
    };

    return alignMap[align];
  };

  return (
    <div
      className={cn(
        'flex flex-col',
        getSpacingClasses(),
        getAlignClasses(),
        className
      )}
    >
      {children}
    </div>
  );
}
