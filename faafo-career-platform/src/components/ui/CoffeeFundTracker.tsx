'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Coffee, Heart, DollarSign } from 'lucide-react';

interface CoffeeFundTrackerProps {
  currentAmount?: number;
  goal?: number;
  className?: string;
}

export default function CoffeeFundTracker({
  currentAmount = 0, // Starting with reality - no donations yet
  goal = 500, // Monthly coffee goal
  className = ''
}: CoffeeFundTrackerProps) {
  const percentage = Math.min((currentAmount / goal) * 100, 100);
  const coffeeCount = Math.floor(currentAmount / 5); // $5 per coffee
  
  return (
    <Card className={`w-full max-w-md ${className}`}>
      <CardHeader className='text-center pb-4'>
        <CardTitle className='text-lg flex items-center justify-center gap-2'>
          <Coffee className='w-5 h-5 text-amber-600' />
          Darjus's Coffee Fund
        </CardTitle>
        <p className='text-sm text-muted-foreground'>
          Keeping the solo developer caffeinated ☕
        </p>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='text-center'>
          <div className='text-2xl font-bold text-amber-600'>
            ${currentAmount}
          </div>
          <div className='text-sm text-muted-foreground'>
            of ${goal} monthly goal
          </div>
        </div>

        <Progress value={percentage} className='h-3' />

        <div className='flex justify-between text-sm text-muted-foreground'>
          <span>{Math.round(percentage)}% funded</span>
          <span>{coffeeCount} ☕ {currentAmount === 0 ? 'needed' : 'earned'}</span>
        </div>
        
        <div className='bg-amber-50 dark:bg-amber-950 p-4 rounded-lg text-center'>
          <p className='text-sm text-amber-800 dark:text-amber-200 mb-3'>
            <strong>100% optional!</strong> Platform stays free either way. 
            But if you're feeling generous... 🤷‍♂️
          </p>
          
          <div className='flex flex-col gap-2'>
            <div className='flex gap-2 justify-center'>
              <Button
                size="sm"
                variant="outline"
                className='flex items-center gap-1'
                onClick={() => window.open('https://ko-fi.com/faafo', '_blank')}
              >
                <Coffee className='w-4 h-4' />
                $5 Coffee
              </Button>
              <Button
                size="sm"
                variant="outline"
                className='flex items-center gap-1'
                onClick={() => window.open('https://ko-fi.com/faafo', '_blank')}
              >
                <Heart className='w-4 h-4' />
                $10 Fancy Coffee
              </Button>
            </div>
            <div className='flex justify-center'>
              <Button
                size="sm"
                variant="default"
                className='flex items-center gap-1 bg-amber-600 hover:bg-amber-700'
                onClick={() => window.open('https://ko-fi.com/faafo', '_blank')}
              >
                ☕ Choose Your Amount
              </Button>
            </div>
          </div>
          
          <p className='text-xs text-muted-foreground mt-2'>
            Will go toward server costs, coffee, and my questionable life choices
          </p>
        </div>

        <div className='text-center'>
          <p className='text-xs text-muted-foreground'>
            Recent supporters: Nobody yet... be the first! 🥺
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
