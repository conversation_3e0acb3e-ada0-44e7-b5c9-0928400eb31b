'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, MessageCircle, TrendingUp, Coffee } from 'lucide-react';
import { useInterviewPracticeTracker } from '@/hooks/useAPIUsageTracker';

/**
 * Example component showing how to integrate coffee reminder tracking
 * into an interview practice feature
 */

interface InterviewQuestion {
  id: string;
  question: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface InterviewFeedback {
  score: number;
  strengths: string[];
  improvements: string[];
  suggestion: string;
}

export default function InterviewPracticeWithTracking() {
  const [currentQuestion, setCurrentQuestion] = useState<InterviewQuestion | null>(null);
  const [userAnswer, setUserAnswer] = useState('');
  const [feedback, setFeedback] = useState<InterviewFeedback | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionQuestions, setSessionQuestions] = useState<number>(0);

  // Use the interview practice tracker hook
  const { trackQuestion, trackSession, usageSummary } = useInterviewPracticeTracker();

  /**
   * Generate a new interview question
   */
  const handleGenerateQuestion = async () => {
    setIsLoading(true);
    setFeedback(null);
    
    try {
      // Track this API usage - this will automatically trigger coffee reminders
      // when the user reaches the threshold
      trackQuestion();
      
      // Simulate API call to generate question
      const response = await fetch('/api/interview/generate-question', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          category: 'behavioral',
          difficulty: 'medium'
        })
      });

      if (!response.ok) throw new Error('Failed to generate question');
      
      const question = await response.json();
      setCurrentQuestion(question);
      setSessionQuestions(prev => prev + 1);
      
    } catch (error) {
      console.error('Failed to generate question:', error);
      // Even if the API fails, we still tracked the usage
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Submit answer for feedback
   */
  const handleSubmitAnswer = async () => {
    if (!currentQuestion || !userAnswer.trim()) return;
    
    setIsLoading(true);
    
    try {
      // Track this API usage too - feedback generation is also AI-intensive
      trackQuestion();
      
      // Simulate API call for feedback
      const response = await fetch('/api/interview/analyze-answer', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          questionId: currentQuestion.id,
          answer: userAnswer
        })
      });

      if (!response.ok) throw new Error('Failed to analyze answer');
      
      const feedbackData = await response.json();
      setFeedback(feedbackData);
      
    } catch (error) {
      console.error('Failed to analyze answer:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Complete the interview session
   */
  const handleCompleteSession = () => {
    // Track the entire session at once (alternative approach)
    // trackSession(sessionQuestions);
    
    // Reset for new session
    setCurrentQuestion(null);
    setUserAnswer('');
    setFeedback(null);
    setSessionQuestions(0);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header with usage stats */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              Interview Practice
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="flex items-center gap-1">
                <TrendingUp className="w-3 h-3" />
                {usageSummary.totalCalls} AI calls today
              </Badge>
              {usageSummary.isHighUsage && (
                <Badge variant="outline" className="flex items-center gap-1 text-amber-600">
                  <Coffee className="w-3 h-3" />
                  Power user!
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Practice your interview skills with AI-powered questions and feedback.
            {sessionQuestions > 0 && (
              <span className="ml-2 text-sm font-medium">
                {sessionQuestions} questions this session
              </span>
            )}
          </p>
        </CardContent>
      </Card>

      {/* Question Generation */}
      <Card>
        <CardHeader>
          <CardTitle>Generate Interview Question</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={handleGenerateQuestion} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating Question...
              </>
            ) : (
              'Generate New Question'
            )}
          </Button>

          {currentQuestion && (
            <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border">
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="outline">{currentQuestion.category}</Badge>
                <Badge variant={
                  currentQuestion.difficulty === 'easy' ? 'secondary' :
                  currentQuestion.difficulty === 'medium' ? 'default' : 'destructive'
                }>
                  {currentQuestion.difficulty}
                </Badge>
              </div>
              <p className="font-medium text-lg">{currentQuestion.question}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Answer Input */}
      {currentQuestion && (
        <Card>
          <CardHeader>
            <CardTitle>Your Answer</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              placeholder="Type your answer here... Be specific and use examples from your experience."
              value={userAnswer}
              onChange={(e) => setUserAnswer(e.target.value)}
              rows={6}
              className="min-h-[150px]"
            />
            <Button 
              onClick={handleSubmitAnswer}
              disabled={isLoading || !userAnswer.trim()}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Analyzing Answer...
                </>
              ) : (
                'Get AI Feedback'
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Feedback Display */}
      {feedback && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              AI Feedback
              <Badge variant="outline">Score: {feedback.score}/10</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">
                  Strengths
                </h4>
                <ul className="space-y-1">
                  {feedback.strengths.map((strength, index) => (
                    <li key={index} className="text-sm text-green-700 dark:text-green-300">
                      • {strength}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div className="p-4 bg-orange-50 dark:bg-orange-950 rounded-lg">
                <h4 className="font-semibold text-orange-800 dark:text-orange-200 mb-2">
                  Areas for Improvement
                </h4>
                <ul className="space-y-1">
                  {feedback.improvements.map((improvement, index) => (
                    <li key={index} className="text-sm text-orange-700 dark:text-orange-300">
                      • {improvement}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
              <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                Suggestion for Next Time
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {feedback.suggestion}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Session Controls */}
      {sessionQuestions > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Session Progress</p>
                <p className="text-sm text-muted-foreground">
                  {sessionQuestions} questions completed
                </p>
              </div>
              <Button variant="outline" onClick={handleCompleteSession}>
                Complete Session
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Usage Info */}
      {usageSummary.totalCalls > 0 && (
        <Card className="border-amber-200 bg-amber-50 dark:bg-amber-950">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-amber-800 dark:text-amber-200">
              <Coffee className="w-4 h-4" />
              <span className="text-sm">
                You've made {usageSummary.totalCalls} AI calls today
                {usageSummary.topFeature && ` (mostly ${usageSummary.topFeature})`}
                {usageSummary.isHighUsage && ' - you\'re really getting value from this! 🚀'}
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
