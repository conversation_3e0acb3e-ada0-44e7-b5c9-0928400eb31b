'use client';

import { useCallback, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { APIUsageTracker, API_INTENSIVE_FEATURES } from '@/lib/services/api-usage-tracker';

/**
 * Hook for tracking API usage and managing coffee reminders
 */
export function useAPIUsageTracker() {
  const { data: session } = useSession();
  const [usageSummary, setUsageSummary] = useState(APIUsageTracker.getUsageSummary());

  // Update usage summary when component mounts or session changes
  useEffect(() => {
    setUsageSummary(APIUsageTracker.getUsageSummary());
  }, [session]);

  /**
   * Track usage for a specific feature
   */
  const trackUsage = useCallback((feature: keyof typeof API_INTENSIVE_FEATURES) => {
    APIUsageTracker.trackUsage(feature, session?.user?.id);
    setUsageSummary(APIUsageTracker.getUsageSummary());
  }, [session?.user?.id]);

  /**
   * Track multiple API calls for a feature (e.g., batch operations)
   */
  const trackMultipleUsage = useCallback((
    feature: keyof typeof API_INTENSIVE_FEATURES, 
    count: number
  ) => {
    for (let i = 0; i < count; i++) {
      APIUsageTracker.trackUsage(feature, session?.user?.id);
    }
    setUsageSummary(APIUsageTracker.getUsageSummary());
  }, [session?.user?.id]);

  /**
   * Track usage with custom timing (for long-running operations)
   */
  const trackTimedUsage = useCallback(async <T>(
    feature: keyof typeof API_INTENSIVE_FEATURES,
    operation: () => Promise<T>
  ): Promise<T> => {
    const startTime = Date.now();
    
    try {
      const result = await operation();
      
      // Track usage after successful completion
      APIUsageTracker.trackUsage(feature, session?.user?.id);
      setUsageSummary(APIUsageTracker.getUsageSummary());
      
      return result;
    } catch (error) {
      // Still track usage even if operation fails (API was called)
      APIUsageTracker.trackUsage(feature, session?.user?.id);
      setUsageSummary(APIUsageTracker.getUsageSummary());
      throw error;
    }
  }, [session?.user?.id]);

  /**
   * Reset usage statistics
   */
  const resetUsage = useCallback(() => {
    APIUsageTracker.resetStats();
    setUsageSummary(APIUsageTracker.getUsageSummary());
  }, []);

  /**
   * Check if coffee reminder should be shown
   */
  const shouldShowReminder = useCallback(() => {
    return APIUsageTracker.shouldShowReminder();
  }, []);

  return {
    // Usage tracking functions
    trackUsage,
    trackMultipleUsage,
    trackTimedUsage,
    
    // Usage data
    usageSummary,
    
    // Utility functions
    resetUsage,
    shouldShowReminder,
    
    // Helper data
    availableFeatures: Object.keys(API_INTENSIVE_FEATURES) as Array<keyof typeof API_INTENSIVE_FEATURES>
  };
}

/**
 * Hook specifically for interview practice tracking
 */
export function useInterviewPracticeTracker() {
  const { trackUsage, trackMultipleUsage, usageSummary } = useAPIUsageTracker();

  const trackQuestion = useCallback(() => {
    trackUsage('interview-practice');
  }, [trackUsage]);

  const trackSession = useCallback((questionCount: number) => {
    trackMultipleUsage('interview-practice', questionCount);
  }, [trackMultipleUsage]);

  return {
    trackQuestion,
    trackSession,
    usageSummary
  };
}

/**
 * Hook specifically for skill gap analyzer tracking
 */
export function useSkillGapTracker() {
  const { trackUsage, trackTimedUsage, usageSummary } = useAPIUsageTracker();

  const trackAnalysis = useCallback(() => {
    trackUsage('skill-gap-analyzer');
  }, [trackUsage]);

  const trackTimedAnalysis = useCallback(async <T>(operation: () => Promise<T>) => {
    return trackTimedUsage('skill-gap-analyzer', operation);
  }, [trackTimedUsage]);

  return {
    trackAnalysis,
    trackTimedAnalysis,
    usageSummary
  };
}

/**
 * Hook specifically for career assessment tracking
 */
export function useCareerAssessmentTracker() {
  const { trackUsage, usageSummary } = useAPIUsageTracker();

  const trackAssessmentStep = useCallback(() => {
    trackUsage('career-assessment');
  }, [trackUsage]);

  const trackAIInsights = useCallback(() => {
    trackUsage('ai-insights');
  }, [trackUsage]);

  return {
    trackAssessmentStep,
    trackAIInsights,
    usageSummary
  };
}

/**
 * Hook for general AI feature tracking
 */
export function useAIFeatureTracker() {
  const { trackUsage, trackTimedUsage, usageSummary } = useAPIUsageTracker();

  const trackResumeAnalysis = useCallback(() => {
    trackUsage('resume-analysis');
  }, [trackUsage]);

  const trackPersonalizedRecommendations = useCallback(() => {
    trackUsage('personalized-recommendations');
  }, [trackUsage]);

  const trackCareerPathAnalysis = useCallback(() => {
    trackUsage('career-path-analysis');
  }, [trackUsage]);

  const trackTimedAIOperation = useCallback(async <T>(
    feature: 'resume-analysis' | 'personalized-recommendations' | 'career-path-analysis',
    operation: () => Promise<T>
  ) => {
    return trackTimedUsage(feature, operation);
  }, [trackTimedUsage]);

  return {
    trackResumeAnalysis,
    trackPersonalizedRecommendations,
    trackCareerPathAnalysis,
    trackTimedAIOperation,
    usageSummary
  };
}

export default useAPIUsageTracker;
