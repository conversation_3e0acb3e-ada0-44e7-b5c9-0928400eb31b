import {
  startOfDay,
  endOfDay,
  subDays,
  subMonths,
  format,
  eachDayOfInterval,
  eachMonthOfInterval,
  startOfMonth,
  endOfMonth
} from 'date-fns';

import prisma from '@/lib/prisma';

export interface UserEngagementMetrics {
  totalUsers: number;
  activeUsers: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  newUsers: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
  userRetention: {
    day1: number;
    day7: number;
    day30: number;
  };
  sessionMetrics: {
    averageSessionDuration: number;
    totalSessions: number;
    bounceRate: number;
  };
  engagementTrends: Array<{
    date: string;
    activeUsers: number;
    newUsers: number;
    sessions: number;
  }>;
}

export interface LearningProgressMetrics {
  totalResources: number;
  completedResources: number;
  inProgressResources: number;
  averageCompletionTime: number;
  completionRate: number;
  popularResources: Array<{
    id: string;
    title: string;
    completions: number;
    averageRating: number;
  }>;
  learningTrends: Array<{
    date: string;
    completions: number;
    newStarted: number;
    timeSpent: number;
  }>;
  categoryBreakdown: Array<{
    category: string;
    totalResources: number;
    completedResources: number;
    completionRate: number;
  }>;
}

export interface CareerPathMetrics {
  totalPaths: number;
  activePaths: number;
  completionRates: Array<{
    pathId: string;
    pathName: string;
    totalEnrolled: number;
    completed: number;
    completionRate: number;
    averageTimeToComplete: number;
  }>;
  pathPopularity: Array<{
    pathId: string;
    pathName: string;
    bookmarks: number;
    enrollments: number;
    popularityScore: number;
  }>;
  progressDistribution: Array<{
    progressRange: string;
    userCount: number;
    percentage: number;
  }>;
}

export interface CommunityMetrics {
  totalPosts: number;
  totalReplies: number;
  activePosters: number;
  engagementRate: number;
  topContributors: Array<{
    userId: string;
    userName: string;
    postCount: number;
    replyCount: number;
    reputation: number;
  }>;
  categoryActivity: Array<{
    categoryId: string;
    categoryName: string;
    postCount: number;
    replyCount: number;
    lastActivity: string;
  }>;
  communityTrends: Array<{
    date: string;
    newPosts: number;
    newReplies: number;
    activeUsers: number;
  }>;
}

export class AnalyticsService {
  async getUserEngagementMetrics(days: number = 30): Promise<UserEngagementMetrics> {
    const endDate = new Date();
    const startDate = subDays(endDate, days);

    // Get total users
    const totalUsers = await prisma.user.count();

    // Get active users
    const dailyActive = await prisma.user.count({
      where: {
        updatedAt: {
          gte: startOfDay(subDays(endDate, 1)),
          lte: endOfDay(subDays(endDate, 1))
        }
      }
    });

    const weeklyActive = await prisma.user.count({
      where: {
        updatedAt: {
          gte: subDays(endDate, 7)
        }
      }
    });

    const monthlyActive = await prisma.user.count({
      where: {
        updatedAt: {
          gte: subDays(endDate, 30)
        }
      }
    });

    // Get new users
    const newUsersToday = await prisma.user.count({
      where: {
        createdAt: {
          gte: startOfDay(endDate),
          lte: endOfDay(endDate)
        }
      }
    });

    const newUsersThisWeek = await prisma.user.count({
      where: {
        createdAt: {
          gte: subDays(endDate, 7)
        }
      }
    });

    const newUsersThisMonth = await prisma.user.count({
      where: {
        createdAt: {
          gte: subDays(endDate, 30)
        }
      }
    });

    // Calculate retention rates (simplified)
    const usersCreated30DaysAgo = await prisma.user.findMany({
      where: {
        createdAt: {
          gte: startOfDay(subDays(endDate, 31)),
          lte: endOfDay(subDays(endDate, 30))
        }
      },
      select: { id: true }
    });

    const retainedUsers30Days = await prisma.user.count({
      where: {
        id: { in: usersCreated30DaysAgo.map(u => u.id) },
        updatedAt: {
          gte: subDays(endDate, 7)
        }
      }
    });

    // Get engagement trends
    const engagementTrends = await this.getEngagementTrends(startDate, endDate);

    return {
      totalUsers,
      activeUsers: {
        daily: dailyActive,
        weekly: weeklyActive,
        monthly: monthlyActive
      },
      newUsers: {
        today: newUsersToday,
        thisWeek: newUsersThisWeek,
        thisMonth: newUsersThisMonth
      },
      userRetention: {
        day1: 0, // Simplified for now
        day7: 0,
        day30:
          usersCreated30DaysAgo.length > 0
            ? (retainedUsers30Days / usersCreated30DaysAgo.length) * 100
            : 0
      },
      sessionMetrics: {
        averageSessionDuration: 0, // Would need session tracking
        totalSessions: 0,
        bounceRate: 0
      },
      engagementTrends
    };
  }

  async getLearningProgressMetrics(days: number = 30): Promise<LearningProgressMetrics> {
    const endDate = new Date();
    const startDate = subDays(endDate, days);

    // Get total resources
    const totalResources = await prisma.learningResource.count({
      where: { isActive: true }
    });

    // Get completion stats
    const completedResources = await prisma.userLearningProgress.count({
      where: {
        status: 'COMPLETED',
        completedAt: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    const inProgressResources = await prisma.userLearningProgress.count({
      where: {
        status: 'IN_PROGRESS',
        updatedAt: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    // Get popular resources
    const popularResources = await prisma.learningResource.findMany({
      where: { isActive: true },
      include: {
        userProgress: {
          where: {
            status: 'COMPLETED',
            completedAt: {
              gte: startDate,
              lte: endDate
            }
          }
        },
        ratings: true
      },
      take: 10
    });

    const popularResourcesFormatted = popularResources
      .map(resource => ({
        id: resource.id,
        title: resource.title,
        completions: resource.userProgress.length,
        averageRating:
          resource.ratings.length > 0
            ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length
            : 0
      }))
      .sort((a, b) => b.completions - a.completions);

    // Get learning trends
    const learningTrends = await this.getLearningTrends(startDate, endDate);

    // Get category breakdown
    const categoryBreakdown = await this.getCategoryBreakdown(startDate, endDate);

    return {
      totalResources,
      completedResources,
      inProgressResources,
      averageCompletionTime: 0, // Would need more detailed tracking
      completionRate: totalResources > 0 ? (completedResources / totalResources) * 100 : 0,
      popularResources: popularResourcesFormatted,
      learningTrends,
      categoryBreakdown
    };
  }

  private async getEngagementTrends(startDate: Date, endDate: Date) {
    const days = eachDayOfInterval({ start: startDate, end: endDate });

    const trends = await Promise.all(
      days.map(async day => {
        const dayStart = startOfDay(day);
        const dayEnd = endOfDay(day);

        const [activeUsers, newUsers] = await Promise.all([
          prisma.user.count({
            where: {
              updatedAt: {
                gte: dayStart,
                lte: dayEnd
              }
            }
          }),
          prisma.user.count({
            where: {
              createdAt: {
                gte: dayStart,
                lte: dayEnd
              }
            }
          })
        ]);

        return {
          date: format(day, 'yyyy-MM-dd'),
          activeUsers,
          newUsers,
          sessions: 0 // Would need session tracking
        };
      })
    );

    return trends;
  }

  private async getLearningTrends(startDate: Date, endDate: Date) {
    const days = eachDayOfInterval({ start: startDate, end: endDate });

    const trends = await Promise.all(
      days.map(async day => {
        const dayStart = startOfDay(day);
        const dayEnd = endOfDay(day);

        const [completions, newStarted] = await Promise.all([
          prisma.userLearningProgress.count({
            where: {
              status: 'COMPLETED',
              completedAt: {
                gte: dayStart,
                lte: dayEnd
              }
            }
          }),
          prisma.userLearningProgress.count({
            where: {
              status: 'IN_PROGRESS',
              createdAt: {
                gte: dayStart,
                lte: dayEnd
              }
            }
          })
        ]);

        return {
          date: format(day, 'yyyy-MM-dd'),
          completions,
          newStarted,
          timeSpent: 0 // Would need time tracking
        };
      })
    );

    return trends;
  }

  private async getCategoryBreakdown(startDate: Date, endDate: Date) {
    const categories = await prisma.learningResource.groupBy({
      by: ['category'],
      where: { isActive: true },
      _count: { id: true }
    });

    const breakdown = await Promise.all(
      categories.map(async cat => {
        const completedInCategory = await prisma.userLearningProgress.count({
          where: {
            status: 'COMPLETED',
            completedAt: {
              gte: startDate,
              lte: endDate
            },
            resource: {
              category: cat.category
            }
          }
        });

        return {
          category: cat.category,
          totalResources: cat._count.id,
          completedResources: completedInCategory,
          completionRate: cat._count.id > 0 ? (completedInCategory / cat._count.id) * 100 : 0
        };
      })
    );

    return breakdown;
  }

  async getCareerPathMetrics(days: number = 30): Promise<CareerPathMetrics> {
    const endDate = new Date();
    const startDate = subDays(endDate, days);

    // Get total paths
    const totalPaths = await prisma.careerPath.count({
      where: { isActive: true }
    });

    const activePaths = await prisma.careerPath.count({
      where: {
        isActive: true,
        bookmarks: {
          some: {
            createdAt: {
              gte: startDate
            }
          }
        }
      }
    });

    // Get completion rates for each path
    const careerPaths = await prisma.careerPath.findMany({
      where: { isActive: true },
      include: {
        bookmarks: {
          where: {
            createdAt: {
              gte: startDate
            }
          }
        },
        learningPaths: {
          include: {
            userPaths: {
              where: {
                createdAt: {
                  gte: startDate
                }
              }
            }
          }
        }
      }
    });

    const completionRates = careerPaths.map(path => {
      const totalEnrolled = path.learningPaths.reduce((sum, lp) => sum + lp.userPaths.length, 0);
      const completed = path.learningPaths.reduce(
        (sum, lp) => sum + lp.userPaths.filter(up => up.status === 'COMPLETED').length,
        0
      );

      return {
        pathId: path.id,
        pathName: path.name,
        totalEnrolled,
        completed,
        completionRate: totalEnrolled > 0 ? (completed / totalEnrolled) * 100 : 0,
        averageTimeToComplete: 0 // Would need detailed time tracking
      };
    });

    // Get path popularity
    const pathPopularity = careerPaths
      .map(path => {
        const bookmarks = path.bookmarks.length;
        const enrollments = path.learningPaths.reduce((sum, lp) => sum + lp.userPaths.length, 0);

        return {
          pathId: path.id,
          pathName: path.name,
          bookmarks,
          enrollments,
          popularityScore: bookmarks + enrollments * 2 // Weight enrollments more
        };
      })
      .sort((a, b) => b.popularityScore - a.popularityScore);

    // Progress distribution (simplified)
    const progressDistribution = [
      { progressRange: '0-25%', userCount: 0, percentage: 0 },
      { progressRange: '26-50%', userCount: 0, percentage: 0 },
      { progressRange: '51-75%', userCount: 0, percentage: 0 },
      { progressRange: '76-100%', userCount: 0, percentage: 0 }
    ];

    return {
      totalPaths,
      activePaths,
      completionRates,
      pathPopularity,
      progressDistribution
    };
  }

  async getCommunityMetrics(days: number = 30): Promise<CommunityMetrics> {
    const endDate = new Date();
    const startDate = subDays(endDate, days);

    // Get total posts and replies
    const [totalPosts, totalReplies] = await Promise.all([
      prisma.forumPost.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        }
      }),
      prisma.forumReply.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        }
      })
    ]);

    // Get active posters
    const activePosters = await prisma.user.count({
      where: {
        OR: [
          {
            forumPosts: {
              some: {
                createdAt: {
                  gte: startDate,
                  lte: endDate
                }
              }
            }
          },
          {
            forumReplies: {
              some: {
                createdAt: {
                  gte: startDate,
                  lte: endDate
                }
              }
            }
          }
        ]
      }
    });

    // Get top contributors
    const topContributors = await prisma.user.findMany({
      where: {
        OR: [
          {
            forumPosts: {
              some: {
                createdAt: {
                  gte: startDate,
                  lte: endDate
                }
              }
            }
          },
          {
            forumReplies: {
              some: {
                createdAt: {
                  gte: startDate,
                  lte: endDate
                }
              }
            }
          }
        ]
      },
      include: {
        forumPosts: {
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          }
        },
        forumReplies: {
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          }
        },
        profile: {
          select: {
            forumReputation: true
          }
        }
      },
      take: 10
    });

    const topContributorsFormatted = topContributors
      .map(user => ({
        userId: user.id,
        userName: user.name || 'Anonymous',
        postCount: user.forumPosts.length,
        replyCount: user.forumReplies.length,
        reputation: user.profile?.forumReputation || 0
      }))
      .sort((a, b) => b.postCount + b.replyCount - (a.postCount + a.replyCount));

    // Get category activity
    const categoryActivity = await prisma.forumCategory.findMany({
      where: { isActive: true },
      include: {
        posts: {
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          },
          include: {
            replies: {
              where: {
                createdAt: {
                  gte: startDate,
                  lte: endDate
                }
              }
            }
          }
        }
      }
    });

    const categoryActivityFormatted = categoryActivity.map(category => ({
      categoryId: category.id,
      categoryName: category.name,
      postCount: category.posts.length,
      replyCount: category.posts.reduce((sum, post) => sum + post.replies.length, 0),
      lastActivity: category.lastPostAt?.toISOString() || ''
    }));

    // Get community trends
    const communityTrends = await this.getCommunityTrends(startDate, endDate);

    const engagementRate = totalPosts > 0 ? (totalReplies / totalPosts) * 100 : 0;

    return {
      totalPosts,
      totalReplies,
      activePosters,
      engagementRate,
      topContributors: topContributorsFormatted,
      categoryActivity: categoryActivityFormatted,
      communityTrends
    };
  }

  private async getCommunityTrends(startDate: Date, endDate: Date) {
    const days = eachDayOfInterval({ start: startDate, end: endDate });

    const trends = await Promise.all(
      days.map(async day => {
        const dayStart = startOfDay(day);
        const dayEnd = endOfDay(day);

        const [newPosts, newReplies, activeUsers] = await Promise.all([
          prisma.forumPost.count({
            where: {
              createdAt: {
                gte: dayStart,
                lte: dayEnd
              }
            }
          }),
          prisma.forumReply.count({
            where: {
              createdAt: {
                gte: dayStart,
                lte: dayEnd
              }
            }
          }),
          prisma.user.count({
            where: {
              OR: [
                {
                  forumPosts: {
                    some: {
                      createdAt: {
                        gte: dayStart,
                        lte: dayEnd
                      }
                    }
                  }
                },
                {
                  forumReplies: {
                    some: {
                      createdAt: {
                        gte: dayStart,
                        lte: dayEnd
                      }
                    }
                  }
                }
              ]
            }
          })
        ]);

        return {
          date: format(day, 'yyyy-MM-dd'),
          newPosts,
          newReplies,
          activeUsers
        };
      })
    );

    return trends;
  }

  async getComprehensiveAnalytics(days: number = 30) {
    const [userEngagement, learningProgress, careerPaths, community] = await Promise.all([
      this.getUserEngagementMetrics(days),
      this.getLearningProgressMetrics(days),
      this.getCareerPathMetrics(days),
      this.getCommunityMetrics(days)
    ]);

    return {
      userEngagement,
      learningProgress,
      careerPaths,
      community,
      generatedAt: new Date().toISOString(),
      timeRange: `${days} days`
    };
  }
}

export const analyticsService = new AnalyticsService();
