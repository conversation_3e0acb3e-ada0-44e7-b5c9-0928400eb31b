// FAAFO Career Platform is now completely free for all users
// This service is kept for compatibility but all methods return free access

export interface CreateSubscriptionData {
  userId: string;
  packageId?: string;
}

export interface SubscriptionWithDetails {
  id: string;
  userId: string;
  tier: string;
  status: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  daysRemaining: number;
  package: {
    name: string;
    description: string;
    price: number;
  };
}

export class SubscriptionService {
  /**
   * All users now have free access - this method is kept for compatibility
   */
  static async createSubscription(data: CreateSubscriptionData): Promise<any> {
    // All features are now free - return mock subscription for compatibility
    return {
      id: 'free-subscription',
      userId: data.userId,
      tier: 'FREE',
      status: 'ACTIVE',
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      isActive: true,
      package: {
        name: 'Free Access',
        description: 'All features are completely free',
        price: 0,
      },
    };
  }

  /**
   * Get user's subscription - always returns free access
   */
  static async getUserSubscription(userId: string): Promise<SubscriptionWithDetails | null> {
    return {
      id: 'free-subscription',
      userId,
      tier: 'FREE',
      status: 'ACTIVE',
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      isActive: true,
      daysRemaining: 365,
      package: {
        name: 'Free Access',
        description: 'All features are completely free',
        price: 0,
      },
    };
  }

  /**
   * Check if user has active subscription - always returns true (free access)
   */
  static async hasActiveSubscription(userId: string): Promise<boolean> {
    return true;
  }

  /**
   * Check if user can access premium features - always returns true (all free)
   */
  static async canAccessPremiumFeatures(userId: string): Promise<boolean> {
    return true;
  }

  /**
   * Get subscription by payment intent - not needed for free platform
   */
  static async getSubscriptionByPaymentIntent(paymentIntentId: string): Promise<any> {
    return null;
  }

  /**
   * Cancel subscription - not needed for free platform
   */
  static async cancelSubscription(userId: string): Promise<boolean> {
    return true;
  }

  /**
   * Clean up expired subscriptions - not needed for free platform
   */
  static async cleanupExpiredSubscriptions(): Promise<number> {
    return 0;
  }
}

export default SubscriptionService;
