'use client';

/**
 * API Usage Tracker for Coffee Reminders
 * Tracks when users are getting lots of value from AI features
 */

export interface APIUsageEvent {
  feature: string;
  timestamp: number;
  cost: number; // Relative cost (1 = low, 5 = high)
  userId?: string;
}

export interface UsageStats {
  totalCalls: number;
  totalCost: number;
  features: Record<string, number>;
  lastReminderShown: number;
  sessionsWithHighUsage: number;
}

// Features that use significant API calls
export const API_INTENSIVE_FEATURES = {
  'interview-practice': { cost: 4, name: 'Interview Practice' },
  'skill-gap-analyzer': { cost: 3, name: 'Skill Gap Analyzer' },
  'career-assessment': { cost: 2, name: 'Career Assessment' },
  'ai-insights': { cost: 3, name: 'AI Career Insights' },
  'resume-analysis': { cost: 2, name: 'Resume Analysis' },
  'personalized-recommendations': { cost: 2, name: 'Personalized Recommendations' },
  'career-path-analysis': { cost: 3, name: 'Career Path Analysis' }
} as const;

export class APIUsageTracker {
  private static readonly STORAGE_KEY = 'faafo_api_usage';
  private static readonly REMINDER_COOLDOWN = 24 * 60 * 60 * 1000; // 24 hours
  private static readonly HIGH_USAGE_THRESHOLD = 15; // Cost points in session
  private static readonly COFFEE_REMINDER_THRESHOLD = 25; // Total cost points

  /**
   * Track an API usage event
   */
  static trackUsage(feature: keyof typeof API_INTENSIVE_FEATURES, userId?: string): void {
    if (typeof window === 'undefined') return;

    const featureConfig = API_INTENSIVE_FEATURES[feature];
    if (!featureConfig) return;

    const event: APIUsageEvent = {
      feature,
      timestamp: Date.now(),
      cost: featureConfig.cost,
      userId
    };

    // Get current stats
    const stats = this.getUsageStats();
    
    // Update stats
    stats.totalCalls += 1;
    stats.totalCost += event.cost;
    stats.features[feature] = (stats.features[feature] || 0) + 1;

    // Save updated stats
    this.saveUsageStats(stats);

    // Check if we should show coffee reminder
    this.checkForCoffeeReminder(stats);
  }

  /**
   * Get current usage statistics
   */
  static getUsageStats(): UsageStats {
    if (typeof window === 'undefined') {
      return this.getDefaultStats();
    }

    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return this.getDefaultStats();

      const stats = JSON.parse(stored) as UsageStats;
      
      // Reset daily stats if it's a new day
      const lastActivity = Math.max(...Object.values(stats.features).map(() => Date.now()));
      const isNewDay = Date.now() - lastActivity > 24 * 60 * 60 * 1000;
      
      if (isNewDay) {
        return {
          ...stats,
          totalCalls: 0,
          totalCost: 0,
          features: {}
        };
      }

      return stats;
    } catch {
      return this.getDefaultStats();
    }
  }

  /**
   * Save usage statistics
   */
  private static saveUsageStats(stats: UsageStats): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(stats));
    } catch (error) {
      console.warn('Failed to save API usage stats:', error);
    }
  }

  /**
   * Check if we should show a coffee reminder
   */
  private static checkForCoffeeReminder(stats: UsageStats): void {
    const now = Date.now();
    const timeSinceLastReminder = now - stats.lastReminderShown;
    
    // Don't show if we've shown recently
    if (timeSinceLastReminder < this.REMINDER_COOLDOWN) return;

    // Show if user has high usage in this session
    if (stats.totalCost >= this.COFFEE_REMINDER_THRESHOLD) {
      this.triggerCoffeeReminder(stats);
    }
  }

  /**
   * Trigger a coffee reminder
   */
  private static triggerCoffeeReminder(stats: UsageStats): void {
    // Update last reminder time
    stats.lastReminderShown = Date.now();
    stats.sessionsWithHighUsage += 1;
    this.saveUsageStats(stats);

    // Dispatch custom event for components to listen to
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('faafo:coffee-reminder', {
        detail: {
          stats,
          features: this.getTopFeaturesUsed(stats),
          message: this.getCoffeeReminderMessage(stats)
        }
      }));
    }
  }

  /**
   * Get top features used in this session
   */
  private static getTopFeaturesUsed(stats: UsageStats): Array<{ name: string; count: number }> {
    return Object.entries(stats.features)
      .map(([feature, count]) => ({
        name: API_INTENSIVE_FEATURES[feature as keyof typeof API_INTENSIVE_FEATURES]?.name || feature,
        count
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3);
  }

  /**
   * Get personalized coffee reminder message
   */
  private static getCoffeeReminderMessage(stats: UsageStats): string {
    const topFeatures = this.getTopFeaturesUsed(stats);
    const totalCalls = stats.totalCalls;
    const sessionsCount = stats.sessionsWithHighUsage;

    if (sessionsCount === 1) {
      return `Wow! You've used ${topFeatures[0]?.name} ${totalCalls} times today. You're really getting value from this! ☕`;
    } else if (sessionsCount <= 3) {
      return `You're becoming a power user! ${totalCalls} AI calls today. The servers are working hard for you 🔥`;
    } else if (sessionsCount <= 5) {
      return `${totalCalls} AI calls today! You're really making progress on your career journey 🚀`;
    } else {
      return `Holy coffee! ${totalCalls} AI calls today. You're absolutely crushing it! The AI is probably tired 😅`;
    }
  }

  /**
   * Get default stats structure
   */
  private static getDefaultStats(): UsageStats {
    return {
      totalCalls: 0,
      totalCost: 0,
      features: {},
      lastReminderShown: 0,
      sessionsWithHighUsage: 0
    };
  }

  /**
   * Reset usage stats (for testing or user request)
   */
  static resetStats(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.STORAGE_KEY);
  }

  /**
   * Get usage summary for display
   */
  static getUsageSummary(): {
    totalCalls: number;
    topFeature: string | null;
    isHighUsage: boolean;
  } {
    const stats = this.getUsageStats();
    const topFeatures = this.getTopFeaturesUsed(stats);
    
    return {
      totalCalls: stats.totalCalls,
      topFeature: topFeatures[0]?.name || null,
      isHighUsage: stats.totalCost >= this.HIGH_USAGE_THRESHOLD
    };
  }

  /**
   * Check if user should see coffee reminder (without triggering it)
   */
  static shouldShowReminder(): boolean {
    const stats = this.getUsageStats();
    const timeSinceLastReminder = Date.now() - stats.lastReminderShown;
    
    return stats.totalCost >= this.COFFEE_REMINDER_THRESHOLD && 
           timeSinceLastReminder >= this.REMINDER_COOLDOWN;
  }
}

export default APIUsageTracker;
