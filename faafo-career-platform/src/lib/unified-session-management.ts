/**
 * Unified Session Management Service
 *
 * Consolidates all session management logic to eliminate semantic duplicates
 * and provide consistent session handling across the application.
 */

import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NextRequest } from 'next/server';

export interface SessionValidationResult {
  isValid: boolean;
  session?: any;
  user?: any;
  error?: string;
  statusCode?: number;
}

export interface SessionUpdateData {
  status?: 'NOT_STARTED' | 'IN_PROGRESS' | 'PAUSED' | 'COMPLETED' | 'ABANDONED';
  completedQuestions?: number;
  timeSpent?: number;
  currentQuestionIndex?: number;
  lastActivityAt?: Date;
}

export interface SessionCreationData {
  sessionType: string;
  careerPath?: string;
  experienceLevel?: string;
  companyType?: string;
  industryFocus?: string;
  specificRole?: string;
  interviewType?: string;
  preparationTime?: string;
  focusAreas?: string[];
  difficulty: string;
  totalQuestions: number;
}

export class UnifiedSessionManagement {
  /**
   * Validate session access with comprehensive security checks
   */
  static async validateSessionAccess(
    request: NextRequest,
    sessionId: string,
    requiredPermissions: string[] = []
  ): Promise<SessionValidationResult> {
    try {
      // Get authenticated user
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return {
          isValid: false,
          error: 'Authentication required',
          statusCode: 401
        };
      }

      const userId = session.user.id;

      // Validate session exists and user has access
      const interviewSession = await prisma.interviewSession.findFirst({
        where: {
          id: sessionId,
          userId: userId
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              emailVerified: true
            }
          },
          questions: {
            include: {
              responses: true
            }
          }
        }
      });

      if (!interviewSession) {
        return {
          isValid: false,
          error: 'Session not found or access denied',
          statusCode: 404
        };
      }

      // Check session status
      if (interviewSession.status === 'ABANDONED') {
        return {
          isValid: false,
          error: 'Session has been abandoned',
          statusCode: 410
        };
      }

      // Update last activity
      await this.updateLastActivity(sessionId);

      return {
        isValid: true,
        session: interviewSession,
        user: interviewSession.user
      };
    } catch (error) {
      console.error('Session validation error:', error);
      return {
        isValid: false,
        error: 'Session validation failed',
        statusCode: 500
      };
    }
  }

  /**
   * Create a new interview session with proper validation
   */
  static async createSession(
    userId: string,
    sessionData: SessionCreationData
  ): Promise<SessionValidationResult> {
    try {
      // Check for existing active sessions
      const existingActiveSession = await prisma.interviewSession.findFirst({
        where: {
          userId,
          status: {
            in: ['IN_PROGRESS', 'PAUSED']
          }
        }
      });

      if (existingActiveSession) {
        return {
          isValid: false,
          error: 'You already have an active session. Please complete or abandon it first.',
          statusCode: 409
        };
      }

      // Create new session
      const newSession = await prisma.interviewSession.create({
        data: {
          userId,
          sessionType: sessionData.sessionType as any,
          careerPath: sessionData.careerPath,
          experienceLevel: sessionData.experienceLevel as any,
          companyType: sessionData.companyType,
          industryFocus: sessionData.industryFocus,
          specificRole: sessionData.specificRole,
          interviewType: sessionData.interviewType as any,
          preparationTime: sessionData.preparationTime,
          focusAreas: sessionData.focusAreas,
          difficulty: sessionData.difficulty as any,
          totalQuestions: sessionData.totalQuestions,
          status: 'IN_PROGRESS',
          startedAt: new Date(),
          lastActiveAt: new Date(),
          completedQuestions: 0,
          timeSpent: 0
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              emailVerified: true
            }
          }
        }
      });

      return {
        isValid: true,
        session: newSession,
        user: newSession.user
      };
    } catch (error) {
      console.error('Session creation error:', error);
      return {
        isValid: false,
        error: 'Failed to create session',
        statusCode: 500
      };
    }
  }

  /**
   * Update session with validation and conflict resolution
   */
  static async updateSession(
    sessionId: string,
    userId: string,
    updateData: SessionUpdateData
  ): Promise<SessionValidationResult> {
    try {
      // Validate session access first
      const validation = await this.validateSessionOwnership(sessionId, userId);
      if (!validation.isValid) {
        return validation;
      }

      // Prepare update data with conflict resolution
      const updatePayload: any = {
        lastActiveAt: new Date()
      };

      if (updateData.status) {
        updatePayload.status = updateData.status;

        // Set completion time if completing
        if (updateData.status === 'COMPLETED') {
          updatePayload.completedAt = new Date();
        }
      }

      if (updateData.completedQuestions !== undefined) {
        updatePayload.completedQuestions = Math.max(0, updateData.completedQuestions);
      }

      if (updateData.timeSpent !== undefined) {
        updatePayload.timeSpent = Math.max(0, updateData.timeSpent);
      }

      if (updateData.currentQuestionIndex !== undefined) {
        updatePayload.currentQuestionIndex = Math.max(0, updateData.currentQuestionIndex);
      }

      // Update session with optimistic locking
      const updatedSession = await prisma.interviewSession.update({
        where: {
          id: sessionId,
          userId: userId // Additional security check
        },
        data: updatePayload,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              emailVerified: true
            }
          },
          questions: {
            include: {
              responses: true
            }
          }
        }
      });

      return {
        isValid: true,
        session: updatedSession,
        user: updatedSession.user
      };
    } catch (error) {
      console.error('Session update error:', error);
      return {
        isValid: false,
        error: 'Failed to update session',
        statusCode: 500
      };
    }
  }

  /**
   * Get user's sessions with filtering and pagination
   */
  static async getUserSessions(
    userId: string,
    filters: {
      status?: string[];
      sessionType?: string[];
      limit?: number;
      offset?: number;
    } = {}
  ) {
    try {
      const {
        status = ['IN_PROGRESS', 'PAUSED', 'COMPLETED'],
        sessionType,
        limit = 20,
        offset = 0
      } = filters;

      const where: any = {
        userId,
        status: {
          in: status
        }
      };

      if (sessionType && sessionType.length > 0) {
        where.sessionType = {
          in: sessionType
        };
      }

      const sessions = await prisma.interviewSession.findMany({
        where,
        include: {
          questions: {
            include: {
              responses: {
                select: {
                  id: true,
                  isCompleted: true,
                  aiScore: true,
                  createdAt: true
                }
              }
            }
          }
        },
        orderBy: {
          lastActiveAt: 'desc'
        },
        take: limit,
        skip: offset
      });

      const total = await prisma.interviewSession.count({ where });

      return {
        sessions,
        total,
        hasMore: offset + limit < total
      };
    } catch (error) {
      console.error('Error fetching user sessions:', error);
      throw new Error('Failed to fetch sessions');
    }
  }

  /**
   * Clean up abandoned sessions
   */
  static async cleanupAbandonedSessions(): Promise<number> {
    try {
      // Mark sessions as abandoned if inactive for more than 24 hours
      const abandonedThreshold = new Date(Date.now() - 24 * 60 * 60 * 1000);

      const result = await prisma.interviewSession.updateMany({
        where: {
          status: {
            in: ['IN_PROGRESS', 'PAUSED']
          },
          lastActiveAt: {
            lt: abandonedThreshold
          }
        },
        data: {
          status: 'ABANDONED',
          lastActiveAt: new Date()
        }
      });

      console.log(`Cleaned up ${result.count} abandoned sessions`);
      return result.count;
    } catch (error) {
      console.error('Error cleaning up abandoned sessions:', error);
      return 0;
    }
  }

  /**
   * Private helper methods
   */
  private static async validateSessionOwnership(
    sessionId: string,
    userId: string
  ): Promise<SessionValidationResult> {
    const session = await prisma.interviewSession.findFirst({
      where: {
        id: sessionId,
        userId: userId
      }
    });

    if (!session) {
      return {
        isValid: false,
        error: 'Session not found or access denied',
        statusCode: 404
      };
    }

    return {
      isValid: true,
      session
    };
  }

  private static async updateLastActivity(sessionId: string): Promise<void> {
    try {
      await prisma.interviewSession.update({
        where: { id: sessionId },
        data: { lastActiveAt: new Date() }
      });
    } catch (error) {
      // Don't throw - this is a best-effort operation
      console.error('Failed to update last activity:', error);
    }
  }
}

export default UnifiedSessionManagement;
