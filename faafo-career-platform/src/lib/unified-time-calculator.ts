/**
 * Unified Time Calculator Service
 * 
 * This service provides consistent time calculations across all components
 * to fix contradictory time metrics (L001) between Overview, Skill Gaps, 
 * and Learning Plan tabs.
 */

export interface TimeCalculationParams {
  gapSize: number;
  difficulty?: number;
  learningStyle?: LearningStyle;
  timeCommitment?: TimeCommitment;
  skillComplexity?: number;
  skillName?: string;
}

export type LearningStyle = 'VISUAL' | 'AUDITORY' | 'KINESTHETIC' | 'READING';
export type TimeCommitment = 'MINIMAL' | 'MODERATE' | 'INTENSIVE';

export interface TimeEstimate {
  hours: number;
  weeks: number;
  months: number;
  formattedString: string;
}

/**
 * Unified Time Calculator - Single source of truth for all time calculations
 */
export class UnifiedTimeCalculator {
  // Base configuration - consistent across all calculations
  private static readonly BASE_HOURS_PER_LEVEL = 12; // Standardized base time
  private static readonly MIN_HOURS = 4;
  private static readonly MAX_HOURS = 200;
  
  // Learning style multipliers
  private static readonly LEARNING_STYLE_MULTIPLIERS = {
    VISUAL: 0.9,
    AUDITORY: 1.0,
    KINESTHETIC: 1.2,
    READING: 0.95
  } as const;
  
  // Time commitment multipliers
  private static readonly TIME_COMMITMENT_MULTIPLIERS = {
    MINIMAL: 1.3,
    MODERATE: 1.0,
    INTENSIVE: 0.8
  } as const;
  
  // Skill complexity multipliers (unified from all services)
  private static readonly SKILL_COMPLEXITY_MULTIPLIERS: Record<string, number> = {
    'Programming': 1.5,
    'Data Analysis': 1.3,
    'Machine Learning': 1.8,
    'Project Management': 1.0,
    'Communication': 0.8,
    'JavaScript': 1.4,
    'Python': 1.3,
    'React': 1.2,
    'Node.js': 1.3,
    'Database': 1.1,
    'TypeScript': 1.3,
    'AWS': 1.4,
    'Docker': 1.2,
    'Kubernetes': 1.6,
    'DevOps': 1.4,
    'UI/UX Design': 1.1,
    'Testing': 1.0,
    'Security': 1.5,
    'Leadership': 0.9,
    'Agile': 0.8
  };

  /**
   * Main calculation method - used by all components
   */
  static calculateLearningTime(params: TimeCalculationParams): TimeEstimate {
    try {
      const {
        gapSize,
        difficulty = 5,
        learningStyle = 'AUDITORY',
        timeCommitment = 'MODERATE',
        skillComplexity,
        skillName
      } = params;

      // Validate inputs
      const validatedGapSize = Math.max(1, Math.min(10, gapSize));
      const validatedDifficulty = Math.max(1, Math.min(10, difficulty));

      // Base calculation
      let totalHours = validatedGapSize * this.BASE_HOURS_PER_LEVEL;

      // Apply difficulty multiplier
      const difficultyMultiplier = 1 + (validatedDifficulty - 5) * 0.1;
      totalHours *= difficultyMultiplier;

      // Apply learning style multiplier
      if (learningStyle && this.LEARNING_STYLE_MULTIPLIERS[learningStyle as LearningStyle]) {
        totalHours *= this.LEARNING_STYLE_MULTIPLIERS[learningStyle as LearningStyle];
      }

      // Apply time commitment multiplier
      if (timeCommitment && this.TIME_COMMITMENT_MULTIPLIERS[timeCommitment]) {
        totalHours *= this.TIME_COMMITMENT_MULTIPLIERS[timeCommitment];
      }

      // Apply skill complexity multiplier
      if (skillComplexity) {
        totalHours *= skillComplexity;
      } else if (skillName) {
        const complexityMultiplier = this.getSkillComplexityMultiplier(skillName);
        totalHours *= complexityMultiplier;
      }

      // Ensure within bounds
      totalHours = Math.max(this.MIN_HOURS, Math.min(this.MAX_HOURS, totalHours));
      
      // Round to nearest hour
      const hours = Math.round(totalHours);
      
      return this.formatTimeEstimate(hours);
    } catch (error) {
      console.error('Error in unified time calculation:', error);
      return this.getDefaultTimeEstimate();
    }
  }

  /**
   * Get skill complexity multiplier
   */
  private static getSkillComplexityMultiplier(skillName: string): number {
    // Check for exact matches first
    if (this.SKILL_COMPLEXITY_MULTIPLIERS[skillName]) {
      return this.SKILL_COMPLEXITY_MULTIPLIERS[skillName];
    }

    // Check for partial matches
    const lowerSkillName = skillName.toLowerCase();
    for (const [key, multiplier] of Object.entries(this.SKILL_COMPLEXITY_MULTIPLIERS)) {
      if (lowerSkillName.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerSkillName)) {
        return multiplier;
      }
    }

    // Default multiplier
    return 1.0;
  }

  /**
   * Format time estimate into different units
   */
  private static formatTimeEstimate(hours: number): TimeEstimate {
    // Use consistent calculation: 10 hours per week, 4.33 weeks per month
    const weeks = Math.round(hours / 10); // Round to nearest week for consistency
    const months = Math.round(weeks / 4.33); // Round to nearest month for consistency

    let formattedString: string;

    // Use consistent thresholds to avoid jumps
    if (hours <= 30) {
      // For small amounts, show hours (up to 3 weeks worth)
      formattedString = `${hours}h`;
    } else if (hours <= 80) {
      // For medium amounts, show weeks (up to ~2 months worth)
      formattedString = `${weeks}w`;
    } else {
      // For large amounts, show months
      formattedString = `${months}mo`;
    }

    return {
      hours,
      weeks,
      months,
      formattedString
    };
  }

  /**
   * Default fallback time estimate
   */
  private static getDefaultTimeEstimate(): TimeEstimate {
    return this.formatTimeEstimate(40);
  }

  /**
   * Calculate total learning plan time from individual skill gaps
   */
  static calculateTotalLearningPlanTime(skillGaps: Array<{ gapSize?: number; estimatedLearningTime?: number; skillName?: string }>): TimeEstimate {
    try {
      let totalHours = 0;

      for (const gap of skillGaps) {
        if (gap.estimatedLearningTime) {
          // Use existing calculation if available
          totalHours += gap.estimatedLearningTime;
        } else if (gap.gapSize) {
          // Calculate using unified method
          const estimate = this.calculateLearningTime({
            gapSize: gap.gapSize,
            skillName: gap.skillName
          });
          totalHours += estimate.hours;
        }
      }

      return this.formatTimeEstimate(totalHours);
    } catch (error) {
      console.error('Error calculating total learning plan time:', error);
      return this.getDefaultTimeEstimate();
    }
  }

  /**
   * Convert between different time units consistently
   */
  static convertTime(value: number, fromUnit: 'hours' | 'weeks' | 'months', toUnit: 'hours' | 'weeks' | 'months'): number {
    // Convert to hours first
    let hours: number;
    switch (fromUnit) {
      case 'hours':
        hours = value;
        break;
      case 'weeks':
        hours = value * 10; // 10 hours per week
        break;
      case 'months':
        hours = value * 43.3; // 43.3 hours per month (10 hours/week * 4.33 weeks/month)
        break;
    }

    // Convert from hours to target unit
    switch (toUnit) {
      case 'hours':
        return Math.round(hours);
      case 'weeks':
        return Math.ceil(hours / 10);
      case 'months':
        return Math.ceil(hours / 43.3);
    }
  }

  /**
   * Validate time calculation parameters
   */
  static validateParams(params: TimeCalculationParams): boolean {
    return (
      params.gapSize > 0 &&
      params.gapSize <= 10 &&
      (!params.difficulty || (params.difficulty >= 1 && params.difficulty <= 10))
    );
  }
}
