
// Auto-generated meta tags configuration for SEO optimization
export const pageMetaConfig = {
  "/forum": {
    "title": "Career Community Forum | FAAFO - Connect with Professionals",
    "description": "Join our career community forum. Get advice, share experiences, and connect with professionals on their career transition journey.",
    "keywords": "career forum, professional community, career advice, job search help, career transition support",
    "structuredData": {
      "@context": "https://schema.org",
      "@type": "DiscussionForumPosting",
      "name": "FAAFO Career Community Forum",
      "description": "A community forum for career development and professional growth",
      "url": "https://www.faafocareer.com/forum"
    }
  },
  "/tools": {
    "title": "Free Career Tools | FAAFO - Assessment, Calculator & More",
    "description": "Access free career development tools including skill assessments, salary calculators, and interview practice. Advance your career today.",
    "keywords": "career tools, skill assessment, salary calculator, interview practice, career development tools",
    "structuredData": {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "FAAFO Career Tools",
      "description": "Comprehensive suite of career development tools",
      "url": "https://www.faafocareer.com/tools"
    }
  },
  "/resources": {
    "title": "Career Resources & Guides | FAAFO - Free Learning Materials",
    "description": "Discover free career resources, guides, and learning materials to accelerate your professional growth and career transition.",
    "keywords": "career resources, professional development, career guides, learning materials, career transition help",
    "structuredData": {
      "@context": "https://schema.org",
      "@type": "EducationalOrganization",
      "name": "FAAFO Career Resources",
      "description": "Free educational resources for career development",
      "url": "https://www.faafocareer.com/resources"
    }
  }
};

export function getPageMeta(path: string) {
  return pageMetaConfig[path] || null;
}
