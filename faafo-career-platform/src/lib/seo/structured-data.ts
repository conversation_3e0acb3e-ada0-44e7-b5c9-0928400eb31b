
// Auto-generated structured data for SEO
export const organizationSchema = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  'name': 'FAAFO Career Platform',
  'url': 'https://www.faafocareer.com',
  'logo': 'https://www.faafocareer.com/logo-faafo.png',
  'description': 'Free career development platform helping professionals transition to their dream careers',
  'sameAs': [
    'https://twitter.com/faafo_platform',
    'https://linkedin.com/company/faafo-career'
  ],
  'contactPoint': {
    '@type': 'ContactPoint',
    'contactType': 'customer service',
    'url': 'https://www.faafocareer.com/contact'
  }
};

export const websiteSchema = {
  '@context': 'https://schema.org',
  '@type': 'WebSite',
  'name': 'FAAFO Career Platform',
  'url': 'https://www.faafocareer.com',
  'description': 'Free career assessment, skill gap analysis & interview practice platform',
  'potentialAction': {
    '@type': 'SearchAction',
    'target': 'https://www.faafocareer.com/search?q={search_term_string}',
    'query-input': 'required name=search_term_string'
  }
};

export const breadcrumbSchema = (items: Array<{name: string, url: string}>) => ({
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  'itemListElement': items.map((item, index) => ({
    '@type': 'ListItem',
    'position': index + 1,
    'name': item.name,
    'item': item.url
  }))
});
