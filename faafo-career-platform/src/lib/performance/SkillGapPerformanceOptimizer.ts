import { performance } from 'perf_hooks';

import { MemoryCache } from '@/lib/cache';
import prisma from '@/lib/prisma';

interface CacheConfig {
  maxSize: number;
  ttl: number; // Time to live in milliseconds
}

interface PerformanceMetrics {
  queryTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  operationsPerSecond: number;
}

interface BatchOperation<T> {
  id: string;
  operation: () => Promise<T>;
  priority: number;
}

/**
 * Performance optimizer for Skill Gap Analyzer operations
 * Implements caching, batching, and query optimization
 */
export class SkillGapPerformanceOptimizer {
  private static instance: SkillGapPerformanceOptimizer;

  // Multi-level caching
  private userCache: MemoryCache;
  private skillCache: MemoryCache;
  private marketDataCache: MemoryCache;
  private assessmentCache: MemoryCache;

  // Performance tracking
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private operationCounts: Map<string, number> = new Map();
  private cacheHits: Map<string, number> = new Map();
  private cacheMisses: Map<string, number> = new Map();

  // Batch processing
  private batchQueue: Map<string, BatchOperation<any>[]> = new Map();
  private batchTimers: Map<string, NodeJS.Timeout> = new Map();
  private readonly BATCH_DELAY = 50; // 50ms batch delay
  private readonly MAX_BATCH_SIZE = 10;

  private constructor() {
    // Initialize caches with optimized configurations
    this.userCache = new MemoryCache({
      maxSize: 1000,
      ttl: 5 * 60 * 1000 // 5 minutes
    });

    this.skillCache = new MemoryCache({
      maxSize: 5000,
      ttl: 30 * 60 * 1000 // 30 minutes
    });

    this.marketDataCache = new MemoryCache({
      maxSize: 2000,
      ttl: 60 * 60 * 1000 // 1 hour
    });

    this.assessmentCache = new MemoryCache({
      maxSize: 10000,
      ttl: 10 * 60 * 1000 // 10 minutes
    });
  }

  public static getInstance(): SkillGapPerformanceOptimizer {
    if (!SkillGapPerformanceOptimizer.instance) {
      SkillGapPerformanceOptimizer.instance = new SkillGapPerformanceOptimizer();
    }
    return SkillGapPerformanceOptimizer.instance;
  }

  /**
   * Optimized user lookup with caching
   */
  async getUser(userId: string): Promise<any> {
    const startTime = performance.now();
    const cacheKey = `user:${userId}`;

    // Check cache first
    let user = this.userCache.get(cacheKey);
    if (user) {
      this.recordCacheHit('getUser');
      this.recordMetrics('getUser', startTime);
      return user;
    }

    this.recordCacheMiss('getUser');

    try {
      // Database query with optimized selection
      user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          createdAt: true
          // Only select fields we actually need
        }
      });

      if (user) {
        this.userCache.set(cacheKey, user);
      }
    } catch (error) {
      console.error('Error fetching user:', error);
      user = null;
    }

    this.recordMetrics('getUser', startTime);
    return user;
  }

  /**
   * Batch skill lookup with caching
   */
  async getSkills(skillIds: string[]): Promise<any[]> {
    const startTime = performance.now();
    const results: any[] = [];
    const uncachedIds: string[] = [];

    // Check cache for each skill
    for (const skillId of skillIds) {
      const cacheKey = `skill:${skillId}`;
      const cached = this.skillCache.get(cacheKey);
      if (cached) {
        results.push(cached);
        this.recordCacheHit('getSkills');
      } else {
        uncachedIds.push(skillId);
        this.recordCacheMiss('getSkills');
      }
    }

    // Batch fetch uncached skills
    if (uncachedIds.length > 0) {
      try {
        const skills = await prisma.skill.findMany({
          where: {
            id: { in: uncachedIds }
          },
          select: {
            id: true,
            name: true,
            category: true,
            description: true
            // Optimize by only selecting needed fields
          }
        });

        // Cache the results
        for (const skill of skills) {
          const cacheKey = `skill:${skill.id}`;
          this.skillCache.set(cacheKey, skill);
          results.push(skill);
        }
      } catch (error) {
        console.error('Error fetching skills:', error);
        // Return partial results if database fails
      }
    }

    this.recordMetrics('getSkills', startTime);
    return results;
  }

  /**
   * Optimized market data retrieval with intelligent caching
   */
  async getMarketData(skill: string, location?: string): Promise<any> {
    const startTime = performance.now();
    const cacheKey = `market:${skill}:${location || 'global'}`;

    // Check cache first
    let marketData = this.marketDataCache.get(cacheKey);
    if (marketData) {
      this.recordCacheHit('getMarketData');
      this.recordMetrics('getMarketData', startTime);
      return marketData;
    }

    this.recordCacheMiss('getMarketData');

    try {
      // Try to find skill first (with caching)
      const skillRecord = await this.getSkillByName(skill);
      if (!skillRecord) {
        // Return default market data for unknown skills
        marketData = this.getDefaultMarketData(skill);
        this.marketDataCache.set(cacheKey, marketData);
        this.recordMetrics('getMarketData', startTime);
        return marketData;
      }

      // Query market data with optimized query
      const dbMarketData = await prisma.skillMarketData.findFirst({
        where: {
          skillId: skillRecord.id,
          isActive: true
        },
        orderBy: {
          dataDate: 'desc'
        },
        select: {
          demandLevel: true,
          averageSalaryImpact: true,
          growthTrend: true,
          dataDate: true,
          region: true
        }
      });

      // Ensure we always include the skill name in the response
      marketData = dbMarketData
        ? {
            ...dbMarketData,
            skill: skill.toLowerCase(),
            skillId: skillRecord.id,
            skillName: skillRecord.name
          }
        : this.getDefaultMarketData(skill);

      this.marketDataCache.set(cacheKey, marketData);
    } catch (error) {
      console.error('Error fetching market data:', error);
      marketData = this.getDefaultMarketData(skill);
      this.marketDataCache.set(cacheKey, marketData);
    }

    this.recordMetrics('getMarketData', startTime);
    return marketData;
  }

  /**
   * Optimized skill lookup by name with caching
   */
  async getSkillByName(skillName: string): Promise<any> {
    const cacheKey = `skill:name:${skillName.toLowerCase()}`;

    let skill = this.skillCache.get(cacheKey);
    if (skill) {
      this.recordCacheHit('getSkillByName');
      return skill;
    }

    this.recordCacheMiss('getSkillByName');

    try {
      skill = await prisma.skill.findFirst({
        where: {
          name: {
            equals: skillName,
            
          }
        },
        select: {
          id: true,
          name: true,
          category: true
        }
      });

      if (skill) {
        this.skillCache.set(cacheKey, skill);
      }
    } catch (error) {
      console.error('Error fetching skill by name:', error);
      skill = null;
    }

    return skill;
  }

  /**
   * Batch assessment creation with optimized database operations
   */
  async createAssessmentsBatch(assessments: any[]): Promise<any[]> {
    const startTime = performance.now();

    // Use transaction for batch operations
    const results = await prisma.$transaction(async tx => {
      const createdAssessments = [];

      for (const assessment of assessments) {
        const created = await tx.skillAssessment.create({
          data: assessment,
          select: {
            id: true,
            userId: true,
            skillId: true,
            selfRating: true,
            confidenceLevel: true,
            assessmentDate: true
          }
        });
        createdAssessments.push(created);
      }

      return createdAssessments;
    });

    // Cache the results
    for (const assessment of results) {
      const cacheKey = `assessment:${assessment.userId}:${assessment.skillId}`;
      this.assessmentCache.set(cacheKey, assessment);
    }

    this.recordMetrics('createAssessmentsBatch', startTime);
    return results;
  }

  /**
   * Add operation to batch queue for processing
   */
  async addToBatch<T>(
    batchType: string,
    operationId: string,
    operation: () => Promise<T>,
    priority: number = 1
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      if (!this.batchQueue.has(batchType)) {
        this.batchQueue.set(batchType, []);
      }

      const batch = this.batchQueue.get(batchType)!;
      batch.push({
        id: operationId,
        operation: async () => {
          try {
            const result = await operation();
            resolve(result);
            return result;
          } catch (error) {
            reject(error);
            throw error;
          }
        },
        priority
      });

      // Sort by priority
      batch.sort((a, b) => b.priority - a.priority);

      // Process batch if it's full or set timer
      if (batch.length >= this.MAX_BATCH_SIZE) {
        this.processBatch(batchType);
      } else {
        this.scheduleBatchProcessing(batchType);
      }
    });
  }

  /**
   * Process a batch of operations
   */
  private async processBatch(batchType: string): Promise<void> {
    const batch = this.batchQueue.get(batchType);
    if (!batch || batch.length === 0) return;

    // Clear the batch and timer
    this.batchQueue.set(batchType, []);
    const timer = this.batchTimers.get(batchType);
    if (timer) {
      clearTimeout(timer);
      this.batchTimers.delete(batchType);
    }

    // Execute all operations in parallel
    await Promise.allSettled(batch.map(item => item.operation()));
  }

  /**
   * Schedule batch processing with delay
   */
  private scheduleBatchProcessing(batchType: string): void {
    if (this.batchTimers.has(batchType)) return;

    const timer = setTimeout(() => {
      this.processBatch(batchType);
    }, this.BATCH_DELAY);

    this.batchTimers.set(batchType, timer);
  }

  /**
   * Get default market data for unknown skills
   */
  private getDefaultMarketData(skill: string): any {
    return {
      skill: skill.toLowerCase(),
      demand: 50,
      supply: 50,
      averageSalary: 75000,
      growth: 5,
      difficulty: 5,
      timeToLearn: 12,
      category: 'Unknown',
      lastUpdated: new Date().toISOString(),
      isStale: true
    };
  }

  /**
   * Record cache hit
   */
  private recordCacheHit(type: string): void {
    const current = this.cacheHits.get(type) || 0;
    this.cacheHits.set(type, current + 1);
  }

  /**
   * Record cache miss
   */
  private recordCacheMiss(type: string): void {
    const current = this.cacheMisses.get(type) || 0;
    this.cacheMisses.set(type, current + 1);
  }

  /**
   * Record performance metrics
   */
  private recordMetrics(operation: string, startTime: number): void {
    const endTime = performance.now();
    const queryTime = endTime - startTime;

    const current = this.operationCounts.get(operation) || 0;
    this.operationCounts.set(operation, current + 1);

    // Calculate cache hit rate for this specific operation
    const hits = this.cacheHits.get(operation) || 0;
    const misses = this.cacheMisses.get(operation) || 0;
    const total = hits + misses;
    const cacheHitRate = total > 0 ? hits / total : 0;

    this.metrics.set(operation, {
      queryTime,
      cacheHitRate,
      memoryUsage: process.memoryUsage().heapUsed,
      operationsPerSecond: current + 1 // Include current operation
    });
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): Map<string, PerformanceMetrics> {
    return new Map(this.metrics);
  }

  /**
   * Clear all caches
   */
  clearCaches(): void {
    this.userCache.clear();
    this.skillCache.clear();
    this.marketDataCache.clear();
    this.assessmentCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): any {
    // Aggregate hits and misses for related operations
    const userHits = this.cacheHits.get('getUser') || 0;
    const userMisses = this.cacheMisses.get('getUser') || 0;

    const skillHits =
      (this.cacheHits.get('getSkills') || 0) + (this.cacheHits.get('getSkillByName') || 0);
    const skillMisses =
      (this.cacheMisses.get('getSkills') || 0) + (this.cacheMisses.get('getSkillByName') || 0);

    const marketDataHits = this.cacheHits.get('getMarketData') || 0;
    const marketDataMisses = this.cacheMisses.get('getMarketData') || 0;

    const assessmentHits = this.cacheHits.get('assessment') || 0;
    const assessmentMisses = this.cacheMisses.get('assessment') || 0;

    return {
      user: {
        size: this.userCache.getStats().size,
        hits: userHits,
        misses: userMisses
      },
      skill: {
        size: this.skillCache.getStats().size,
        hits: skillHits,
        misses: skillMisses
      },
      marketData: {
        size: this.marketDataCache.getStats().size,
        hits: marketDataHits,
        misses: marketDataMisses
      },
      assessment: {
        size: this.assessmentCache.getStats().size,
        hits: assessmentHits,
        misses: assessmentMisses
      }
    };
  }
}

// Export singleton instance
export const skillGapPerformanceOptimizer = SkillGapPerformanceOptimizer.getInstance();
