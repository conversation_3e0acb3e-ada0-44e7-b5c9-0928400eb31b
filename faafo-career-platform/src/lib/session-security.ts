import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';

import securityStorage from '@/lib/security-storage';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';

export interface SessionValidationResult {
  isValid: boolean;
  userId?: string;
  sessionData?: any;
  error?: string;
  statusCode?: number;
}

export class SessionSecurity {
  private static readonly MAX_SESSION_AGE = 24 * 60 * 60 * 1000; // 24 hours
  private static readonly SESSION_ID_PATTERN =
    /^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$/i;

  /**
   * Comprehensive session validation with anti-enumeration protection
   */
  static async validateSessionAccess(
    request: NextRequest,
    sessionId: string,
    sessionType: 'interview' | 'learning' | 'assessment' = 'interview'
  ): Promise<SessionValidationResult> {
    try {
      // 1. Basic session ID format validation
      if (!this.isValidSessionIdFormat(sessionId)) {
        // Use generic error to prevent enumeration
        return {
          isValid: false,
          error: 'Session not found',
          statusCode: 404
        };
      }

      // 2. Authentication check
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return {
          isValid: false,
          error: 'Authentication required',
          statusCode: 401
        };
      }

      const userId = session.user.id;

      // 3. Rate limiting for session access attempts
      const rateLimitResult = await securityStorage.checkRateLimit(
        request,
        5 * 60 * 1000, // 5 minutes
        20 // Max 20 session access attempts per 5 minutes
      );

      if (!rateLimitResult.allowed) {
        return {
          isValid: false,
          error: 'Too many session access attempts',
          statusCode: 429
        };
      }

      // 4. Database validation with ownership check
      let sessionData;
      switch (sessionType) {
        case 'interview':
          sessionData = await this.validateInterviewSession(sessionId, userId);
          break;
        case 'learning':
          sessionData = await this.validateLearningSession(sessionId, userId);
          break;
        case 'assessment':
          sessionData = await this.validateAssessmentSession(sessionId, userId);
          break;
        default:
          return {
            isValid: false,
            error: 'Invalid session type',
            statusCode: 400
          };
      }

      if (!sessionData) {
        // Log potential enumeration attempt
        await this.logSecurityEvent(request, userId, 'session_enumeration_attempt', {
          sessionId,
          sessionType
        });

        return {
          isValid: false,
          error: 'Session not found',
          statusCode: 404
        };
      }

      // 5. Session age validation
      if (!this.isSessionActive(sessionData)) {
        return {
          isValid: false,
          error: 'Session expired',
          statusCode: 410
        };
      }

      // 6. Update last access time
      await this.updateSessionAccess(sessionId, sessionType);

      return {
        isValid: true,
        userId,
        sessionData
      };
    } catch (error) {
      console.error('Session validation error:', error);
      return {
        isValid: false,
        error: 'Session validation failed',
        statusCode: 500
      };
    }
  }

  /**
   * Validate session ID format to prevent injection attacks
   */
  private static isValidSessionIdFormat(sessionId: string): boolean {
    if (!sessionId || typeof sessionId !== 'string') {
      return false;
    }

    // Check UUID v4 format
    return this.SESSION_ID_PATTERN.test(sessionId);
  }

  /**
   * Validate interview session ownership
   */
  private static async validateInterviewSession(sessionId: string, userId: string) {
    return await prisma.interviewSession.findFirst({
      where: {
        id: sessionId,
        userId
      },
      select: {
        id: true,
        userId: true,
        status: true,
        createdAt: true,
        lastActiveAt: true,
        completedAt: true
      }
    });
  }

  /**
   * Validate learning session ownership
   */
  private static async validateLearningSession(sessionId: string, userId: string) {
    return await prisma.userLearningPath.findFirst({
      where: {
        id: sessionId,
        userId
      },
      select: {
        id: true,
        userId: true,
        status: true,
        startedAt: true,
        lastAccessedAt: true,
        completedAt: true
      }
    });
  }

  /**
   * Validate assessment session ownership
   */
  private static async validateAssessmentSession(sessionId: string, userId: string) {
    return await prisma.assessment.findFirst({
      where: {
        id: sessionId,
        userId
      },
      select: {
        id: true,
        userId: true,
        status: true,
        createdAt: true,
        updatedAt: true
      }
    });
  }

  /**
   * Check if session is still active
   */
  private static isSessionActive(sessionData: any): boolean {
    if (!sessionData) return false;

    // Check if session is completed
    if (sessionData.status === 'COMPLETED' || sessionData.status === 'ABANDONED') {
      return true; // Allow access to completed sessions for review
    }

    // Check session age
    const lastActive =
      sessionData.lastActiveAt || sessionData.lastAccessedAt || sessionData.updatedAt;
    if (lastActive) {
      const ageMs = Date.now() - new Date(lastActive).getTime();
      return ageMs <= this.MAX_SESSION_AGE;
    }

    return true; // Default to active if no timestamp available
  }

  /**
   * Update session last access time
   */
  private static async updateSessionAccess(sessionId: string, sessionType: string) {
    try {
      switch (sessionType) {
        case 'interview':
          await prisma.interviewSession.update({
            where: { id: sessionId },
            data: { lastActiveAt: new Date() }
          });
          break;
        case 'learning':
          await prisma.userLearningPath.update({
            where: { id: sessionId },
            data: { lastAccessedAt: new Date() }
          });
          break;
        case 'assessment':
          await prisma.assessment.update({
            where: { id: sessionId },
            data: { updatedAt: new Date() }
          });
          break;
      }
    } catch (error) {
      console.warn('Failed to update session access time:', error);
    }
  }

  /**
   * Log security events for monitoring
   */
  private static async logSecurityEvent(
    request: NextRequest,
    userId: string,
    eventType: string,
    metadata: any
  ) {
    try {
      // Store security event in database for monitoring
      await prisma.securityToken.create({
        data: {
          identifier: `security_log_${userId}`,
          type: 'SECURITY_EVENT',
          token: eventType,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          metadata: {
            ...metadata,
            ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
            userAgent: request.headers.get('user-agent'),
            timestamp: new Date().toISOString(),
            userId
          }
        }
      });
    } catch (error) {
      console.warn('Failed to log security event:', error);
    }
  }

  /**
   * Generate secure session token with additional entropy
   */
  static generateSecureSessionToken(): string {
    const timestamp = Date.now().toString(36);
    const randomBytes = crypto.getRandomValues(new Uint8Array(16));
    const randomString = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join(
      ''
    );
    return `${timestamp}_${randomString}`;
  }

  /**
   * Validate session state transitions
   */
  static validateSessionStateTransition(
    currentStatus: string,
    newStatus: string,
    sessionType: string = 'interview'
  ): { isValid: boolean; error?: string } {
    const validTransitions: Record<string, Record<string, string[]>> = {
      interview: {
        NOT_STARTED: ['IN_PROGRESS'],
        IN_PROGRESS: ['PAUSED', 'COMPLETED', 'ABANDONED'],
        PAUSED: ['IN_PROGRESS', 'ABANDONED'],
        COMPLETED: [], // No transitions from completed
        ABANDONED: [] // No transitions from abandoned
      },
      learning: {
        NOT_STARTED: ['IN_PROGRESS'],
        IN_PROGRESS: ['PAUSED', 'COMPLETED'],
        PAUSED: ['IN_PROGRESS'],
        COMPLETED: [], // Allow re-starting completed learning paths
        ARCHIVED: []
      },
      assessment: {
        NOT_STARTED: ['IN_PROGRESS'],
        IN_PROGRESS: ['COMPLETED', 'ABANDONED'],
        COMPLETED: [],
        ABANDONED: []
      }
    };

    const allowedTransitions = validTransitions[sessionType]?.[currentStatus] || [];

    if (!allowedTransitions.includes(newStatus)) {
      return {
        isValid: false,
        error: `Invalid status transition from ${currentStatus} to ${newStatus}`
      };
    }

    return { isValid: true };
  }
}

export default SessionSecurity;
