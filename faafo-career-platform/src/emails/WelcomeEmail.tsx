import * as React from 'react';
import { Html, Head, Body, Container, Text, <PERSON><PERSON>, Link } from '@react-email/components';

interface WelcomeEmailProps {
  username: string;
  dashboardLink: string;
}

export const WelcomeEmail = ({ username, dashboardLink }: WelcomeEmailProps) => (
  <Html>
    <Head>
      <style>{`
        body {
          background-color: white;
          margin: auto;
          font-family: sans-serif;
          padding: 8px;
        }
        .container {
          border: 1px solid #eaeaea;
          border-radius: 8px;
          margin: 32px auto;
          padding: 32px;
          max-width: 600px;
        }
        .title {
          color: #2563eb;
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 16px;
        }
        .text {
          color: black;
          font-size: 16px;
          line-height: 1.5;
          margin-bottom: 16px;
        }
        .button {
          background-color: #2563eb;
          border-radius: 6px;
          color: white;
          font-size: 16px;
          font-weight: 600;
          text-decoration: none;
          text-align: center;
          padding: 12px 20px;
          display: inline-block;
          margin: 16px 0;
        }
        .warning-box {
          background-color: #fef3c7;
          border: 1px solid #f59e0b;
          border-radius: 6px;
          padding: 16px;
          margin: 16px 0;
        }
        .signature {
          color: #666;
          font-size: 14px;
          font-style: italic;
          margin-top: 24px;
        }
      `}</style>
    </Head>
    <Body>
      <Container className='container'>
        <Text className='title'>🎉 Welcome to the FAAFO Experiment!</Text>
        <Text className='text'>Hey {username}!</Text>
        <Text className='text'>
          Congrats! You've successfully verified your email and officially joined the ranks of career rebels 
          who are ready to f*** around and find out what actually works. 🚀
        </Text>
        
        <div className='warning-box'>
          <Text style={{ margin: '0', fontWeight: 'bold', color: '#92400e' }}>
            ⚠️ Quick Reality Check:
          </Text>
          <Text style={{ margin: '8px 0 0 0', color: '#92400e' }}>
            This platform was built by a solo developer (me) at 3 AM with coffee, weed, and questionable life choices. 
            Expect bugs, weird behavior, and the occasional "what the hell was I thinking?" moment. 
            But hey, at least we're honest about it! )))
          </Text>
        </div>

        <Text className='text'>
          <strong>Here's what you can do now:</strong>
        </Text>
        <Text className='text'>
          • Take the career assessment (it's not your typical "what's your spirit animal?" quiz)<br/>
          • Calculate your Freedom Fund (because "follow your passion" is terrible advice if you can't pay rent)<br/>
          • Join the community of fellow career rebels<br/>
          • Explore resources that don't suck<br/>
          • Start f***ing around with your career path!
        </Text>

        <Button className='button' href={dashboardLink}>
          Let's Start This Chaos! 🚀
        </Button>

        <Text className='text'>
          <strong>Pro tip:</strong> If something breaks (and it probably will), just refresh the page, 
          try again, and maybe make some coffee. If it's really broken, just reply to this email 
          and I'll fix it... eventually... after I finish crying into my keyboard 😅
        </Text>

        <Text className='text'>
          Remember: career change is a marathon, not a sprint. Be patient with yourself, 
          celebrate small wins, and don't be afraid to ask for help. I'm here to support you 
          (when I'm not debugging at 3 AM).
        </Text>

        <Text className='signature'>
          Welcome to the rebellion,<br/>
          Darjus<br/>
          Solo Founder & Chief Coffee Consumer ☕<br/>
          <em>P.S. - I actually read replies, so feel free to say hi or share your story!</em>
        </Text>
      </Container>
    </Body>
  </Html>
);

export default WelcomeEmail;
