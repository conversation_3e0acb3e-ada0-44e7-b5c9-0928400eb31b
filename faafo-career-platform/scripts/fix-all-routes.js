#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// List of routes that need to be fixed
const problematicRoutes = [
  'src/app/api/interview-practice/[sessionId]/responses/route.ts',
  'src/app/api/interview-practice/progress/route.ts',
  'src/app/api/interview-practice/route.ts',
  'src/app/api/monitoring/health/route.ts',
  'src/app/api/learning-paths/route.ts',
  'src/app/api/learning-paths/[id]/enroll/route.ts',
  'src/app/api/learning-paths/[id]/steps/[stepId]/progress/route.ts',
  'src/app/api/learning-paths/[id]/route.ts'
];

// Problematic imports to look for
const problematicImports = [
  'consolidatedCache',
  'optimizedAIService', 
  'geminiService',
  'SelfHealingAIService',
  'UnifiedValidationService',
  'EnhancedQuestionGenerator',
  'withUnifiedErrorHandling',
  'withRateLimit',
  'rateLimiters',
  'prisma'
];

function fixRoute(routePath) {
  console.log(`Fixing route: ${routePath}`);
  
  if (!fs.existsSync(routePath)) {
    console.log(`Route does not exist: ${routePath}`);
    return;
  }
  
  let content = fs.readFileSync(routePath, 'utf8');
  
  // Check if this route has problematic imports
  const hasProblematicImports = problematicImports.some(imp => 
    content.includes(`import`) && content.includes(imp)
  );
  
  if (!hasProblematicImports) {
    console.log(`Route ${routePath} doesn't need fixing`);
    return;
  }
  
  console.log(`Route ${routePath} needs fixing - applying dynamic imports pattern`);
  
  // Remove problematic imports and add dynamic configuration
  const lines = content.split('\n');
  const newLines = [];
  let inImportSection = true;
  let hasNextImports = false;
  let hasAuthImports = false;
  let hasZodImports = false;
  
  for (let line of lines) {
    // Keep essential imports
    if (line.includes("import { NextRequest, NextResponse }")) {
      hasNextImports = true;
      newLines.push(line);
    } else if (line.includes("import { getServerSession }") || line.includes("import { authOptions }")) {
      hasAuthImports = true;
      newLines.push(line);
    } else if (line.includes("import { z }")) {
      hasZodImports = true;
      newLines.push(line);
    } else if (line.startsWith('import') && problematicImports.some(imp => line.includes(imp))) {
      // Skip problematic imports
      continue;
    } else if (line.startsWith('export const dynamic')) {
      // Skip existing dynamic config
      continue;
    } else if (line.startsWith('export const runtime')) {
      // Skip existing runtime config  
      continue;
    } else if (line.startsWith('export const revalidate')) {
      // Skip existing revalidate config
      continue;
    } else {
      if (inImportSection && !line.startsWith('import') && !line.startsWith('//') && line.trim() !== '') {
        // End of import section, add dynamic config
        if (!content.includes('export const dynamic')) {
          newLines.push('');
          newLines.push('// Force dynamic rendering for this route');
          newLines.push("export const dynamic = 'force-dynamic';");
          newLines.push("export const runtime = 'nodejs';");
          newLines.push("export const revalidate = false;");
        }
        inImportSection = false;
      }
      newLines.push(line);
    }
  }
  
  // Write the updated content
  fs.writeFileSync(routePath, newLines.join('\n'));
  console.log(`Fixed imports for ${routePath}`);
}

// Fix all routes
console.log('Starting to fix all problematic routes...');

for (const route of problematicRoutes) {
  try {
    fixRoute(route);
  } catch (error) {
    console.error(`Error fixing ${route}:`, error.message);
  }
}

console.log('Finished fixing routes. Now testing build...');

try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build successful!');
} catch (error) {
  console.error('❌ Build failed, manual fixes needed');
  process.exit(1);
}
