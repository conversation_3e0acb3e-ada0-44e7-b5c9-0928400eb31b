/**
 * Debug the exact signup email process
 */

const React = require('react');
const { Resend } = require('resend');
const { render } = require('@react-email/render');
require('dotenv').config({ path: '.env.local' });

// Import the actual VerificationEmail component
const { VerificationEmail } = require('../emails/VerificationEmail');

async function debugSignupEmail() {
  console.log('🔍 Debugging signup email process...');
  
  const testEmail = '<EMAIL>';
  const verificationToken = 'debug-token-' + Date.now();
  const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(testEmail)}`;
  
  console.log('📧 Test email:', testEmail);
  console.log('🔗 Verification URL:', verificationUrl);
  console.log('📤 From address:', process.env.EMAIL_FROM);
  console.log('🔑 API Key present:', !!process.env.RESEND_API_KEY);
  
  try {
    console.log('1️⃣ Creating React element...');
    const emailTemplate = React.createElement(VerificationEmail, {
      username: testEmail,
      verificationLink: verificationUrl
    });
    console.log('✅ React element created successfully');
    
    console.log('2️⃣ Rendering email template...');
    const html = await render(emailTemplate);
    console.log('✅ Email template rendered successfully');
    console.log('📄 HTML length:', html.length, 'characters');
    
    console.log('3️⃣ Initializing Resend...');
    const resend = new Resend(process.env.RESEND_API_KEY);
    console.log('✅ Resend initialized');
    
    console.log('4️⃣ Sending email...');
    const { data, error } = await resend.emails.send({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: testEmail,
      subject: '🚀 Welcome to FAAFO! Time to verify and start the chaos',
      html
    });
    
    if (error) {
      console.error('❌ Resend error:', error);
      return;
    }
    
    console.log('✅ Email sent successfully!');
    console.log('📧 Email data:', data);
    
  } catch (error) {
    console.error('❌ Error in process:', error);
    console.error('📍 Error stack:', error.stack);
  }
}

debugSignupEmail();
