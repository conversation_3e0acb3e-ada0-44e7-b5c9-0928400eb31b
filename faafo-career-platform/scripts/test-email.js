const { Resend } = require('resend');
require('dotenv').config({ path: '.env.local' });

async function testEmail() {
  console.log('🧪 Testing email configuration...');
  
  // Check if API key exists
  const apiKey = process.env.RESEND_API_KEY;
  if (!apiKey) {
    console.error('❌ RESEND_API_KEY not found in environment variables');
    return;
  }
  
  console.log('✅ RESEND_API_KEY found:', apiKey.substring(0, 10) + '...');
  
  // Initialize Resend
  const resend = new Resend(apiKey);
  
  try {
    console.log('📧 Attempting to send test email...');
    
    const { data, error } = await resend.emails.send({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: '<EMAIL>', // Test with different email once domain is verified
      subject: '🧪 Test Email from FAAFO Platform',
      html: `
        <h1>Test Email</h1>
        <p>This is a test email to verify the Resend configuration.</p>
        <p>If you receive this, the email system is working correctly.</p>
        <p>Sent at: ${new Date().toISOString()}</p>
      `
    });

    if (error) {
      console.error('❌ Error sending email:', error);
      return;
    }

    console.log('✅ Email sent successfully!');
    console.log('📧 Email data:', data);
    
  } catch (error) {
    console.error('❌ Exception while sending email:', error);
  }
}

testEmail();
