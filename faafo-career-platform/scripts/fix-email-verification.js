/**
 * Temporary fix for email verification during development
 * This script manually verifies users who registered but didn't receive verification emails
 */

const { PrismaClient } = require('@prisma/client');
require('dotenv').config({ path: '.env.local' });

const prisma = new PrismaClient();

async function fixEmailVerification() {
  console.log('🔧 Fixing email verification for unverified users...');
  
  try {
    // Find all unverified users
    const unverifiedUsers = await prisma.user.findMany({
      where: {
        emailVerified: null
      },
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true
      }
    });

    if (unverifiedUsers.length === 0) {
      console.log('✅ No unverified users found');
      return;
    }

    console.log(`📋 Found ${unverifiedUsers.length} unverified users:`);
    unverifiedUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.email} (${user.name || 'No name'}) - Created: ${user.createdAt}`);
    });

    // Ask for confirmation (in a real script, you'd want user input)
    console.log('\n🤔 Do you want to manually verify these users? (This is a temporary fix)');
    console.log('   In production, you should fix the domain verification in Resend first.');
    
    // For now, let's verify them automatically
    console.log('\n✅ Verifying all unverified users...');
    
    const result = await prisma.user.updateMany({
      where: {
        emailVerified: null
      },
      data: {
        emailVerified: new Date()
      }
    });

    console.log(`✅ Successfully verified ${result.count} users`);
    console.log('\n⚠️  IMPORTANT: This is a temporary fix!');
    console.log('   To fix the root cause:');
    console.log('   1. Go to https://resend.com/domains');
    console.log('   2. Verify your domain (faafocareer.com)');
    console.log('   3. Update EMAIL_FROM in .env.local to use your verified domain');
    console.log('   4. Get a production API key if needed');

  } catch (error) {
    console.error('❌ Error fixing email verification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixEmailVerification();
