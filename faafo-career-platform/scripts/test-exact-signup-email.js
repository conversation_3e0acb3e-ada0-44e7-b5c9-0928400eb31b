/**
 * Test the exact signup email flow with React template
 */

const React = require('react');
const { sendEmail } = require('../src/lib/email');
const { VerificationEmail } = require('../emails/VerificationEmail');
require('dotenv').config({ path: '.env.local' });

async function testExactSignupEmail() {
  console.log('🧪 Testing exact signup email flow with React template...');
  
  const testEmail = '<EMAIL>';
  const verificationToken = 'test-token-' + Date.now();
  const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(testEmail)}`;
  
  console.log('📧 Test email:', testEmail);
  console.log('🔗 Verification URL:', verificationUrl);
  console.log('📤 From address:', process.env.EMAIL_FROM);
  
  try {
    console.log('📧 Sending verification email with React template...');
    
    const result = await sendEmail({
      to: testEmail,
      subject: '🚀 Welcome to FAAFO! Time to verify and start the chaos',
      template: React.createElement(VerificationEmail, {
        username: testEmail,
        verificationLink: verificationUrl
      })
    });
    
    console.log('✅ Email result:', result);
    
    if (result.success) {
      console.log('✅ Email sent successfully with React template!');
      console.log('📧 Email ID:', result.data?.id);
    } else {
      console.log('❌ Email failed:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Error sending email:', error);
  }
}

testExactSignupEmail();
