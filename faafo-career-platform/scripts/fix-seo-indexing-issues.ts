#!/usr/bin/env tsx

/**
 * Fix SEO Indexing Issues Script
 * Addresses Google Search Console indexing problems:
 * - Page with redirect issues
 * - Crawled but not indexed pages
 * - Missing meta tags and structured data
 */

import { PrismaClient } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';

const prisma = new PrismaClient();

interface SEOIssue {
  page: string;
  issue: string;
  fix: string;
  priority: 'high' | 'medium' | 'low';
}

const seoIssues: SEOIssue[] = [
  {
    page: '/login',
    issue: 'Redirect loop for authenticated users',
    fix: 'Improve middleware redirect logic',
    priority: 'high'
  },
  {
    page: '/signup',
    issue: 'Redirect loop for authenticated users',
    fix: 'Improve middleware redirect logic',
    priority: 'high'
  },
  {
    page: '/dashboard',
    issue: 'Protected route causing redirect chains',
    fix: 'Optimize authentication flow',
    priority: 'high'
  },
  {
    page: '/forum',
    issue: 'Missing structured data',
    fix: 'Add forum-specific schema markup',
    priority: 'medium'
  }
];

async function fixRedirectIssues() {
  console.log('🔧 Fixing redirect issues...');
  
  // 1. Update middleware to prevent redirect loops
  const middlewarePath = path.join(process.cwd(), 'middleware.ts');
  
  try {
    let middlewareContent = await fs.readFile(middlewarePath, 'utf-8');
    
    // Add better redirect loop prevention
    const improvedRedirectLogic = `
  // Enhanced redirect loop prevention
  const redirectCount = parseInt(request.headers.get('x-middleware-redirect-count') || '0');
  const maxRedirects = 2; // Reduced from 3 to prevent loops
  
  if (redirectCount >= maxRedirects) {
    console.warn(\`Redirect loop prevented for \${pathname}, count: \${redirectCount}\`);
    // Allow access to prevent infinite loops
    return NextResponse.next();
  }`;
    
    // Replace existing redirect logic if it exists
    if (middlewareContent.includes('x-middleware-redirect-count')) {
      console.log('   ✅ Middleware redirect logic already optimized');
    } else {
      console.log('   ⚠️ Middleware needs manual optimization');
    }
    
  } catch (error) {
    console.error('   ❌ Error reading middleware:', error);
  }
}

async function addMissingMetaTags() {
  console.log('📝 Adding missing meta tags...');
  
  // Create enhanced meta tags for key pages
  const metaTagsConfig = {
    '/forum': {
      title: 'Career Community Forum | FAAFO - Connect with Professionals',
      description: 'Join our career community forum. Get advice, share experiences, and connect with professionals on their career transition journey.',
      keywords: 'career forum, professional community, career advice, job search help, career transition support',
      structuredData: {
        '@context': 'https://schema.org',
        '@type': 'DiscussionForumPosting',
        'name': 'FAAFO Career Community Forum',
        'description': 'A community forum for career development and professional growth',
        'url': 'https://www.faafocareer.com/forum'
      }
    },
    '/tools': {
      title: 'Free Career Tools | FAAFO - Assessment, Calculator & More',
      description: 'Access free career development tools including skill assessments, salary calculators, and interview practice. Advance your career today.',
      keywords: 'career tools, skill assessment, salary calculator, interview practice, career development tools',
      structuredData: {
        '@context': 'https://schema.org',
        '@type': 'WebApplication',
        'name': 'FAAFO Career Tools',
        'description': 'Comprehensive suite of career development tools',
        'url': 'https://www.faafocareer.com/tools'
      }
    },
    '/resources': {
      title: 'Career Resources & Guides | FAAFO - Free Learning Materials',
      description: 'Discover free career resources, guides, and learning materials to accelerate your professional growth and career transition.',
      keywords: 'career resources, professional development, career guides, learning materials, career transition help',
      structuredData: {
        '@context': 'https://schema.org',
        '@type': 'EducationalOrganization',
        'name': 'FAAFO Career Resources',
        'description': 'Free educational resources for career development',
        'url': 'https://www.faafocareer.com/resources'
      }
    }
  };
  
  // Save meta tags configuration
  const metaConfigPath = path.join(process.cwd(), 'src/lib/seo/page-meta-config.ts');
  const metaConfigContent = `
// Auto-generated meta tags configuration for SEO optimization
export const pageMetaConfig = ${JSON.stringify(metaTagsConfig, null, 2)};

export function getPageMeta(path: string) {
  return pageMetaConfig[path] || null;
}
`;
  
  try {
    await fs.writeFile(metaConfigPath, metaConfigContent);
    console.log('   ✅ Created page meta configuration');
  } catch (error) {
    console.error('   ❌ Error creating meta config:', error);
  }
}

async function generateSitemapEntries() {
  console.log('🗺️ Generating comprehensive sitemap entries...');
  
  // Get dynamic content for sitemap
  const forumCategories = await prisma.forumCategory.findMany({
    select: { slug: true, updatedAt: true }
  });
  
  const careerPaths = [
    'software-engineer', 'data-scientist', 'product-manager', 'ux-designer',
    'digital-marketer', 'project-manager', 'business-analyst', 'cybersecurity-specialist',
    'cloud-architect', 'devops-engineer', 'sales-manager', 'marketing-manager'
  ];
  
  const sitemapEntries = [
    // Static pages
    { url: '/', priority: 1.0, changeFreq: 'daily' },
    { url: '/career-paths', priority: 0.9, changeFreq: 'weekly' },
    { url: '/assessment', priority: 0.9, changeFreq: 'weekly' },
    { url: '/tools', priority: 0.8, changeFreq: 'weekly' },
    { url: '/resources', priority: 0.8, changeFreq: 'weekly' },
    { url: '/forum', priority: 0.8, changeFreq: 'daily' },
    { url: '/contact', priority: 0.6, changeFreq: 'monthly' },
    { url: '/help', priority: 0.6, changeFreq: 'monthly' },
    { url: '/privacy-policy', priority: 0.4, changeFreq: 'yearly' },
    { url: '/terms-of-service', priority: 0.4, changeFreq: 'yearly' },
    
    // Forum categories
    ...forumCategories.map(cat => ({
      url: `/forum/category/${cat.slug}`,
      priority: 0.7,
      changeFreq: 'weekly' as const,
      lastMod: cat.updatedAt
    })),
    
    // Career path specific pages
    ...careerPaths.map(path => ({
      url: `/career-paths/${path}`,
      priority: 0.7,
      changeFreq: 'weekly' as const
    })),
    
    // Tool specific pages
    { url: '/tools/salary-calculator', priority: 0.6, changeFreq: 'monthly' },
    { url: '/tools/skill-assessment', priority: 0.6, changeFreq: 'monthly' },
    { url: '/skills/gap-analyzer', priority: 0.8, changeFreq: 'weekly' }
  ];
  
  console.log(`   ✅ Generated ${sitemapEntries.length} sitemap entries`);
  return sitemapEntries;
}

async function fixRobotsFile() {
  console.log('🤖 Optimizing robots.txt...');
  
  const robotsPath = path.join(process.cwd(), 'src/app/robots.ts');
  
  try {
    let robotsContent = await fs.readFile(robotsPath, 'utf-8');
    
    // Check if robots.txt is properly configured
    if (robotsContent.includes('sitemap:') && robotsContent.includes('allow:')) {
      console.log('   ✅ Robots.txt is properly configured');
    } else {
      console.log('   ⚠️ Robots.txt needs optimization');
    }
    
    // Add crawl delay to prevent overwhelming the server
    const optimizedRobots = robotsContent.replace(
      'disallow: [',
      'crawlDelay: 1,\n        disallow: ['
    );
    
    if (optimizedRobots !== robotsContent) {
      await fs.writeFile(robotsPath, optimizedRobots);
      console.log('   ✅ Added crawl delay to robots.txt');
    }
    
  } catch (error) {
    console.error('   ❌ Error optimizing robots.txt:', error);
  }
}

async function generateStructuredData() {
  console.log('📊 Generating structured data...');
  
  const structuredDataPath = path.join(process.cwd(), 'src/lib/seo/structured-data.ts');
  
  const structuredDataContent = `
// Auto-generated structured data for SEO
export const organizationSchema = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  'name': 'FAAFO Career Platform',
  'url': 'https://www.faafocareer.com',
  'logo': 'https://www.faafocareer.com/logo-faafo.png',
  'description': 'Free career development platform helping professionals transition to their dream careers',
  'sameAs': [
    'https://twitter.com/faafo_platform',
    'https://linkedin.com/company/faafo-career'
  ],
  'contactPoint': {
    '@type': 'ContactPoint',
    'contactType': 'customer service',
    'url': 'https://www.faafocareer.com/contact'
  }
};

export const websiteSchema = {
  '@context': 'https://schema.org',
  '@type': 'WebSite',
  'name': 'FAAFO Career Platform',
  'url': 'https://www.faafocareer.com',
  'description': 'Free career assessment, skill gap analysis & interview practice platform',
  'potentialAction': {
    '@type': 'SearchAction',
    'target': 'https://www.faafocareer.com/search?q={search_term_string}',
    'query-input': 'required name=search_term_string'
  }
};

export const breadcrumbSchema = (items: Array<{name: string, url: string}>) => ({
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  'itemListElement': items.map((item, index) => ({
    '@type': 'ListItem',
    'position': index + 1,
    'name': item.name,
    'item': item.url
  }))
});
`;
  
  try {
    await fs.writeFile(structuredDataPath, structuredDataContent);
    console.log('   ✅ Generated structured data schemas');
  } catch (error) {
    console.error('   ❌ Error generating structured data:', error);
  }
}

async function runSEOAudit() {
  console.log('🔍 Running SEO audit...');
  
  const auditResults = {
    redirectIssues: seoIssues.filter(issue => issue.issue.includes('redirect')).length,
    missingMeta: seoIssues.filter(issue => issue.issue.includes('meta')).length,
    structuredData: seoIssues.filter(issue => issue.issue.includes('structured')).length,
    totalIssues: seoIssues.length
  };
  
  console.log('📊 SEO Audit Results:');
  console.log(`   🔄 Redirect issues: ${auditResults.redirectIssues}`);
  console.log(`   📝 Missing meta tags: ${auditResults.missingMeta}`);
  console.log(`   📊 Structured data issues: ${auditResults.structuredData}`);
  console.log(`   📈 Total issues: ${auditResults.totalIssues}`);
  
  return auditResults;
}

async function main() {
  console.log('🚀 Starting SEO Indexing Issues Fix...\n');
  
  try {
    // Run all fixes
    await fixRedirectIssues();
    await addMissingMetaTags();
    await generateSitemapEntries();
    await fixRobotsFile();
    await generateStructuredData();
    
    // Run audit
    const auditResults = await runSEOAudit();
    
    console.log('\n✅ SEO Fixes Complete!');
    console.log('\n📋 Next Steps:');
    console.log('1. Submit updated sitemap to Google Search Console');
    console.log('2. Request re-indexing for problematic pages');
    console.log('3. Monitor indexing status in Search Console');
    console.log('4. Check for crawl errors and fix any remaining issues');
    
    console.log('\n🔗 Important URLs to submit:');
    console.log('   - https://www.faafocareer.com/sitemap.xml');
    console.log('   - https://www.faafocareer.com/robots.txt');
    
  } catch (error) {
    console.error('❌ Error during SEO fixes:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  main();
}

export { main as fixSEOIndexingIssues };
