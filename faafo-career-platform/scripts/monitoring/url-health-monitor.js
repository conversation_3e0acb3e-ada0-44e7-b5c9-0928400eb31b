#!/usr/bin/env node

/**
 * FAAFO Career Platform - URL Health Monitoring Script
 * 
 * Automated script for periodic URL health checking
 * Designed for weekly/monthly execution via cron job
 * 
 * Features:
 * - Tests all learning resource URLs
 * - Generates detailed health reports
 * - Sends alerts for broken links
 * - Tracks URL health trends over time
 * - Supports email notifications
 * 
 * Usage:
 *   node scripts/monitoring/url-health-monitor.js
 *   node scripts/monitoring/url-health-monitor.js --email
 *   node scripts/monitoring/url-health-monitor.js --verbose
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Configuration
const CONFIG = {
  // Request timeout in milliseconds
  REQUEST_TIMEOUT: 10000,
  
  // Maximum concurrent requests
  MAX_CONCURRENT: 5,
  
  // Retry attempts for failed requests
  MAX_RETRIES: 2,
  
  // Alert thresholds
  ALERT_THRESHOLD_BROKEN_URLS: 5,
  ALERT_THRESHOLD_FAILURE_RATE: 10, // percentage
  
  // Report storage
  REPORTS_DIR: 'docs/monitoring',
  HISTORICAL_REPORTS_LIMIT: 30, // Keep last 30 reports
  
  // Email configuration (if enabled)
  EMAIL_ENABLED: process.argv.includes('--email'),
  EMAIL_RECIPIENTS: ['<EMAIL>'], // Configure as needed
  
  // Verbose logging
  VERBOSE: process.argv.includes('--verbose') || process.argv.includes('-v')
};

class URLHealthMonitor {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      summary: {
        totalResources: 0,
        workingUrls: 0,
        brokenUrls: 0,
        timeoutUrls: 0,
        redirectUrls: 0,
        successRate: 0,
        averageResponseTime: 0
      },
      details: [],
      alerts: [],
      recommendations: []
    };
    
    this.startTime = Date.now();
  }

  async run() {
    try {
      console.log('🔍 FAAFO URL Health Monitor Starting...');
      console.log('=====================================\n');

      // Ensure reports directory exists
      this.ensureReportsDirectory();

      // Fetch all active learning resources
      const resources = await this.fetchResources();
      this.results.summary.totalResources = resources.length;

      console.log(`📊 Testing ${resources.length} learning resource URLs...\n`);

      // Test URLs in batches to avoid overwhelming servers
      await this.testUrlsInBatches(resources);

      // Calculate summary statistics
      this.calculateSummary();

      // Generate alerts if needed
      this.generateAlerts();

      // Generate recommendations
      this.generateRecommendations();

      // Save report
      const reportPath = await this.saveReport();

      // Clean up old reports
      this.cleanupOldReports();

      // Display results
      this.displayResults();

      // Send email alerts if configured
      if (CONFIG.EMAIL_ENABLED) {
        await this.sendEmailAlerts();
      }

      console.log(`\n💾 Report saved to: ${reportPath}`);
      console.log(`⏱️  Total execution time: ${((Date.now() - this.startTime) / 1000).toFixed(2)}s`);

      return this.results;

    } catch (error) {
      console.error('❌ Error during URL health monitoring:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  async fetchResources() {
    return await prisma.learningResource.findMany({
      where: { isActive: true },
      select: {
        id: true,
        title: true,
        url: true,
        author: true,
        category: true,
        careerPaths: {
          select: {
            name: true
          }
        }
      },
      orderBy: { title: 'asc' }
    });
  }

  async testUrlsInBatches(resources) {
    const batches = this.createBatches(resources, CONFIG.MAX_CONCURRENT);
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`Testing batch ${i + 1}/${batches.length} (${batch.length} URLs)...`);
      
      const promises = batch.map(resource => this.testUrl(resource));
      await Promise.all(promises);
      
      // Small delay between batches to be respectful to servers
      if (i < batches.length - 1) {
        await this.delay(1000);
      }
    }
  }

  createBatches(array, batchSize) {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  async testUrl(resource, retryCount = 0) {
    const startTime = Date.now();
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), CONFIG.REQUEST_TIMEOUT);

      const response = await fetch(resource.url, {
        method: 'HEAD', // Use HEAD to avoid downloading content
        signal: controller.signal,
        headers: {
          'User-Agent': 'FAAFO-URL-Health-Monitor/1.0'
        }
      });

      clearTimeout(timeoutId);
      const responseTime = Date.now() - startTime;

      const result = {
        resourceId: resource.id,
        title: resource.title,
        url: resource.url,
        author: resource.author,
        category: resource.category,
        careerPaths: resource.careerPaths.map(cp => cp.name),
        status: response.status,
        statusText: response.statusText,
        responseTime,
        isWorking: response.ok,
        isRedirect: response.status >= 300 && response.status < 400,
        error: null,
        testedAt: new Date().toISOString()
      };

      // Log verbose output
      if (CONFIG.VERBOSE) {
        const status = result.isWorking ? '✅' : '❌';
        console.log(`  ${status} ${result.title} (${result.status}) - ${responseTime}ms`);
      }

      this.results.details.push(result);

      // Update counters
      if (result.isWorking) {
        this.results.summary.workingUrls++;
      } else {
        this.results.summary.brokenUrls++;
      }

      if (result.isRedirect) {
        this.results.summary.redirectUrls++;
      }

      return result;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      // Handle timeout specifically
      if (error.name === 'AbortError') {
        this.results.summary.timeoutUrls++;
        
        const result = {
          resourceId: resource.id,
          title: resource.title,
          url: resource.url,
          author: resource.author,
          category: resource.category,
          careerPaths: resource.careerPaths.map(cp => cp.name),
          status: 0,
          statusText: 'Timeout',
          responseTime,
          isWorking: false,
          isRedirect: false,
          error: 'Request timeout',
          testedAt: new Date().toISOString()
        };

        if (CONFIG.VERBOSE) {
          console.log(`  ⏰ ${result.title} - TIMEOUT (${responseTime}ms)`);
        }

        this.results.details.push(result);
        this.results.summary.brokenUrls++;
        return result;
      }

      // Retry logic for network errors
      if (retryCount < CONFIG.MAX_RETRIES) {
        console.log(`  🔄 Retrying ${resource.title} (attempt ${retryCount + 1}/${CONFIG.MAX_RETRIES})`);
        await this.delay(2000); // Wait 2 seconds before retry
        return this.testUrl(resource, retryCount + 1);
      }

      // Final failure
      const result = {
        resourceId: resource.id,
        title: resource.title,
        url: resource.url,
        author: resource.author,
        category: resource.category,
        careerPaths: resource.careerPaths.map(cp => cp.name),
        status: 0,
        statusText: 'Error',
        responseTime,
        isWorking: false,
        isRedirect: false,
        error: error.message,
        testedAt: new Date().toISOString()
      };

      if (CONFIG.VERBOSE) {
        console.log(`  ❌ ${result.title} - ERROR: ${error.message}`);
      }

      this.results.details.push(result);
      this.results.summary.brokenUrls++;
      return result;
    }
  }

  calculateSummary() {
    const { totalResources, workingUrls, brokenUrls } = this.results.summary;
    
    this.results.summary.successRate = totalResources > 0 
      ? ((workingUrls / totalResources) * 100).toFixed(2)
      : 0;

    const responseTimes = this.results.details
      .filter(r => r.responseTime > 0)
      .map(r => r.responseTime);
    
    this.results.summary.averageResponseTime = responseTimes.length > 0
      ? Math.round(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length)
      : 0;
  }

  generateAlerts() {
    const { brokenUrls, successRate } = this.results.summary;

    // Alert for too many broken URLs
    if (brokenUrls >= CONFIG.ALERT_THRESHOLD_BROKEN_URLS) {
      this.results.alerts.push({
        type: 'HIGH_BROKEN_URL_COUNT',
        severity: 'HIGH',
        message: `${brokenUrls} broken URLs detected (threshold: ${CONFIG.ALERT_THRESHOLD_BROKEN_URLS})`,
        action: 'Immediate review and fixing required'
      });
    }

    // Alert for low success rate
    if (parseFloat(successRate) < (100 - CONFIG.ALERT_THRESHOLD_FAILURE_RATE)) {
      this.results.alerts.push({
        type: 'LOW_SUCCESS_RATE',
        severity: 'MEDIUM',
        message: `URL success rate is ${successRate}% (threshold: ${100 - CONFIG.ALERT_THRESHOLD_FAILURE_RATE}%)`,
        action: 'Review and fix failing URLs'
      });
    }

    // Alert for timeout issues
    if (this.results.summary.timeoutUrls > 3) {
      this.results.alerts.push({
        type: 'HIGH_TIMEOUT_COUNT',
        severity: 'MEDIUM',
        message: `${this.results.summary.timeoutUrls} URLs are timing out`,
        action: 'Check network connectivity and server responsiveness'
      });
    }
  }

  generateRecommendations() {
    const brokenResources = this.results.details.filter(r => !r.isWorking);
    const slowResources = this.results.details.filter(r => r.responseTime > 5000);

    if (brokenResources.length > 0) {
      this.results.recommendations.push({
        type: 'FIX_BROKEN_URLS',
        priority: 'HIGH',
        description: `Fix ${brokenResources.length} broken URLs`,
        resources: brokenResources.map(r => ({
          title: r.title,
          url: r.url,
          error: r.error || `HTTP ${r.status}`
        }))
      });
    }

    if (slowResources.length > 0) {
      this.results.recommendations.push({
        type: 'OPTIMIZE_SLOW_RESOURCES',
        priority: 'MEDIUM',
        description: `${slowResources.length} resources have slow response times (>5s)`,
        resources: slowResources.map(r => ({
          title: r.title,
          url: r.url,
          responseTime: `${r.responseTime}ms`
        }))
      });
    }

    if (this.results.summary.redirectUrls > 0) {
      this.results.recommendations.push({
        type: 'UPDATE_REDIRECTED_URLS',
        priority: 'LOW',
        description: `${this.results.summary.redirectUrls} URLs are redirecting`,
        action: 'Consider updating URLs to their final destinations'
      });
    }
  }

  ensureReportsDirectory() {
    const reportsDir = path.join(process.cwd(), CONFIG.REPORTS_DIR);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
  }

  async saveReport() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `url-health-report-${timestamp}.json`;
    const filepath = path.join(process.cwd(), CONFIG.REPORTS_DIR, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(this.results, null, 2));
    
    // Also save a "latest" report for easy access
    const latestPath = path.join(process.cwd(), CONFIG.REPORTS_DIR, 'url-health-latest.json');
    fs.writeFileSync(latestPath, JSON.stringify(this.results, null, 2));
    
    return filepath;
  }

  cleanupOldReports() {
    const reportsDir = path.join(process.cwd(), CONFIG.REPORTS_DIR);
    const files = fs.readdirSync(reportsDir)
      .filter(file => file.startsWith('url-health-report-') && file.endsWith('.json'))
      .map(file => ({
        name: file,
        path: path.join(reportsDir, file),
        mtime: fs.statSync(path.join(reportsDir, file)).mtime
      }))
      .sort((a, b) => b.mtime - a.mtime);

    // Keep only the most recent reports
    if (files.length > CONFIG.HISTORICAL_REPORTS_LIMIT) {
      const filesToDelete = files.slice(CONFIG.HISTORICAL_REPORTS_LIMIT);
      filesToDelete.forEach(file => {
        fs.unlinkSync(file.path);
        console.log(`🗑️  Cleaned up old report: ${file.name}`);
      });
    }
  }

  displayResults() {
    const { summary, alerts, recommendations } = this.results;
    
    console.log('\n📊 URL HEALTH MONITORING RESULTS');
    console.log('=================================');
    console.log(`Total Resources: ${summary.totalResources}`);
    console.log(`Working URLs: ${summary.workingUrls} ✅`);
    console.log(`Broken URLs: ${summary.brokenUrls} ❌`);
    console.log(`Timeout URLs: ${summary.timeoutUrls} ⏰`);
    console.log(`Redirect URLs: ${summary.redirectUrls} 🔄`);
    console.log(`Success Rate: ${summary.successRate}%`);
    console.log(`Average Response Time: ${summary.averageResponseTime}ms`);

    if (alerts.length > 0) {
      console.log('\n🚨 ALERTS:');
      alerts.forEach(alert => {
        console.log(`  ${alert.severity}: ${alert.message}`);
        console.log(`  Action: ${alert.action}`);
      });
    }

    if (recommendations.length > 0) {
      console.log('\n💡 RECOMMENDATIONS:');
      recommendations.forEach(rec => {
        console.log(`  ${rec.priority}: ${rec.description}`);
      });
    }

    if (summary.brokenUrls === 0) {
      console.log('\n🎉 All URLs are working perfectly!');
    }
  }

  async sendEmailAlerts() {
    // Email implementation would go here
    // This is a placeholder for email notification functionality
    console.log('📧 Email alerts would be sent here (not implemented)');
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// CLI execution
if (require.main === module) {
  const monitor = new URLHealthMonitor();
  monitor.run()
    .then(results => {
      const exitCode = results.summary.brokenUrls > 0 ? 1 : 0;
      process.exit(exitCode);
    })
    .catch(error => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}

module.exports = { URLHealthMonitor };

// Cron job setup instructions (add to your crontab):
//
// Weekly monitoring (every Monday at 9 AM):
// 0 9 * * 1 cd /path/to/faafo-career-platform && node scripts/monitoring/url-health-monitor.js
//
// Monthly monitoring (1st of each month at 9 AM):
// 0 9 1 * * cd /path/to/faafo-career-platform && node scripts/monitoring/url-health-monitor.js --email
//
// Daily monitoring (every day at 6 AM, verbose):
// 0 6 * * * cd /path/to/faafo-career-platform && node scripts/monitoring/url-health-monitor.js --verbose
