#!/bin/bash

# FAAFO Career Platform - Monitoring Setup Script
# 
# This script sets up automated URL health monitoring
# for the FAAFO Career Platform learning resources.
#
# Usage:
#   chmod +x scripts/monitoring/setup-monitoring.sh
#   ./scripts/monitoring/setup-monitoring.sh

set -e

echo "🔧 FAAFO Career Platform - Monitoring Setup"
echo "==========================================="
echo ""

# Get the absolute path to the project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
MONITORING_DIR="$PROJECT_DIR/scripts/monitoring"
REPORTS_DIR="$PROJECT_DIR/docs/monitoring"

echo "📁 Project Directory: $PROJECT_DIR"
echo "📁 Monitoring Scripts: $MONITORING_DIR"
echo "📁 Reports Directory: $REPORTS_DIR"
echo ""

# Create necessary directories
echo "📂 Creating directories..."
mkdir -p "$REPORTS_DIR"
mkdir -p "$PROJECT_DIR/logs/monitoring"

# Make monitoring script executable
echo "🔧 Making monitoring script executable..."
chmod +x "$MONITORING_DIR/url-health-monitor.js"

# Test the monitoring script
echo "🧪 Testing monitoring script..."
echo "Running a test execution (this may take a few minutes)..."
cd "$PROJECT_DIR"

if node scripts/monitoring/url-health-monitor.js --verbose; then
    echo "✅ Monitoring script test completed successfully!"
else
    echo "❌ Monitoring script test failed!"
    exit 1
fi

echo ""
echo "📋 CRON JOB SETUP OPTIONS"
echo "========================="
echo ""
echo "Choose your monitoring frequency:"
echo ""
echo "1. Weekly Monitoring (Recommended)"
echo "   - Runs every Monday at 9:00 AM"
echo "   - Good balance of monitoring and resource usage"
echo ""
echo "2. Monthly Monitoring"
echo "   - Runs on the 1st of each month at 9:00 AM"
echo "   - Minimal resource usage, good for stable systems"
echo ""
echo "3. Daily Monitoring"
echo "   - Runs every day at 6:00 AM"
echo "   - Maximum monitoring, higher resource usage"
echo ""
echo "4. Custom Setup"
echo "   - Manual cron job configuration"
echo ""

read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        CRON_SCHEDULE="0 9 * * 1"
        CRON_COMMAND="cd $PROJECT_DIR && node scripts/monitoring/url-health-monitor.js >> logs/monitoring/weekly.log 2>&1"
        DESCRIPTION="Weekly monitoring (Mondays at 9 AM)"
        ;;
    2)
        CRON_SCHEDULE="0 9 1 * *"
        CRON_COMMAND="cd $PROJECT_DIR && node scripts/monitoring/url-health-monitor.js --email >> logs/monitoring/monthly.log 2>&1"
        DESCRIPTION="Monthly monitoring (1st of month at 9 AM with email)"
        ;;
    3)
        CRON_SCHEDULE="0 6 * * *"
        CRON_COMMAND="cd $PROJECT_DIR && node scripts/monitoring/url-health-monitor.js >> logs/monitoring/daily.log 2>&1"
        DESCRIPTION="Daily monitoring (6 AM)"
        ;;
    4)
        echo ""
        echo "📝 MANUAL CRON JOB SETUP"
        echo "========================"
        echo ""
        echo "To set up monitoring manually, add one of these lines to your crontab:"
        echo "(Run 'crontab -e' to edit your cron jobs)"
        echo ""
        echo "Weekly (Mondays at 9 AM):"
        echo "0 9 * * 1 cd $PROJECT_DIR && node scripts/monitoring/url-health-monitor.js"
        echo ""
        echo "Monthly (1st of month at 9 AM with email):"
        echo "0 9 1 * * cd $PROJECT_DIR && node scripts/monitoring/url-health-monitor.js --email"
        echo ""
        echo "Daily (6 AM with verbose output):"
        echo "0 6 * * * cd $PROJECT_DIR && node scripts/monitoring/url-health-monitor.js --verbose"
        echo ""
        echo "✅ Manual setup information provided!"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice. Exiting."
        exit 1
        ;;
esac

echo ""
echo "⚙️  SETTING UP AUTOMATED MONITORING"
echo "==================================="
echo ""
echo "Schedule: $DESCRIPTION"
echo "Command: $CRON_COMMAND"
echo ""

# Check if cron is available
if ! command -v crontab &> /dev/null; then
    echo "❌ crontab command not found. Please install cron or set up monitoring manually."
    exit 1
fi

# Backup existing crontab
echo "💾 Backing up existing crontab..."
crontab -l > "$PROJECT_DIR/logs/monitoring/crontab-backup-$(date +%Y%m%d-%H%M%S).txt" 2>/dev/null || echo "No existing crontab found."

# Add the new cron job
echo "➕ Adding cron job..."
(crontab -l 2>/dev/null; echo "# FAAFO Career Platform URL Health Monitoring"; echo "$CRON_SCHEDULE $CRON_COMMAND") | crontab -

echo "✅ Cron job added successfully!"
echo ""

# Create log rotation setup
echo "📜 Setting up log rotation..."
cat > "$PROJECT_DIR/logs/monitoring/logrotate.conf" << EOF
# FAAFO Career Platform Monitoring Log Rotation
$PROJECT_DIR/logs/monitoring/*.log {
    weekly
    rotate 12
    compress
    delaycompress
    missingok
    notifempty
    create 644
}
EOF

echo "✅ Log rotation configuration created!"
echo ""

# Create monitoring status script
echo "📊 Creating monitoring status script..."
cat > "$MONITORING_DIR/monitoring-status.sh" << 'EOF'
#!/bin/bash

# Quick status check for FAAFO monitoring system

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
REPORTS_DIR="$PROJECT_DIR/docs/monitoring"

echo "🔍 FAAFO Monitoring System Status"
echo "================================="
echo ""

# Check if latest report exists
if [ -f "$REPORTS_DIR/url-health-latest.json" ]; then
    echo "📊 Latest Report:"
    LAST_RUN=$(jq -r '.timestamp' "$REPORTS_DIR/url-health-latest.json" 2>/dev/null || echo "Unknown")
    TOTAL_RESOURCES=$(jq -r '.summary.totalResources' "$REPORTS_DIR/url-health-latest.json" 2>/dev/null || echo "Unknown")
    WORKING_URLS=$(jq -r '.summary.workingUrls' "$REPORTS_DIR/url-health-latest.json" 2>/dev/null || echo "Unknown")
    BROKEN_URLS=$(jq -r '.summary.brokenUrls' "$REPORTS_DIR/url-health-latest.json" 2>/dev/null || echo "Unknown")
    SUCCESS_RATE=$(jq -r '.summary.successRate' "$REPORTS_DIR/url-health-latest.json" 2>/dev/null || echo "Unknown")
    
    echo "  Last Run: $LAST_RUN"
    echo "  Total Resources: $TOTAL_RESOURCES"
    echo "  Working URLs: $WORKING_URLS"
    echo "  Broken URLs: $BROKEN_URLS"
    echo "  Success Rate: $SUCCESS_RATE%"
else
    echo "❌ No monitoring reports found. Run the monitoring script first."
fi

echo ""

# Check cron job status
echo "⏰ Cron Job Status:"
if crontab -l 2>/dev/null | grep -q "url-health-monitor.js"; then
    echo "✅ Monitoring cron job is active"
    echo "📅 Scheduled jobs:"
    crontab -l 2>/dev/null | grep "url-health-monitor.js" | while read line; do
        echo "  $line"
    done
else
    echo "❌ No monitoring cron job found"
fi

echo ""

# Check recent log files
echo "📜 Recent Logs:"
LOG_DIR="$PROJECT_DIR/logs/monitoring"
if [ -d "$LOG_DIR" ]; then
    find "$LOG_DIR" -name "*.log" -mtime -7 | head -5 | while read logfile; do
        echo "  $(basename "$logfile") ($(stat -c %y "$logfile" | cut -d' ' -f1))"
    done
else
    echo "  No log directory found"
fi

echo ""
echo "🔧 Commands:"
echo "  Run monitoring now: node scripts/monitoring/url-health-monitor.js"
echo "  View latest report: cat docs/monitoring/url-health-latest.json | jq"
echo "  Edit cron jobs: crontab -e"
EOF

chmod +x "$MONITORING_DIR/monitoring-status.sh"

echo "✅ Monitoring status script created!"
echo ""

# Final summary
echo "🎉 MONITORING SETUP COMPLETE!"
echo "============================="
echo ""
echo "✅ Monitoring script installed and tested"
echo "✅ Cron job scheduled: $DESCRIPTION"
echo "✅ Log rotation configured"
echo "✅ Status checking script created"
echo ""
echo "📋 NEXT STEPS:"
echo ""
echo "1. Check monitoring status:"
echo "   ./scripts/monitoring/monitoring-status.sh"
echo ""
echo "2. View latest monitoring report:"
echo "   cat docs/monitoring/url-health-latest.json | jq"
echo ""
echo "3. Run monitoring manually:"
echo "   node scripts/monitoring/url-health-monitor.js"
echo ""
echo "4. Check cron jobs:"
echo "   crontab -l"
echo ""
echo "📁 Important Files:"
echo "   - Monitoring script: scripts/monitoring/url-health-monitor.js"
echo "   - Status checker: scripts/monitoring/monitoring-status.sh"
echo "   - Reports directory: docs/monitoring/"
echo "   - Logs directory: logs/monitoring/"
echo ""
echo "🔔 The monitoring system will now automatically check URL health"
echo "   according to your selected schedule and generate reports."
echo ""
echo "✨ Setup completed successfully!"
