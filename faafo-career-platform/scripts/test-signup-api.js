/**
 * Test the signup API endpoint directly
 */

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config({ path: '.env.local' });

async function testSignupAPI() {
  console.log('🧪 Testing signup API endpoint...');
  
  const testEmail = `test${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';
  
  console.log('📧 Test email:', testEmail);
  console.log('🔑 Test password:', testPassword);
  
  try {
    console.log('📤 Sending signup request...');
    
    const response = await fetch('http://localhost:3000/api/signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword,
        name: 'Test User'
      })
    });
    
    const result = await response.json();
    
    console.log('📊 Response status:', response.status);
    console.log('📧 Response body:', result);
    
    if (response.ok) {
      console.log('✅ Signup successful!');
      console.log('📧 Check if verification email was sent (should appear in server logs)');
    } else {
      console.log('❌ Signup failed:', result);
    }
    
  } catch (error) {
    console.error('❌ Error testing signup:', error);
  }
}

testSignupAPI();
