#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Fixing Prisma import statements...');

// Get all files with incorrect prisma imports
const result = execSync('grep -r "import { prisma }" src/ --include="*.ts" --include="*.tsx" -l', { 
  encoding: 'utf8',
  cwd: process.cwd()
});

const files = result.trim().split('\n').filter(f => f.length > 0);

console.log(`📁 Found ${files.length} files with incorrect prisma imports`);

let fixedCount = 0;
let errorCount = 0;

files.forEach(filePath => {
  try {
    console.log(`🔄 Fixing: ${filePath}`);
    
    // Read file content
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Replace the import statement
    const fixedContent = content.replace(
      /import { prisma } from '@\/lib\/prisma';/g,
      "import prisma from '@/lib/prisma';"
    );
    
    // Write back if changed
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      fixedCount++;
    } else {
      console.log(`⚠️  No changes needed: ${filePath}`);
    }
    
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    errorCount++;
  }
});

console.log('\n📊 Summary:');
console.log(`✅ Fixed: ${fixedCount} files`);
console.log(`❌ Errors: ${errorCount} files`);
console.log(`📁 Total processed: ${files.length} files`);

if (fixedCount > 0) {
  console.log('\n🎉 Prisma imports have been fixed!');
  console.log('💡 You should now run: npm run build');
} else {
  console.log('\n🤔 No files needed fixing.');
}
