import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function resetForumAndCreateWelcomeMessages() {
  console.log('🧹 Starting forum reset...');

  try {
    // Step 1: Delete all existing forum data
    console.log('Deleting all forum posts, replies, reactions, and bookmarks...');
    
    // Delete in correct order due to foreign key constraints
    await prisma.forumReplyReaction.deleteMany({});
    await prisma.forumPostReaction.deleteMany({});
    await prisma.forumBookmark.deleteMany({});
    await prisma.forumReport.deleteMany({});
    await prisma.forumReply.deleteMany({});
    await prisma.forumPost.deleteMany({});
    
    console.log('✅ All forum posts and related data deleted');

    // Step 2: Reset category counts
    console.log('Resetting category post counts...');
    await prisma.forumCategory.updateMany({
      data: {
        postCount: 0,
        replyCount: 0,
        lastPostAt: null,
        lastPostBy: null
      }
    });

    // Step 3: Get or create admin user for welcome messages
    let adminUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    });

    if (!adminUser) {
      // Create a system admin user for welcome messages
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'FAAFO Team',
          password: 'temp-password-123', // Required field
          emailVerified: new Date(),
          profile: {
            create: {
              forumReputation: 1000,
              forumPostCount: 0
            }
          }
        }
      });
    }

    // Step 4: Add new "Platform Feedback & Criticism" category
    console.log('Creating new feedback category...');
    const feedbackCategory = await prisma.forumCategory.upsert({
      where: { slug: 'platform-feedback' },
      update: {},
      create: {
        name: 'Platform Feedback & Criticism',
        slug: 'platform-feedback',
        description: 'Share your honest feedback, criticism, and suggestions about the platform and its creator. All opinions welcome! 😅',
        guidelines: 'Feel free to share any criticism, complaints, or suggestions about the platform or me (the creator). I can take it! This is a safe space for honest feedback.',
        icon: '💬',
        color: '#8B5CF6',
        sortOrder: 10
      }
    });

    // Step 5: Get all existing categories
    const categories = await prisma.forumCategory.findMany({
      orderBy: { sortOrder: 'asc' }
    });

    // Step 6: Create welcome messages for each category
    console.log('Creating welcome messages...');

    const welcomeMessages = [
      {
        categorySlug: 'career-advice',
        title: '👋 Welcome to Career Advice!',
        content: `Welcome to our Career Advice community! 🎯

This is your space to:
• Ask for guidance on career transitions
• Share professional development tips
• Get advice on job searching strategies
• Connect with others on similar journeys

Whether you're just starting to think about a career change or you're deep in the process, this community is here to support you. Don't hesitate to share your challenges - we've all been there!

Looking forward to your questions and insights! 💪`
      },
      {
        categorySlug: 'job-search-strategies',
        title: '🔍 Welcome to Job Search Strategies!',
        content: `Welcome to Job Search Strategies! 🚀

This is your hub for:
• Networking tips and techniques
• Interview preparation strategies
• Application optimization advice
• Industry-specific job search tactics

The job search can feel overwhelming, but you're not alone. Share your wins, challenges, and discoveries here. Every small step forward is worth celebrating!

Let's help each other land those dream jobs! ✨`
      },
      {
        categorySlug: 'skill-development',
        title: '📚 Welcome to Skill Development!',
        content: `Welcome to Skill Development! 🎓

This community is for:
• Discussing learning resources and courses
• Sharing skill-building strategies
• Getting advice on which skills to prioritize
• Celebrating learning milestones

Whether you're learning to code, developing leadership skills, or mastering a new tool, this is your space to grow together. Share your learning journey and help others on theirs!

Keep growing! 🌱`
      },
      {
        categorySlug: 'industry-insights',
        title: '🏢 Welcome to Industry Insights!',
        content: `Welcome to Industry Insights! 💼

Dive into:
• Industry trends and market analysis
• Company culture discussions
• Salary and compensation insights
• Career path exploration by industry

Knowledge is power, especially when making career decisions. Share what you know about your industry and learn about others. Together we can make more informed career choices!

Let's share the inside scoop! 🔍`
      },
      {
        categorySlug: 'success-stories',
        title: '🌟 Welcome to Success Stories!',
        content: `Welcome to Success Stories! 🎉

This is our celebration space for:
• Career transition victories (big and small!)
• Breakthrough moments and lessons learned
• Inspiring journeys that motivate others
• Proof that career change is possible

Your success story - no matter how small it might seem to you - could be exactly what someone else needs to hear today. Don't be shy about sharing your wins!

We're all rooting for each other here! 🙌`
      },
      {
        categorySlug: 'platform-feedback',
        title: '💬 Welcome to Platform Feedback & Criticism!',
        content: `Welcome to the most honest corner of our community! 😅

This is where you can:
• Roast the platform (and me, the creator) 🔥
• Share what's broken, annoying, or confusing
• Suggest improvements and new features
• Vent about anything that's frustrating you

I built this platform because I've been in terrible jobs and know how much career change sucks. But I'm sure I've made mistakes and missed things. Your brutal honesty helps me make this better for everyone!

Don't hold back - I can take it! 💪 (But also, if you like something, that's nice to hear too... 🥺)`
      }
    ];

    // Create welcome posts
    for (const message of welcomeMessages) {
      const category = categories.find(c => c.slug === message.categorySlug);
      if (category) {
        await prisma.forumPost.create({
          data: {
            title: message.title,
            content: message.content,
            authorId: adminUser.id,
            categoryId: category.id,
            isPinned: true,
            createdAt: new Date()
          }
        });

        // Update category counts
        await prisma.forumCategory.update({
          where: { id: category.id },
          data: {
            postCount: 1,
            lastPostAt: new Date(),
            lastPostBy: adminUser.id
          }
        });

        console.log(`✅ Created welcome message for ${category.name}`);
      }
    }

    // Update admin user's post count
    await prisma.profile.update({
      where: { userId: adminUser.id },
      data: {
        forumPostCount: welcomeMessages.length
      }
    });

    console.log('🎉 Forum reset complete!');
    console.log(`Created ${welcomeMessages.length} welcome messages`);
    console.log('Added new "Platform Feedback & Criticism" category for honest feedback');

  } catch (error) {
    console.error('❌ Error during forum reset:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
resetForumAndCreateWelcomeMessages()
  .then(() => {
    console.log('✨ All done!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
