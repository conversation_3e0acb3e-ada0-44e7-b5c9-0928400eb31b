const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// List of broken URLs from validation
const brokenUrls = [
  'https://www.freshbooks.com/hub/accounting/budgeting-for-freelancers',
  'https://www.linkedin.com/learning/building-and-leading-teams',
  'https://www.futurelearn.com/courses/business-english-for-cross-cultural-communication',
  'https://www.edx.org/course/intercultural-communication',
  'https://www.figma.com/academy/',
  'https://cloud.google.com/training/courses/gcp-fundamentals',
  'https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0',
  'https://www.kaggle.com/learn',
  'https://www.edx.org/course/entrepreneurship-micromaster',
  'https://www.nolo.com/legal-encyclopedia/small-business-startup',
  'https://www.coursera.org/learn/personal-finance',
  'https://www.nerdwallet.com/article/finance/financial-planning-career-change',
  'https://amplitude.com/academy',
  'https://productschool.com/free-product-management-course/',
  'https://www.fidelity.com/learning-center/personal-finance/retirement/career-change-retirement',
  'https://www.linkedin.com/learning/salary-negotiation',
  'https://www.coursera.org/learn/venture-capital',
  'https://www.coursera.org/learn/technical-writing',
  'https://www.theodinproject.com/',
  'https://helpx.adobe.com/xd/tutorials.html',
  'https://example.com/test-project-1753655074370'
];

async function getBrokenUrlsDetails() {
  console.log('🔍 GATHERING BROKEN URL DETAILS');
  console.log('================================\n');

  try {
    const brokenUrlsInventory = [];

    for (const url of brokenUrls) {
      const resource = await prisma.learningResource.findFirst({
        where: { url: url },
        include: {
          careerPaths: {
            select: {
              name: true,
              slug: true
            }
          }
        }
      });

      if (resource) {
        let errorType = 'UNKNOWN';
        let errorStatus = 'UNKNOWN';

        // Determine error type based on URL patterns
        if (url.includes('example.com')) {
          errorType = 'PLACEHOLDER';
          errorStatus = 'TIMEOUT';
        } else if (url.includes('futurelearn.com') || url.includes('fidelity.com') || url.includes('theodinproject.com')) {
          errorType = 'FORBIDDEN';
          errorStatus = '403';
        } else if (url.includes('helpx.adobe.com')) {
          errorType = 'TIMEOUT';
          errorStatus = 'TIMEOUT';
        } else {
          errorType = 'NOT_FOUND';
          errorStatus = '404';
        }

        const careerPathNames = resource.careerPaths.map(cp => cp.name).join(', ') || 'None';

        brokenUrlsInventory.push({
          resourceId: resource.id,
          resourceTitle: resource.title,
          careerPaths: careerPathNames,
          url: resource.url,
          errorStatus: errorStatus,
          errorType: errorType,
          category: resource.category,
          author: resource.author
        });

        console.log(`✓ Found: ${resource.title} (${errorStatus})`);
      } else {
        console.log(`✗ Not found in database: ${url}`);
      }
    }

    // Save to JSON file
    const fs = require('fs');
    const outputPath = 'docs/cleanup/broken-urls-inventory.json';
    
    // Ensure directory exists
    const path = require('path');
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(outputPath, JSON.stringify(brokenUrlsInventory, null, 2));

    console.log(`\n📊 BROKEN URLS SUMMARY:`);
    console.log(`Total broken URLs: ${brokenUrlsInventory.length}`);
    console.log(`404 errors: ${brokenUrlsInventory.filter(r => r.errorStatus === '404').length}`);
    console.log(`403 errors: ${brokenUrlsInventory.filter(r => r.errorStatus === '403').length}`);
    console.log(`Timeouts: ${brokenUrlsInventory.filter(r => r.errorStatus === 'TIMEOUT').length}`);
    console.log(`Placeholders: ${brokenUrlsInventory.filter(r => r.errorType === 'PLACEHOLDER').length}`);

    console.log(`\n💾 Inventory saved to: ${outputPath}`);

    return brokenUrlsInventory;

  } catch (error) {
    console.error('❌ Error gathering broken URL details:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  getBrokenUrlsDetails();
}

module.exports = { getBrokenUrlsDetails };
