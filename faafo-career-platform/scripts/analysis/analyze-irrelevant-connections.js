const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function analyzeIrrelevantConnections() {
  console.log('🔍 ANALYZING IRRELEVANT RESOURCE CONNECTIONS');
  console.log('============================================\n');

  try {
    // Get all career paths with their resources
    const careerPaths = await prisma.careerPath.findMany({
      include: {
        learningResources: {
          select: {
            id: true,
            title: true,
            category: true,
            description: true,
            author: true,
            url: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    const irrelevantConnections = [];

    // Define career path keywords for relevance scoring
    const careerPathKeywords = {
      'UX/UI Designer': ['ux', 'ui', 'design', 'user', 'interface', 'experience', 'figma', 'sketch', 'adobe', 'prototype', 'wireframe', 'usability'],
      'Freelance Web Developer': ['web', 'html', 'css', 'javascript', 'react', 'node', 'frontend', 'backend', 'fullstack', 'responsive', 'api'],
      'AI/Machine Learning Engineer': ['ai', 'machine', 'learning', 'neural', 'deep', 'python', 'tensorflow', 'pytorch', 'data', 'algorithm'],
      'Data Scientist': ['data', 'science', 'analytics', 'statistics', 'python', 'r', 'sql', 'visualization', 'machine', 'learning'],
      'Cybersecurity Specialist': ['security', 'cyber', 'network', 'encryption', 'firewall', 'penetration', 'vulnerability', 'compliance'],
      'Digital Marketing Specialist': ['marketing', 'digital', 'seo', 'social', 'media', 'advertising', 'analytics', 'content', 'campaign'],
      'Simple Online Business Owner': ['business', 'entrepreneur', 'startup', 'finance', 'marketing', 'sales', 'management', 'strategy'],
      'Cloud Solutions Architect': ['cloud', 'aws', 'azure', 'gcp', 'architecture', 'infrastructure', 'devops', 'scalability'],
      'DevOps Engineer': ['devops', 'docker', 'kubernetes', 'ci', 'cd', 'pipeline', 'infrastructure', 'automation', 'monitoring'],
      'Product Manager': ['product', 'management', 'strategy', 'roadmap', 'agile', 'scrum', 'user', 'research', 'analytics']
    };

    for (const careerPath of careerPaths) {
      const pathKeywords = careerPathKeywords[careerPath.name] || [];
      
      console.log(`\n📂 Analyzing: ${careerPath.name}`);
      console.log(`   Resources: ${careerPath.learningResources.length}`);

      for (const resource of careerPath.learningResources) {
        const relevanceScore = calculateRelevanceScore(resource, pathKeywords);
        
        console.log(`   📚 ${resource.title}: ${relevanceScore.score}% relevance (${relevanceScore.matches} matches)`);

        // Consider irrelevant if relevance score < 20% or < 2 keyword matches
        if (relevanceScore.score < 20 || relevanceScore.matches < 2) {
          irrelevantConnections.push({
            resourceId: resource.id,
            resourceTitle: resource.title,
            careerPath: careerPath.name,
            careerPathId: careerPath.id,
            category: resource.category,
            author: resource.author,
            url: resource.url,
            relevanceScore: relevanceScore.score,
            keywordMatches: relevanceScore.matches,
            matchedKeywords: relevanceScore.matchedKeywords,
            reason: getIrrelevanceReason(resource, careerPath.name),
            recommendation: getRecommendation(resource, careerPath.name)
          });
        }
      }
    }

    // Save to JSON file
    const fs = require('fs');
    const outputPath = 'docs/cleanup/irrelevant-connections.json';
    
    // Ensure directory exists
    const path = require('path');
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(outputPath, JSON.stringify(irrelevantConnections, null, 2));

    console.log(`\n📊 IRRELEVANT CONNECTIONS SUMMARY:`);
    console.log(`Total irrelevant connections: ${irrelevantConnections.length}`);
    
    // Group by career path
    const byCareerPath = {};
    irrelevantConnections.forEach(conn => {
      byCareerPath[conn.careerPath] = (byCareerPath[conn.careerPath] || 0) + 1;
    });

    console.log('\nBy Career Path:');
    Object.entries(byCareerPath).forEach(([path, count]) => {
      console.log(`  ${path}: ${count} irrelevant resources`);
    });

    console.log(`\n💾 Irrelevant connections saved to: ${outputPath}`);

    return irrelevantConnections;

  } catch (error) {
    console.error('❌ Error analyzing irrelevant connections:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function calculateRelevanceScore(resource, pathKeywords) {
  const text = `${resource.title} ${resource.description || ''} ${resource.category}`.toLowerCase();
  
  let matches = 0;
  const matchedKeywords = [];

  pathKeywords.forEach(keyword => {
    if (text.includes(keyword.toLowerCase())) {
      matches++;
      matchedKeywords.push(keyword);
    }
  });

  const score = pathKeywords.length > 0 ? Math.round((matches / pathKeywords.length) * 100) : 0;
  
  return {
    score,
    matches,
    matchedKeywords
  };
}

function getIrrelevanceReason(resource, careerPath) {
  const reasons = {
    'freeCodeCamp Full Stack Development': 'Web development course not relevant to UX/UI design',
    'React Official Tutorial': 'Technical programming tutorial not core to UX/UI design',
    'The Odin Project': 'Full-stack web development not relevant to UX/UI design',
    'MDN Web Docs': 'Technical web documentation not relevant to UX/UI design',
    'Node.js Developer Roadmap': 'Backend development not relevant to UX/UI design',
    'Blockchain Basics': 'Blockchain technology not core to web development or business',
    'Blockchain Fundamentals': 'Blockchain technology not core to web development or business',
    'Business English for International Careers': 'Generic language course not specific to technical roles',
    'Technical Communication Skills': 'Generic communication course not specific to technical roles',
    'Cross-Cultural Communication': 'Generic communication course not specific to technical roles',
    'Project Management Foundations': 'Generic project management not core to UX/UI design',
    'Introduction to Project Management': 'Generic project management not core to web development'
  };

  return reasons[resource.title] || 'Low keyword relevance to career path requirements';
}

function getRecommendation(resource, careerPath) {
  if (careerPath === 'UX/UI Designer') {
    if (resource.title.includes('freeCodeCamp') || resource.title.includes('React') || resource.title.includes('Odin')) {
      return 'REMOVE - Replace with UX/UI specific resources';
    }
  }
  
  if (resource.title.includes('Blockchain') && !careerPath.includes('Blockchain')) {
    return 'REMOVE - Not relevant to career path';
  }

  if (resource.title.includes('Project Management') && careerPath !== 'Product Manager') {
    return 'REVIEW - Consider if project management is core requirement';
  }

  return 'REVIEW - Assess relevance and consider removal';
}

if (require.main === module) {
  analyzeIrrelevantConnections();
}

module.exports = { analyzeIrrelevantConnections };
