const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function identifyPlaceholderData() {
  console.log('🔍 IDENTIFYING PLACEHOLDER/TEST DATA');
  console.log('====================================\n');

  try {
    // Get all resources
    const resources = await prisma.learningResource.findMany({
      include: {
        careerPaths: {
          select: {
            name: true,
            slug: true
          }
        }
      },
      orderBy: {
        title: 'asc'
      }
    });

    const placeholderData = [];

    console.log(`📊 Analyzing ${resources.length} resources for placeholder data...\n`);

    for (const resource of resources) {
      const placeholderIndicators = [];
      let isPlaceholder = false;

      // Check for placeholder patterns
      
      // 1. URLs with example.com or test domains
      if (resource.url && (
        resource.url.includes('example.com') ||
        resource.url.includes('test.com') ||
        resource.url.includes('placeholder.com') ||
        resource.url.includes('localhost') ||
        resource.url.includes('127.0.0.1')
      )) {
        placeholderIndicators.push('Placeholder URL domain');
        isPlaceholder = true;
      }

      // 2. URLs with test/placeholder patterns
      if (resource.url && (
        resource.url.includes('/test-') ||
        resource.url.includes('/placeholder-') ||
        resource.url.includes('-test-') ||
        resource.url.includes('-placeholder-') ||
        resource.url.includes('test-project') ||
        resource.url.includes('sample-')
      )) {
        placeholderIndicators.push('Test/placeholder URL pattern');
        isPlaceholder = true;
      }

      // 3. Title contains test/placeholder keywords
      if (resource.title && (
        resource.title.toLowerCase().includes('test') ||
        resource.title.toLowerCase().includes('placeholder') ||
        resource.title.toLowerCase().includes('todo') ||
        resource.title.toLowerCase().includes('sample') ||
        resource.title.toLowerCase().includes('example')
      )) {
        placeholderIndicators.push('Test/placeholder in title');
        isPlaceholder = true;
      }

      // 4. Description contains test/placeholder keywords
      if (resource.description && (
        resource.description.toLowerCase().includes('test') ||
        resource.description.toLowerCase().includes('placeholder') ||
        resource.description.toLowerCase().includes('todo') ||
        resource.description.toLowerCase().includes('sample') ||
        resource.description.toLowerCase().includes('example') ||
        resource.description.toLowerCase().includes('lorem ipsum')
      )) {
        placeholderIndicators.push('Test/placeholder in description');
        isPlaceholder = true;
      }

      // 5. Author is null, 'N/A', 'Test', or similar
      if (!resource.author || 
          resource.author === 'N/A' ||
          resource.author === 'null' ||
          resource.author.toLowerCase().includes('test') ||
          resource.author.toLowerCase().includes('placeholder') ||
          resource.author.toLowerCase().includes('example')
      ) {
        placeholderIndicators.push('Missing or placeholder author');
        isPlaceholder = true;
      }

      // 6. Suspicious patterns in URLs (random numbers, timestamps)
      if (resource.url && (
        /\d{10,}/.test(resource.url) || // Long number sequences (timestamps)
        /test-\d+/.test(resource.url) || // test-123456 patterns
        /project-\d+/.test(resource.url) // project-123456 patterns
      )) {
        placeholderIndicators.push('Suspicious URL pattern with numbers');
        isPlaceholder = true;
      }

      if (isPlaceholder) {
        const careerPathNames = resource.careerPaths.map(cp => cp.name).join(', ') || 'None';

        placeholderData.push({
          resourceId: resource.id,
          resourceTitle: resource.title,
          careerPaths: careerPathNames,
          url: resource.url,
          author: resource.author,
          category: resource.category,
          description: resource.description,
          placeholderIndicators: placeholderIndicators,
          priority: getPriority(placeholderIndicators),
          recommendation: getRecommendation(placeholderIndicators)
        });

        console.log(`🚨 PLACEHOLDER FOUND: ${resource.title}`);
        console.log(`   Indicators: ${placeholderIndicators.join(', ')}`);
        console.log(`   URL: ${resource.url}`);
        console.log(`   Author: ${resource.author || 'NULL'}`);
        console.log('');
      }
    }

    // Save to JSON file
    const fs = require('fs');
    const outputPath = 'docs/cleanup/placeholder-data.json';
    
    // Ensure directory exists
    const path = require('path');
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(outputPath, JSON.stringify(placeholderData, null, 2));

    console.log(`📊 PLACEHOLDER DATA SUMMARY:`);
    console.log(`Total placeholder resources: ${placeholderData.length}`);
    console.log(`High priority (immediate removal): ${placeholderData.filter(p => p.priority === 'HIGH').length}`);
    console.log(`Medium priority (review needed): ${placeholderData.filter(p => p.priority === 'MEDIUM').length}`);
    console.log(`Low priority (minor issues): ${placeholderData.filter(p => p.priority === 'LOW').length}`);

    // Group by indicator type
    const indicatorCounts = {};
    placeholderData.forEach(item => {
      item.placeholderIndicators.forEach(indicator => {
        indicatorCounts[indicator] = (indicatorCounts[indicator] || 0) + 1;
      });
    });

    console.log('\nBy Indicator Type:');
    Object.entries(indicatorCounts).forEach(([indicator, count]) => {
      console.log(`  ${indicator}: ${count} resources`);
    });

    console.log(`\n💾 Placeholder data saved to: ${outputPath}`);

    return placeholderData;

  } catch (error) {
    console.error('❌ Error identifying placeholder data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function getPriority(indicators) {
  // High priority: URLs with example.com, test domains, or obvious test patterns
  if (indicators.some(i => 
    i.includes('Placeholder URL domain') || 
    i.includes('Test/placeholder URL pattern') ||
    i.includes('Test/placeholder in title')
  )) {
    return 'HIGH';
  }

  // Medium priority: Missing authors or suspicious patterns
  if (indicators.some(i => 
    i.includes('Missing or placeholder author') ||
    i.includes('Suspicious URL pattern')
  )) {
    return 'MEDIUM';
  }

  // Low priority: Only description issues
  return 'LOW';
}

function getRecommendation(indicators) {
  if (indicators.some(i => i.includes('Placeholder URL domain'))) {
    return 'IMMEDIATE REMOVAL - Placeholder URL cannot be accessed';
  }

  if (indicators.some(i => i.includes('Test/placeholder in title'))) {
    return 'IMMEDIATE REMOVAL - Obviously test data';
  }

  if (indicators.some(i => i.includes('Test/placeholder URL pattern'))) {
    return 'IMMEDIATE REMOVAL - Test URL pattern';
  }

  if (indicators.some(i => i.includes('Missing or placeholder author'))) {
    return 'REVIEW - Fix author information or remove if invalid';
  }

  return 'REVIEW - Assess if legitimate resource with minor issues';
}

if (require.main === module) {
  identifyPlaceholderData();
}

module.exports = { identifyPlaceholderData };
