const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function getEmptyCareerPaths() {
  console.log('📋 DOCUMENTING EMPTY CAREER PATHS');
  console.log('=================================\n');

  try {
    // Get all career paths with their resource counts
    const careerPaths = await prisma.careerPath.findMany({
      include: {
        learningResources: {
          select: {
            id: true,
            title: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Find empty career paths (no resources)
    const emptyCareerPaths = careerPaths.filter(path => path.learningResources.length === 0);

    console.log(`Found ${emptyCareerPaths.length} empty career paths:\n`);

    const emptyCareerPathsData = [];

    for (const path of emptyCareerPaths) {
      console.log(`📂 ${path.name}`);
      console.log(`   ID: ${path.id}`);
      console.log(`   Slug: ${path.slug}`);
      console.log(`   Description: ${path.description || 'No description'}`);
      console.log(`   Resources: ${path.learningResources.length}`);
      console.log('');

      emptyCareerPathsData.push({
        id: path.id,
        name: path.name,
        slug: path.slug,
        description: path.description,
        resourceCount: path.learningResources.length,
        targetSkillRequirements: getTargetSkillRequirements(path.name),
        priority: getPriority(path.name),
        suggestedResourceTypes: getSuggestedResourceTypes(path.name)
      });
    }

    // Save to JSON file
    const fs = require('fs');
    const outputPath = 'docs/cleanup/empty-career-paths.json';
    
    // Ensure directory exists
    const path = require('path');
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(outputPath, JSON.stringify(emptyCareerPathsData, null, 2));

    console.log(`📊 SUMMARY:`);
    console.log(`Empty career paths: ${emptyCareerPathsData.length}`);
    console.log(`High priority: ${emptyCareerPathsData.filter(p => p.priority === 'HIGH').length}`);
    console.log(`Medium priority: ${emptyCareerPathsData.filter(p => p.priority === 'MEDIUM').length}`);

    console.log(`\n💾 Empty career paths data saved to: ${outputPath}`);

    return emptyCareerPathsData;

  } catch (error) {
    console.error('❌ Error documenting empty career paths:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function getTargetSkillRequirements(careerPathName) {
  const skillRequirements = {
    'Cloud Solutions Architect': [
      'AWS fundamentals and services',
      'Azure cloud platform basics',
      'Google Cloud Platform (GCP) essentials',
      'Cloud architecture patterns and best practices',
      'Cloud security and compliance',
      'Cost optimization strategies',
      'Infrastructure as Code (IaC)',
      'Networking and connectivity in cloud',
      'Disaster recovery and backup strategies',
      'Multi-cloud and hybrid cloud concepts'
    ],
    'DevOps Engineer': [
      'Docker containerization',
      'Kubernetes orchestration',
      'CI/CD pipeline design and implementation',
      'Infrastructure as Code (Terraform, CloudFormation)',
      'Monitoring and logging (Prometheus, Grafana, ELK)',
      'Git workflows and version control',
      'Linux/Unix system administration',
      'Scripting (Bash, Python)',
      'Cloud platforms (AWS, Azure, GCP)',
      'Security best practices in DevOps'
    ],
    'Product Manager': [
      'Product strategy and roadmap planning',
      'User research and customer discovery',
      'Data analysis and metrics interpretation',
      'Agile and Scrum methodologies',
      'Stakeholder management and communication',
      'Market research and competitive analysis',
      'Product lifecycle management',
      'User experience (UX) principles',
      'Technical understanding for product decisions',
      'Business case development and ROI analysis'
    ]
  };

  return skillRequirements[careerPathName] || [];
}

function getPriority(careerPathName) {
  const priorities = {
    'Cloud Solutions Architect': 'HIGH',
    'DevOps Engineer': 'HIGH', 
    'Product Manager': 'HIGH'
  };

  return priorities[careerPathName] || 'MEDIUM';
}

function getSuggestedResourceTypes(careerPathName) {
  const resourceTypes = {
    'Cloud Solutions Architect': [
      'Official cloud provider documentation',
      'Free certification prep courses',
      'Hands-on labs and tutorials',
      'Architecture case studies',
      'Best practices guides'
    ],
    'DevOps Engineer': [
      'Official tool documentation',
      'Hands-on tutorials and labs',
      'Open source project contributions',
      'Best practices and patterns',
      'Community-driven learning resources'
    ],
    'Product Manager': [
      'Product management frameworks',
      'Case studies from successful products',
      'User research methodologies',
      'Analytics and metrics guides',
      'Industry best practices'
    ]
  };

  return resourceTypes[careerPathName] || [];
}

if (require.main === module) {
  getEmptyCareerPaths();
}

module.exports = { getEmptyCareerPaths };
