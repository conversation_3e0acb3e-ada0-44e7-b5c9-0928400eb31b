#!/usr/bin/env node

/**
 * Create test data to demonstrate Skill Gap Analyzer bugs
 * This script creates skill gap analysis data that will show the bugs in action
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createBugDemoData() {
  console.log('🔬 Creating test data to demonstrate Skill Gap Analyzer bugs...');
  
  try {
    // Find the premium test user
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!user) {
      console.log('❌ Premium test user not found');
      return;
    }
    
    console.log(`✅ Using user: ${user.email} (ID: ${user.id})`);
    
    // Clear existing data
    await prisma.skillGapAnalysis.deleteMany({
      where: { userId: user.id }
    });
    console.log('🧹 Cleared existing gap analyses');
    
    // Create skill gap analysis with data that will demonstrate the bugs
    const gapAnalysis = await prisma.skillGapAnalysis.create({
      data: {
        userId: user.id,
        targetCareerPathName: 'Full Stack Developer',
        experienceLevel: 'INTERMEDIATE',
        timeframe: 'SIX_MONTHS',
        status: 'COMPLETED',
        completionPercentage: 100,
        analysisData: {
          analysisDate: new Date().toISOString(),
          methodology: 'AI_COMPREHENSIVE',
          confidence: 0.85,
          totalSkillsAnalyzed: 4,
          averageCurrentLevel: 3.5,
          targetAverageLevel: 8.0
        },
        skillGaps: [
          {
            skillId: 'javascript-gap',
            skillName: 'JavaScript',
            currentLevel: 3,
            targetLevel: 9,
            gapSize: 6,
            gapSeverity: 'CRITICAL',
            priority: 1,
            marketDemand: 'VERY_HIGH',
            salaryImpact: 20,
            learningResources: [
              'JavaScript: The Definitive Guide',
              'You Don\'t Know JS Series',
              'Modern JavaScript Tutorial'
            ],
            estimatedLearningTime: 45  // This will trigger time calculation bug
          },
          {
            skillId: 'react-gap',
            skillName: 'React',
            currentLevel: 2,
            targetLevel: 8,
            gapSize: 6,
            gapSeverity: 'HIGH',
            priority: 2,
            marketDemand: 'HIGH',
            salaryImpact: 18,
            learningResources: [
              'React Official Documentation',
              'React Hooks in Action',
              'Full Stack React'
            ],
            estimatedLearningTime: 80  // This will trigger time calculation bug
          },
          {
            skillId: 'nodejs-gap',
            skillName: 'Node.js',
            currentLevel: 4,
            targetLevel: 7,
            gapSize: 3,
            gapSeverity: 'MEDIUM',
            priority: 3,
            marketDemand: 'HIGH',
            salaryImpact: 15,
            learningResources: [
              'Node.js Design Patterns',
              'Express.js Guide',
              'Building APIs with Node.js'
            ],
            estimatedLearningTime: 25  // This will trigger time calculation bug
          },
          {
            skillId: 'typescript-gap',
            skillName: 'TypeScript',
            currentLevel: 1,
            targetLevel: 6,
            gapSize: 5,
            gapSeverity: 'HIGH',
            priority: 4,
            marketDemand: 'MODERATE',
            salaryImpact: 12,
            learningResources: [
              'TypeScript Handbook',
              'Programming TypeScript',
              'TypeScript Deep Dive'
            ],
            estimatedLearningTime: 60  // This will trigger time calculation bug
          }
        ],
        learningPlan: {
          totalEstimatedHours: 210,
          milestones: [
            {
              month: 1,
              skills: ['JavaScript Fundamentals'],
              estimatedHours: 45,
              dueDate: new Date('2024-01-31').toISOString()  // This will trigger date bug
            },
            {
              month: 2,
              skills: ['React Basics'],
              estimatedHours: 40,
              dueDate: new Date('2024-03-31').toISOString()  // This will trigger date bug
            },
            {
              month: 3,
              skills: ['Node.js & TypeScript'],
              estimatedHours: 85,
              dueDate: new Date('2024-05-31').toISOString()  // This will trigger date bug
            },
            {
              month: 4,
              skills: ['Advanced React & Full Stack'],
              estimatedHours: 40,
              dueDate: new Date('2024-07-31').toISOString()  // This will trigger date bug
            }
          ]
        },
        marketData: {
          careerReadiness: {
            currentScore: 45,
            targetScore: 85,
            improvementPotential: 40,
            timeToTarget: 6
          }
        },
        expiresAt: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000), // 6 months
        lastUpdated: new Date()
      }
    });
    
    console.log('✅ Created skill gap analysis with bug-triggering data');
    console.log(`   Analysis ID: ${gapAnalysis.id}`);
    console.log(`   Skills with time calculation bugs: 4`);
    console.log(`   Milestones with date bugs: 4`);
    console.log(`   Priority calculation issues: Multiple skills`);
    
    console.log('\n🎯 BUG DEMONSTRATION DATA READY:');
    console.log('   1. Time Calculation Bug (L001): 45h, 80h, 25h, 60h will show inconsistent formatting');
    console.log('   2. Priority Generation Bug (L002): Priority scores may not make logical sense');
    console.log('   3. Date Handling Bug (L003): Milestone dates from 31st of month will be wrong');
    
    console.log('\n📋 NEXT STEPS:');
    console.log('   1. Navigate to Skill Gap Analyzer in browser');
    console.log('   2. Go to "View Results" tab');
    console.log('   3. Observe the bugs in the displayed data');
    
  } catch (error) {
    console.error('❌ Error creating test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  createBugDemoData().catch(console.error);
}

module.exports = { createBugDemoData };
