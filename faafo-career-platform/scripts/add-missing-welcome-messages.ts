import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function addMissingWelcomeMessages() {
  console.log('🔍 Checking for missing welcome messages...');

  try {
    // Get admin user
    const adminUser = await prisma.user.findFirst({
      where: { 
        email: '<EMAIL>'
      }
    });

    if (!adminUser) {
      console.log('❌ Admin user not found');
      return;
    }

    // Get all categories
    const categories = await prisma.forumCategory.findMany({
      orderBy: { sortOrder: 'asc' }
    });

    console.log('📋 Found categories:');
    categories.forEach(cat => {
      console.log(`  - ${cat.name} (${cat.slug}) - ${cat.postCount} posts`);
    });

    // Additional welcome messages for categories that don't have them yet
    const additionalWelcomeMessages = [
      {
        categorySlug: 'job-search',
        title: '🔍 Welcome to Job Search!',
        content: `Welcome to Job Search! 🚀

This is your hub for:
• Job hunting strategies and experiences
• Application tips and tricks
• Interview preparation and practice
• Networking opportunities and advice

The job search can feel overwhelming, but you're not alone. Share your wins, challenges, and discoveries here. Every application is a step forward!

Let's help each other land those dream jobs! ✨`
      },
      {
        categorySlug: 'networking',
        title: '🤝 Welcome to Networking!',
        content: `Welcome to Networking! 🌐

This community is for:
• Building professional connections
• Sharing networking strategies
• Finding mentorship opportunities
• Industry event discussions

Networking doesn't have to be scary or fake. It's about building genuine relationships that help everyone grow. Share your networking wins and challenges here!

Let's connect and grow together! 🤝`
      },
      {
        categorySlug: 'career-change',
        title: '🔄 Welcome to Career Change!',
        content: `Welcome to Career Change! 🔄

This is your space for:
• Transitioning between careers
• Overcoming career change fears
• Sharing transition stories
• Getting support during the journey

Career change is one of the scariest but most rewarding things you can do. Whether you're just thinking about it or already in the thick of it, this community is here to support you!

You've got this! 💪`
      },
      {
        categorySlug: 'general-discussion',
        title: '💬 Welcome to General Discussion!',
        content: `Welcome to General Discussion! 💬

This is our catch-all space for:
• General career and learning discussions
• Random thoughts and observations
• Off-topic conversations (career-related)
• Community building and getting to know each other

Sometimes the best conversations happen when we're not trying too hard. Share whatever's on your mind related to careers, work, or life!

Let's chat! 🗣️`
      },
      {
        categorySlug: 'freelancing',
        title: '💼 Welcome to Freelancing!',
        content: `Welcome to Freelancing! 💼

This community is for:
• Independent work and consulting
• Client management strategies
• Pricing and business advice
• Freelance lifestyle discussions

Whether you're thinking about going freelance, just starting out, or a seasoned pro, this is your space to share experiences and learn from each other!

Freedom and flexibility await! 🌟`
      },
      {
        categorySlug: 'technology',
        title: '💻 Welcome to Technology!',
        content: `Welcome to Technology! 💻

This is your space for:
• Technology-related discussions and questions
• Programming and development topics
• Tech career advice
• Industry trends and innovations

Whether you're a seasoned developer, transitioning into tech, or just curious about the industry, this community is here to help you navigate the tech world!

Let's code our way to success! 🚀`
      }
    ];

    // Create welcome posts for categories that don't have pinned posts yet
    let createdCount = 0;
    for (const message of additionalWelcomeMessages) {
      const category = categories.find(c => c.slug === message.categorySlug);
      if (category && category.postCount === 0) {
        await prisma.forumPost.create({
          data: {
            title: message.title,
            content: message.content,
            authorId: adminUser.id,
            categoryId: category.id,
            isPinned: true,
            createdAt: new Date()
          }
        });

        // Update category counts
        await prisma.forumCategory.update({
          where: { id: category.id },
          data: {
            postCount: 1,
            lastPostAt: new Date(),
            lastPostBy: adminUser.id
          }
        });

        console.log(`✅ Created welcome message for ${category.name}`);
        createdCount++;
      } else if (category) {
        console.log(`⏭️  Skipping ${category.name} - already has posts`);
      } else {
        console.log(`❓ Category not found: ${message.categorySlug}`);
      }
    }

    // Update admin user's post count
    if (createdCount > 0) {
      await prisma.profile.update({
        where: { userId: adminUser.id },
        data: {
          forumPostCount: { increment: createdCount }
        }
      });
    }

    console.log(`🎉 Created ${createdCount} additional welcome messages`);

  } catch (error) {
    console.error('❌ Error adding welcome messages:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addMissingWelcomeMessages()
  .then(() => {
    console.log('✨ All done!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
