const fs = require('fs');

async function consolidateDeletionLogs() {
  console.log('📋 CONSOLIDATING ALL DELETION LOGS');
  console.log('==================================\n');

  try {
    const consolidatedLog = {
      summary: {
        totalResourcesDeleted: 0,
        totalResourcesMarkedInactive: 0,
        deletionsByType: {},
        deletionsByReason: {},
        deletionsByCareerPath: {},
        generatedAt: new Date().toISOString()
      },
      deletions: []
    };

    // Load all deletion logs
    const logFiles = [
      'docs/cleanup/placeholder-deletion-log.json',
      'docs/cleanup/404-deletion-log.json',
      'docs/cleanup/403-deletion-log.json',
      'docs/cleanup/timeout-deletion-log.json'
    ];

    for (const logFile of logFiles) {
      if (fs.existsSync(logFile)) {
        console.log(`📄 Processing: ${logFile}`);
        
        const logData = JSON.parse(fs.readFileSync(logFile, 'utf8'));
        
        for (const entry of logData) {
          // Standardize the entry format
          const standardizedEntry = {
            resourceId: entry.resourceId,
            resourceTitle: entry.resourceTitle,
            careerPaths: entry.careerPaths || 'None',
            url: entry.url,
            author: entry.author,
            category: entry.category,
            errorStatus: entry.errorStatus || entry.reason || 'Unknown',
            reason: entry.reason || 'Unknown',
            deletedAt: entry.deletedAt,
            status: entry.status,
            source: logFile.split('/').pop().replace('.json', ''),
            userProgressCount: entry.userProgressCount || 0,
            error: entry.error || null
          };

          consolidatedLog.deletions.push(standardizedEntry);

          // Update summary statistics
          if (entry.status === 'SUCCESS' || entry.status === 'DELETED') {
            consolidatedLog.summary.totalResourcesDeleted++;
          } else if (entry.status === 'MARKED_INACTIVE' || entry.status === 'MARKED_INACTIVE_REVIEW') {
            consolidatedLog.summary.totalResourcesMarkedInactive++;
          }

          // Count by type
          const type = entry.category || 'Unknown';
          consolidatedLog.summary.deletionsByType[type] = (consolidatedLog.summary.deletionsByType[type] || 0) + 1;

          // Count by reason
          const reason = entry.reason || 'Unknown';
          consolidatedLog.summary.deletionsByReason[reason] = (consolidatedLog.summary.deletionsByReason[reason] || 0) + 1;

          // Count by career path
          const careerPaths = entry.careerPaths || 'None';
          if (careerPaths !== 'None') {
            careerPaths.split(', ').forEach(path => {
              consolidatedLog.summary.deletionsByCareerPath[path] = (consolidatedLog.summary.deletionsByCareerPath[path] || 0) + 1;
            });
          } else {
            consolidatedLog.summary.deletionsByCareerPath['None'] = (consolidatedLog.summary.deletionsByCareerPath['None'] || 0) + 1;
          }
        }

        console.log(`   ✅ Processed ${logData.length} entries`);
      } else {
        console.log(`   ⚠️ Log file not found: ${logFile}`);
      }
    }

    // Save consolidated log
    const outputPath = 'docs/cleanup/consolidated-deletion-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(consolidatedLog, null, 2));

    // Display summary
    console.log('\n📊 CONSOLIDATED DELETION SUMMARY:');
    console.log('=================================');
    console.log(`Total resources processed: ${consolidatedLog.deletions.length}`);
    console.log(`Successfully deleted: ${consolidatedLog.summary.totalResourcesDeleted}`);
    console.log(`Marked inactive: ${consolidatedLog.summary.totalResourcesMarkedInactive}`);

    console.log('\n📂 Deletions by Category:');
    Object.entries(consolidatedLog.summary.deletionsByType)
      .sort(([,a], [,b]) => b - a)
      .forEach(([type, count]) => {
        console.log(`   ${type}: ${count} resources`);
      });

    console.log('\n🔍 Deletions by Reason:');
    Object.entries(consolidatedLog.summary.deletionsByReason)
      .sort(([,a], [,b]) => b - a)
      .forEach(([reason, count]) => {
        console.log(`   ${reason}: ${count} resources`);
      });

    console.log('\n🛤️ Deletions by Career Path:');
    Object.entries(consolidatedLog.summary.deletionsByCareerPath)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10) // Show top 10
      .forEach(([path, count]) => {
        console.log(`   ${path}: ${count} resources`);
      });

    // Create a summary report
    const summaryReport = `# Resource Deletion Summary Report

Generated: ${new Date().toISOString()}

## Overview
- **Total Resources Processed**: ${consolidatedLog.deletions.length}
- **Successfully Deleted**: ${consolidatedLog.summary.totalResourcesDeleted}
- **Marked Inactive**: ${consolidatedLog.summary.totalResourcesMarkedInactive}

## Deletions by Category
${Object.entries(consolidatedLog.summary.deletionsByType)
  .sort(([,a], [,b]) => b - a)
  .map(([type, count]) => `- ${type}: ${count} resources`)
  .join('\n')}

## Deletions by Reason
${Object.entries(consolidatedLog.summary.deletionsByReason)
  .sort(([,a], [,b]) => b - a)
  .map(([reason, count]) => `- ${reason}: ${count} resources`)
  .join('\n')}

## Most Affected Career Paths
${Object.entries(consolidatedLog.summary.deletionsByCareerPath)
  .sort(([,a], [,b]) => b - a)
  .slice(0, 10)
  .map(([path, count]) => `- ${path}: ${count} resources`)
  .join('\n')}

## Impact Assessment
The cleanup process has successfully removed broken, placeholder, and irrelevant resources from the database. This will improve the overall quality and reliability of the learning resource recommendations.

### Next Steps
1. Add new high-quality resources for empty career paths
2. Fix irrelevant resource connections
3. Replace broken resources with working alternatives
4. Validate all remaining URLs
`;

    fs.writeFileSync('docs/cleanup/deletion-summary-report.md', summaryReport);

    console.log(`\n💾 Consolidated log saved to: ${outputPath}`);
    console.log(`📄 Summary report saved to: docs/cleanup/deletion-summary-report.md`);

    return consolidatedLog;

  } catch (error) {
    console.error('❌ Error consolidating deletion logs:', error);
  }
}

if (require.main === module) {
  consolidateDeletionLogs();
}

module.exports = { consolidateDeletionLogs };
