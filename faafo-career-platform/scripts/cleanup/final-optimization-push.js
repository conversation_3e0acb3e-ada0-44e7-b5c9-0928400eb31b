const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function finalOptimizationPush() {
  console.log('🚀 FINAL OPTIMIZATION PUSH - ACHIEVING MAXIMUM QUALITY');
  console.log('======================================================\n');

  try {
    // Connect remaining high-value unconnected resources to appropriate career paths
    const optimizationActions = [
      // Connect DevOps resources to DevOps Engineer path
      {
        resourceTitle: 'Docker Getting Started',
        targetCareerPath: 'DevOps Engineer',
        reason: 'Essential Docker fundamentals for DevOps engineers'
      },
      {
        resourceTitle: 'AWS Cloud Practitioner Essentials',
        targetCareerPath: 'Cloud Solutions Architect',
        reason: 'Foundational AWS knowledge for cloud architects'
      },
      {
        resourceTitle: 'Microsoft Azure Fundamentals',
        targetCareerPath: 'Cloud Solutions Architect',
        reason: 'Azure basics essential for multi-cloud architects'
      },
      {
        resourceTitle: 'Kubernetes Basics',
        targetCareerPath: 'DevOps Engineer',
        reason: 'Kubernetes fundamentals for container orchestration'
      },
      {
        resourceTitle: 'AWS Solutions Architect Associate',
        targetCareerPath: 'Cloud Solutions Architect',
        reason: 'Advanced AWS certification for cloud architects'
      },
      {
        resourceTitle: 'AWS DevOps Engineer Professional',
        targetCareerPath: 'DevOps Engineer',
        reason: 'Professional-level AWS DevOps certification'
      },
      // Connect Product Management resources
      {
        resourceTitle: 'Agile Development Specialization',
        targetCareerPath: 'Product Manager',
        reason: 'Agile methodologies essential for product managers'
      },
      // Connect mobile development to Freelance Web Developer (React Native focus)
      {
        resourceTitle: 'React Native Complete Guide',
        targetCareerPath: 'Freelance Web Developer',
        reason: 'React Native allows web developers to expand to mobile'
      },
      // Connect entrepreneurship resource to business path
      {
        resourceTitle: 'Overcoming Six Fears of Midlife Career Change',
        targetCareerPath: 'Simple Online Business Owner',
        reason: 'Career transition guidance for entrepreneurs'
      }
    ];

    const optimizationLog = [];

    for (const action of optimizationActions) {
      console.log(`🔗 Connecting: ${action.resourceTitle}`);
      console.log(`   To: ${action.targetCareerPath}`);
      console.log(`   Reason: ${action.reason}`);

      try {
        // Find the resource
        const resource = await prisma.learningResource.findFirst({
          where: { title: action.resourceTitle }
        });

        if (!resource) {
          console.log(`   ❌ Resource not found: ${action.resourceTitle}`);
          optimizationLog.push({
            resourceTitle: action.resourceTitle,
            targetCareerPath: action.targetCareerPath,
            reason: action.reason,
            status: 'NOT_FOUND',
            timestamp: new Date().toISOString()
          });
          continue;
        }

        // Find the career path
        const careerPath = await prisma.careerPath.findFirst({
          where: { name: action.targetCareerPath }
        });

        if (!careerPath) {
          console.log(`   ❌ Career path not found: ${action.targetCareerPath}`);
          optimizationLog.push({
            resourceTitle: action.resourceTitle,
            targetCareerPath: action.targetCareerPath,
            reason: action.reason,
            status: 'CAREER_PATH_NOT_FOUND',
            timestamp: new Date().toISOString()
          });
          continue;
        }

        // Check if already connected
        const existingConnection = await prisma.careerPath.findFirst({
          where: {
            id: careerPath.id,
            learningResources: {
              some: { id: resource.id }
            }
          }
        });

        if (existingConnection) {
          console.log(`   ⚠️ Already connected`);
          optimizationLog.push({
            resourceTitle: action.resourceTitle,
            targetCareerPath: action.targetCareerPath,
            reason: action.reason,
            status: 'ALREADY_CONNECTED',
            timestamp: new Date().toISOString()
          });
          continue;
        }

        // Connect the resource to the career path
        await prisma.careerPath.update({
          where: { id: careerPath.id },
          data: {
            learningResources: {
              connect: { id: resource.id }
            }
          }
        });

        console.log(`   ✅ Successfully connected`);
        optimizationLog.push({
          resourceTitle: action.resourceTitle,
          targetCareerPath: action.targetCareerPath,
          reason: action.reason,
          status: 'SUCCESS',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.log(`   ❌ Failed to connect: ${error.message}`);
        optimizationLog.push({
          resourceTitle: action.resourceTitle,
          targetCareerPath: action.targetCareerPath,
          reason: action.reason,
          status: 'FAILED',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }

      console.log('');
    }

    // Add a few more high-quality resources to paths that need them
    const additionalResources = [
      // Digital Marketing Specialist needs more resources
      {
        title: 'Google Analytics Academy',
        description: 'Free courses on Google Analytics, Google Ads, and digital marketing measurement',
        url: 'https://analytics.google.com/analytics/academy/',
        author: 'Google',
        category: 'DIGITAL_MARKETING',
        type: 'COURSE',
        skillLevel: 'BEGINNER',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true,
        targetCareerPath: 'Digital Marketing Specialist'
      },
      {
        title: 'Facebook Blueprint',
        description: 'Free online training for advertising on Facebook and Instagram',
        url: 'https://www.facebook.com/business/learn',
        author: 'Meta',
        category: 'DIGITAL_MARKETING',
        type: 'COURSE',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true,
        targetCareerPath: 'Digital Marketing Specialist'
      },
      // UX/UI Designer needs one more resource
      {
        title: 'Material Design Guidelines',
        description: 'Comprehensive design system guidelines from Google for creating intuitive user interfaces',
        url: 'https://material.io/design',
        author: 'Google',
        category: 'UX_UI_DESIGN',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true,
        targetCareerPath: 'UX/UI Designer'
      }
    ];

    for (const resourceData of additionalResources) {
      console.log(`➕ Adding: ${resourceData.title}`);
      console.log(`   To: ${resourceData.targetCareerPath}`);

      try {
        // Check if resource already exists
        const existingResource = await prisma.learningResource.findFirst({
          where: {
            OR: [
              { title: resourceData.title },
              { url: resourceData.url }
            ]
          }
        });

        if (existingResource) {
          console.log(`   ⚠️ Resource already exists`);
          continue;
        }

        // Get career path
        const careerPath = await prisma.careerPath.findFirst({
          where: { name: resourceData.targetCareerPath }
        });

        if (!careerPath) {
          console.log(`   ❌ Career path not found`);
          continue;
        }

        // Create new resource
        const { targetCareerPath, ...resourceCreateData } = resourceData;
        
        const newResource = await prisma.learningResource.create({
          data: {
            ...resourceCreateData,
            careerPaths: {
              connect: { id: careerPath.id }
            }
          }
        });

        console.log(`   ✅ Created and connected (ID: ${newResource.id})`);

        optimizationLog.push({
          resourceTitle: resourceData.title,
          targetCareerPath: resourceData.targetCareerPath,
          reason: 'Additional high-quality resource to strengthen career path',
          status: 'CREATED_NEW',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.log(`   ❌ Failed to add: ${error.message}`);
        optimizationLog.push({
          resourceTitle: resourceData.title,
          targetCareerPath: resourceData.targetCareerPath,
          reason: 'Additional high-quality resource to strengthen career path',
          status: 'FAILED',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }

      console.log('');
    }

    // Save optimization log
    const fs = require('fs');
    const outputPath = 'docs/cleanup/final-optimization-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(optimizationLog, null, 2));

    // Summary
    const successful = optimizationLog.filter(log => log.status === 'SUCCESS' || log.status === 'CREATED_NEW').length;
    const failed = optimizationLog.filter(log => log.status === 'FAILED').length;
    const alreadyConnected = optimizationLog.filter(log => log.status === 'ALREADY_CONNECTED').length;

    console.log(`📊 FINAL OPTIMIZATION SUMMARY:`);
    console.log(`Total actions processed: ${optimizationLog.length}`);
    console.log(`Successful optimizations: ${successful}`);
    console.log(`Already optimized: ${alreadyConnected}`);
    console.log(`Failed operations: ${failed}`);

    console.log(`\n💾 Optimization log saved to: ${outputPath}`);

    return optimizationLog;

  } catch (error) {
    console.error('❌ Error during final optimization:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  finalOptimizationPush();
}

module.exports = { finalOptimizationPush };
