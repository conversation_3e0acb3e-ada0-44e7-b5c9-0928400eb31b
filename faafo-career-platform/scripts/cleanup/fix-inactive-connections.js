const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixInactiveConnections() {
  console.log('🔧 FIXING INACTIVE RESOURCE CONNECTIONS');
  console.log('======================================\n');

  try {
    // Find inactive resources that are still connected to career paths
    const inactiveConnectedResources = await prisma.learningResource.findMany({
      where: {
        isActive: false,
        careerPaths: {
          some: {}
        }
      },
      include: {
        careerPaths: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    console.log(`Found ${inactiveConnectedResources.length} inactive resources still connected to career paths\n`);

    const fixLog = [];

    for (const resource of inactiveConnectedResources) {
      console.log(`🔧 Fixing: ${resource.title}`);
      console.log(`   ID: ${resource.id}`);
      console.log(`   Connected to: ${resource.careerPaths.map(cp => cp.name).join(', ')}`);

      try {
        // Disconnect the inactive resource from all career paths
        await prisma.learningResource.update({
          where: { id: resource.id },
          data: {
            careerPaths: {
              disconnect: resource.careerPaths.map(cp => ({ id: cp.id }))
            }
          }
        });

        console.log(`   ✅ Successfully disconnected from ${resource.careerPaths.length} career path(s)`);

        fixLog.push({
          resourceId: resource.id,
          resourceTitle: resource.title,
          disconnectedFrom: resource.careerPaths.map(cp => cp.name),
          status: 'SUCCESS',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.log(`   ❌ Failed to disconnect: ${error.message}`);

        fixLog.push({
          resourceId: resource.id,
          resourceTitle: resource.title,
          disconnectedFrom: resource.careerPaths.map(cp => cp.name),
          status: 'FAILED',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }

      console.log('');
    }

    // Save fix log
    const fs = require('fs');
    const outputPath = 'docs/cleanup/inactive-connections-fix-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(fixLog, null, 2));

    // Summary
    const successful = fixLog.filter(log => log.status === 'SUCCESS').length;
    const failed = fixLog.filter(log => log.status === 'FAILED').length;

    console.log(`📊 INACTIVE CONNECTIONS FIX SUMMARY:`);
    console.log(`Total inactive resources processed: ${fixLog.length}`);
    console.log(`Successfully disconnected: ${successful}`);
    console.log(`Failed to disconnect: ${failed}`);

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      fixLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle}: ${log.error}`);
      });
    }

    console.log(`\n💾 Fix log saved to: ${outputPath}`);

    // Verify the fix by running a quick check
    console.log('\n🔍 Verifying fix...');
    const remainingInactiveConnected = await prisma.learningResource.count({
      where: {
        isActive: false,
        careerPaths: {
          some: {}
        }
      }
    });

    if (remainingInactiveConnected === 0) {
      console.log('✅ Fix successful! No inactive resources are connected to career paths.');
    } else {
      console.log(`❌ ${remainingInactiveConnected} inactive resources still connected. Manual intervention may be required.`);
    }

    return fixLog;

  } catch (error) {
    console.error('❌ Error fixing inactive connections:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  fixInactiveConnections();
}

module.exports = { fixInactiveConnections };
