const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function removePlaceholderResources() {
  console.log('🗑️ REMOVING PLACEHOLDER/TEST RESOURCES');
  console.log('=====================================\n');

  try {
    // Load placeholder data from analysis
    const fs = require('fs');
    const placeholderData = JSON.parse(fs.readFileSync('docs/cleanup/placeholder-data.json', 'utf8'));

    console.log(`📊 Found ${placeholderData.length} placeholder resources to remove\n`);

    const deletionLog = [];

    for (const placeholder of placeholderData) {
      console.log(`🗑️ Removing: ${placeholder.resourceTitle}`);
      console.log(`   ID: ${placeholder.resourceId}`);
      console.log(`   Reason: ${placeholder.recommendation}`);
      console.log(`   Indicators: ${placeholder.placeholderIndicators.join(', ')}`);

      try {
        // Remove the resource (Prisma will handle the many-to-many relationship cleanup automatically)
        const deletedResource = await prisma.learningResource.delete({
          where: {
            id: placeholder.resourceId
          }
        });

        console.log(`   ✅ Successfully deleted`);

        deletionLog.push({
          resourceId: placeholder.resourceId,
          resourceTitle: placeholder.resourceTitle,
          careerPaths: placeholder.careerPaths,
          url: placeholder.url,
          author: placeholder.author,
          category: placeholder.category,
          reason: placeholder.recommendation,
          indicators: placeholder.placeholderIndicators,
          deletedAt: new Date().toISOString(),
          status: 'SUCCESS'
        });

      } catch (error) {
        console.log(`   ❌ Failed to delete: ${error.message}`);
        
        deletionLog.push({
          resourceId: placeholder.resourceId,
          resourceTitle: placeholder.resourceTitle,
          careerPaths: placeholder.careerPaths,
          url: placeholder.url,
          author: placeholder.author,
          category: placeholder.category,
          reason: placeholder.recommendation,
          indicators: placeholder.placeholderIndicators,
          deletedAt: new Date().toISOString(),
          status: 'FAILED',
          error: error.message
        });
      }

      console.log('');
    }

    // Save deletion log
    const outputPath = 'docs/cleanup/placeholder-deletion-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(deletionLog, null, 2));

    // Summary
    const successful = deletionLog.filter(log => log.status === 'SUCCESS').length;
    const failed = deletionLog.filter(log => log.status === 'FAILED').length;

    console.log(`📊 PLACEHOLDER REMOVAL SUMMARY:`);
    console.log(`Total processed: ${deletionLog.length}`);
    console.log(`Successfully deleted: ${successful}`);
    console.log(`Failed deletions: ${failed}`);

    if (failed > 0) {
      console.log('\n❌ Failed deletions:');
      deletionLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle}: ${log.error}`);
      });
    }

    console.log(`\n💾 Deletion log saved to: ${outputPath}`);

    // Verify removal by checking if any placeholder resources still exist
    console.log('\n🔍 Verifying removal...');
    
    const remainingPlaceholders = await prisma.learningResource.findMany({
      where: {
        OR: [
          { url: { contains: 'example.com' } },
          { url: { contains: 'test-project' } },
          { title: { contains: 'Test Project' } },
          { title: { contains: 'test' } },
          { title: { contains: 'placeholder' } }
        ]
      }
    });

    if (remainingPlaceholders.length === 0) {
      console.log('✅ All placeholder resources successfully removed');
    } else {
      console.log(`⚠️ ${remainingPlaceholders.length} placeholder resources still exist:`);
      remainingPlaceholders.forEach(resource => {
        console.log(`  - ${resource.title} (${resource.id})`);
      });
    }

    return deletionLog;

  } catch (error) {
    console.error('❌ Error removing placeholder resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  removePlaceholderResources();
}

module.exports = { removePlaceholderResources };
