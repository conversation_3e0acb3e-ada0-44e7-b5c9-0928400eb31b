const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function remove403Resources() {
  console.log('🗑️ REMOVING RESOURCES WITH 403 FORBIDDEN ERRORS');
  console.log('===============================================\n');

  try {
    // Load broken URLs data from analysis
    const fs = require('fs');
    const brokenUrls = JSON.parse(fs.readFileSync('docs/cleanup/broken-urls-inventory.json', 'utf8'));

    // Filter for 403 errors only
    const resources403 = brokenUrls.filter(resource => resource.errorStatus === '403');

    console.log(`📊 Found ${resources403.length} resources with 403 errors to remove\n`);

    const deletionLog = [];

    for (const resource of resources403) {
      console.log(`🗑️ Analyzing: ${resource.resourceTitle}`);
      console.log(`   ID: ${resource.resourceId}`);
      console.log(`   URL: ${resource.url}`);
      console.log(`   Career Paths: ${resource.careerPaths}`);
      console.log(`   Author: ${resource.author}`);

      // Analyze if this is likely a temporary or permanent block
      const blockAnalysis = analyze403Error(resource.url, resource.author);
      console.log(`   Block Analysis: ${blockAnalysis.type} - ${blockAnalysis.reason}`);

      try {
        // Check if resource still exists (might have been deleted in previous steps)
        const existingResource = await prisma.learningResource.findUnique({
          where: { id: resource.resourceId }
        });

        if (!existingResource) {
          console.log(`   ⚠️ Resource already deleted, skipping`);
          
          deletionLog.push({
            resourceId: resource.resourceId,
            resourceTitle: resource.resourceTitle,
            careerPaths: resource.careerPaths,
            url: resource.url,
            author: resource.author,
            category: resource.category,
            errorStatus: resource.errorStatus,
            blockAnalysis: blockAnalysis,
            reason: '403 Forbidden',
            deletedAt: new Date().toISOString(),
            status: 'ALREADY_DELETED'
          });
          
          console.log('');
          continue;
        }

        // Check for user progress records that would prevent deletion
        const userProgress = await prisma.userLearningProgress.findMany({
          where: { resourceId: resource.resourceId }
        });

        if (userProgress.length > 0) {
          console.log(`   ⚠️ Resource has ${userProgress.length} user progress records, marking as inactive instead`);
          
          // Mark as inactive instead of deleting
          await prisma.learningResource.update({
            where: { id: resource.resourceId },
            data: { isActive: false }
          });

          deletionLog.push({
            resourceId: resource.resourceId,
            resourceTitle: resource.resourceTitle,
            careerPaths: resource.careerPaths,
            url: resource.url,
            author: resource.author,
            category: resource.category,
            errorStatus: resource.errorStatus,
            blockAnalysis: blockAnalysis,
            reason: '403 Forbidden - marked inactive due to user progress',
            deletedAt: new Date().toISOString(),
            status: 'MARKED_INACTIVE',
            userProgressCount: userProgress.length
          });

        } else if (blockAnalysis.type === 'PERMANENT') {
          // Safe to delete - permanent block and no user progress
          await prisma.learningResource.delete({
            where: { id: resource.resourceId }
          });

          console.log(`   ✅ Successfully deleted (permanent block)`);

          deletionLog.push({
            resourceId: resource.resourceId,
            resourceTitle: resource.resourceTitle,
            careerPaths: resource.careerPaths,
            url: resource.url,
            author: resource.author,
            category: resource.category,
            errorStatus: resource.errorStatus,
            blockAnalysis: blockAnalysis,
            reason: '403 Forbidden - permanent block',
            deletedAt: new Date().toISOString(),
            status: 'DELETED'
          });

        } else {
          // Temporary block - mark as inactive for review
          await prisma.learningResource.update({
            where: { id: resource.resourceId },
            data: { isActive: false }
          });

          console.log(`   ⚠️ Marked as inactive (temporary block - needs review)`);

          deletionLog.push({
            resourceId: resource.resourceId,
            resourceTitle: resource.resourceTitle,
            careerPaths: resource.careerPaths,
            url: resource.url,
            author: resource.author,
            category: resource.category,
            errorStatus: resource.errorStatus,
            blockAnalysis: blockAnalysis,
            reason: '403 Forbidden - temporary block, marked inactive for review',
            deletedAt: new Date().toISOString(),
            status: 'MARKED_INACTIVE_REVIEW'
          });
        }

      } catch (error) {
        console.log(`   ❌ Failed to process: ${error.message}`);
        
        deletionLog.push({
          resourceId: resource.resourceId,
          resourceTitle: resource.resourceTitle,
          careerPaths: resource.careerPaths,
          url: resource.url,
          author: resource.author,
          category: resource.category,
          errorStatus: resource.errorStatus,
          blockAnalysis: blockAnalysis,
          reason: '403 Forbidden',
          deletedAt: new Date().toISOString(),
          status: 'FAILED',
          error: error.message
        });
      }

      console.log('');
    }

    // Save deletion log
    const outputPath = 'docs/cleanup/403-deletion-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(deletionLog, null, 2));

    // Summary
    const deleted = deletionLog.filter(log => log.status === 'DELETED').length;
    const markedInactive = deletionLog.filter(log => log.status === 'MARKED_INACTIVE').length;
    const markedInactiveReview = deletionLog.filter(log => log.status === 'MARKED_INACTIVE_REVIEW').length;
    const alreadyDeleted = deletionLog.filter(log => log.status === 'ALREADY_DELETED').length;
    const failed = deletionLog.filter(log => log.status === 'FAILED').length;

    console.log(`📊 403 RESOURCE REMOVAL SUMMARY:`);
    console.log(`Total processed: ${deletionLog.length}`);
    console.log(`Successfully deleted: ${deleted}`);
    console.log(`Marked inactive (user progress): ${markedInactive}`);
    console.log(`Marked inactive (review needed): ${markedInactiveReview}`);
    console.log(`Already deleted: ${alreadyDeleted}`);
    console.log(`Failed operations: ${failed}`);

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      deletionLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle}: ${log.error}`);
      });
    }

    console.log(`\n💾 Deletion log saved to: ${outputPath}`);

    return deletionLog;

  } catch (error) {
    console.error('❌ Error removing 403 resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function analyze403Error(url, author) {
  // Analyze the URL and author to determine if this is likely a temporary or permanent block
  
  // Known patterns for permanent blocks
  if (url.includes('futurelearn.com')) {
    return {
      type: 'PERMANENT',
      reason: 'FutureLearn often blocks automated requests permanently'
    };
  }
  
  if (url.includes('fidelity.com')) {
    return {
      type: 'PERMANENT', 
      reason: 'Financial services sites often have strict access controls'
    };
  }
  
  if (url.includes('theodinproject.com')) {
    return {
      type: 'TEMPORARY',
      reason: 'The Odin Project may have temporary access restrictions'
    };
  }
  
  // Default to temporary for review
  return {
    type: 'TEMPORARY',
    reason: 'Unknown 403 cause - needs manual review'
  };
}

if (require.main === module) {
  remove403Resources();
}

module.exports = { remove403Resources };
