const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addCloudArchitectResources() {
  console.log('☁️ ADDING HIGH-QUALITY CLOUD SOLUTIONS ARCHITECT RESOURCES');
  console.log('=========================================================\n');

  try {
    // Get Cloud Solutions Architect career path
    const cloudArchitectPath = await prisma.careerPath.findFirst({
      where: { name: 'Cloud Solutions Architect' },
      include: {
        learningResources: true
      }
    });

    if (!cloudArchitectPath) {
      console.log('❌ Cloud Solutions Architect career path not found');
      return;
    }

    console.log(`📊 Cloud Solutions Architect path currently has ${cloudArchitectPath.learningResources.length} resources\n`);

    // Define high-quality Cloud Architecture resources to add
    const cloudArchitectResources = [
      {
        title: 'AWS Well-Architected Framework',
        description: 'Learn the five pillars of the AWS Well-Architected Framework: operational excellence, security, reliability, performance efficiency, and cost optimization',
        url: 'https://aws.amazon.com/architecture/well-architected/',
        author: 'Amazon Web Services',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'Microsoft Azure Architecture Center',
        description: 'Comprehensive guidance on Azure cloud architecture patterns, best practices, and reference architectures',
        url: 'https://docs.microsoft.com/en-us/azure/architecture/',
        author: 'Microsoft',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'Google Cloud Architecture Framework',
        description: 'Learn cloud architecture principles and best practices for designing scalable, secure, and cost-effective solutions on Google Cloud',
        url: 'https://cloud.google.com/architecture/framework',
        author: 'Google Cloud',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'AWS Solutions Architect Associate Certification',
        description: 'Comprehensive preparation for AWS Solutions Architect Associate certification covering core AWS services and architecture patterns',
        url: 'https://aws.amazon.com/certification/certified-solutions-architect-associate/',
        author: 'Amazon Web Services',
        category: 'DEVOPS',
        type: 'CERTIFICATION',
        skillLevel: 'INTERMEDIATE',
        cost: 'PAID',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'Cloud Security Best Practices',
        description: 'Essential security principles and practices for cloud architecture including identity management, data protection, and compliance',
        url: 'https://cloud.google.com/security/best-practices',
        author: 'Google Cloud',
        category: 'CYBERSECURITY',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'Multi-Cloud Architecture Patterns',
        description: 'Learn strategies for designing applications that work across multiple cloud providers for resilience and vendor independence',
        url: 'https://www.redhat.com/en/topics/cloud-computing/what-is-multicloud',
        author: 'Red Hat',
        category: 'DEVOPS',
        type: 'ARTICLE',
        skillLevel: 'ADVANCED',
        cost: 'FREE',
        format: 'THEORETICAL',
        isActive: true
      },
      {
        title: 'Cloud Cost Optimization Strategies',
        description: 'Comprehensive guide to optimizing cloud costs through right-sizing, reserved instances, and architectural decisions',
        url: 'https://aws.amazon.com/aws-cost-management/',
        author: 'Amazon Web Services',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'Serverless Architecture Patterns',
        description: 'Learn serverless computing patterns and best practices for building scalable, event-driven applications',
        url: 'https://aws.amazon.com/serverless/',
        author: 'Amazon Web Services',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'HANDS_ON',
        isActive: true
      },
      {
        title: 'Cloud Migration Strategies',
        description: 'Comprehensive guide to cloud migration approaches including lift-and-shift, re-platforming, and re-architecting',
        url: 'https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/migrate/',
        author: 'Microsoft',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'ADVANCED',
        cost: 'FREE',
        format: 'THEORETICAL',
        isActive: true
      },
      {
        title: 'Infrastructure as Code with Terraform',
        description: 'Learn to manage cloud infrastructure as code using Terraform for consistent, repeatable deployments across multiple cloud providers',
        url: 'https://learn.hashicorp.com/terraform',
        author: 'HashiCorp',
        category: 'DEVOPS',
        type: 'COURSE',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'HANDS_ON',
        isActive: true
      }
    ];

    const additionLog = [];

    for (const resourceData of cloudArchitectResources) {
      console.log(`➕ Adding: ${resourceData.title}`);
      console.log(`   Author: ${resourceData.author}`);
      console.log(`   URL: ${resourceData.url}`);
      console.log(`   Skill Level: ${resourceData.skillLevel}`);
      console.log(`   Category: ${resourceData.category}`);

      try {
        // Check if resource already exists
        const existingResource = await prisma.learningResource.findFirst({
          where: {
            OR: [
              { title: resourceData.title },
              { url: resourceData.url }
            ]
          }
        });

        if (existingResource) {
          console.log(`   ⚠️ Resource already exists, connecting to Cloud Solutions Architect path`);
          
          // Connect existing resource to Cloud Solutions Architect path
          await prisma.careerPath.update({
            where: { id: cloudArchitectPath.id },
            data: {
              learningResources: {
                connect: { id: existingResource.id }
              }
            }
          });

          additionLog.push({
            resourceId: existingResource.id,
            resourceTitle: resourceData.title,
            author: resourceData.author,
            url: resourceData.url,
            category: resourceData.category,
            skillLevel: resourceData.skillLevel,
            cost: resourceData.cost,
            careerPath: 'Cloud Solutions Architect',
            action: 'CONNECTED_EXISTING',
            addedAt: new Date().toISOString(),
            status: 'SUCCESS'
          });

        } else {
          // Create new resource
          const newResource = await prisma.learningResource.create({
            data: {
              ...resourceData,
              careerPaths: {
                connect: { id: cloudArchitectPath.id }
              }
            }
          });

          console.log(`   ✅ Created and connected new resource (ID: ${newResource.id})`);

          additionLog.push({
            resourceId: newResource.id,
            resourceTitle: resourceData.title,
            author: resourceData.author,
            url: resourceData.url,
            category: resourceData.category,
            skillLevel: resourceData.skillLevel,
            cost: resourceData.cost,
            careerPath: 'Cloud Solutions Architect',
            action: 'CREATED_NEW',
            addedAt: new Date().toISOString(),
            status: 'SUCCESS'
          });
        }

      } catch (error) {
        console.log(`   ❌ Failed to add: ${error.message}`);
        
        additionLog.push({
          resourceId: null,
          resourceTitle: resourceData.title,
          author: resourceData.author,
          url: resourceData.url,
          category: resourceData.category,
          skillLevel: resourceData.skillLevel,
          cost: resourceData.cost,
          careerPath: 'Cloud Solutions Architect',
          action: 'FAILED',
          addedAt: new Date().toISOString(),
          status: 'FAILED',
          error: error.message
        });
      }

      console.log('');
    }

    // Check final state
    const updatedCloudArchitectPath = await prisma.careerPath.findFirst({
      where: { name: 'Cloud Solutions Architect' },
      include: {
        learningResources: {
          select: {
            id: true,
            title: true,
            author: true,
            skillLevel: true,
            cost: true,
            category: true
          }
        }
      }
    });

    console.log(`📊 Cloud Solutions Architect path now has ${updatedCloudArchitectPath.learningResources.length} resources\n`);
    
    console.log('☁️ Cloud Solutions Architect resources:');
    updatedCloudArchitectPath.learningResources.forEach(resource => {
      console.log(`   • ${resource.title} (${resource.skillLevel}) - ${resource.author}`);
    });

    // Save addition log
    const fs = require('fs');
    const outputPath = 'docs/cleanup/cloud-architect-resources-addition-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(additionLog, null, 2));

    // Summary
    const created = additionLog.filter(log => log.status === 'SUCCESS' && log.action === 'CREATED_NEW').length;
    const connected = additionLog.filter(log => log.status === 'SUCCESS' && log.action === 'CONNECTED_EXISTING').length;
    const failed = additionLog.filter(log => log.status === 'FAILED').length;

    console.log(`\n📊 CLOUD SOLUTIONS ARCHITECT RESOURCES ADDITION SUMMARY:`);
    console.log(`Total resources processed: ${additionLog.length}`);
    console.log(`New resources created: ${created}`);
    console.log(`Existing resources connected: ${connected}`);
    console.log(`Failed operations: ${failed}`);
    console.log(`Resources before: ${cloudArchitectPath.learningResources.length}`);
    console.log(`Resources after: ${updatedCloudArchitectPath.learningResources.length}`);

    // Summary by skill level
    console.log('\n📈 Resources by Skill Level:');
    const bySkillLevel = {};
    updatedCloudArchitectPath.learningResources.forEach(resource => {
      bySkillLevel[resource.skillLevel] = (bySkillLevel[resource.skillLevel] || 0) + 1;
    });
    
    Object.entries(bySkillLevel).forEach(([level, count]) => {
      console.log(`   ${level}: ${count} resources`);
    });

    // Summary by category
    console.log('\n📂 Resources by Category:');
    const byCategory = {};
    updatedCloudArchitectPath.learningResources.forEach(resource => {
      byCategory[resource.category] = (byCategory[resource.category] || 0) + 1;
    });
    
    Object.entries(byCategory).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} resources`);
    });

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      additionLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle}: ${log.error}`);
      });
    }

    console.log(`\n💾 Addition log saved to: ${outputPath}`);

    return additionLog;

  } catch (error) {
    console.error('❌ Error adding Cloud Solutions Architect resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  addCloudArchitectResources();
}

module.exports = { addCloudArchitectResources };
