const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function removeProjectManagementFromNonManagementPaths() {
  console.log('📋 REMOVING PROJECT MANAGEMENT FROM NON-MANAGEMENT PATHS');
  console.log('=======================================================\n');

  try {
    // Define career paths that should not have generic project management courses
    const nonManagementPaths = [
      'UX/UI Designer',
      'Freelance Web Developer'
    ];

    // Define generic project management courses to remove
    const projectManagementCourses = [
      'Project Management Foundations',
      'Introduction to Project Management'
    ];

    const removalLog = [];

    for (const pathName of nonManagementPaths) {
      console.log(`👨‍💼 Processing: ${pathName}`);
      
      // Get the career path with its resources
      const careerPath = await prisma.careerPath.findFirst({
        where: { name: pathName },
        include: {
          learningResources: {
            select: {
              id: true,
              title: true,
              category: true,
              url: true,
              author: true,
              description: true
            }
          }
        }
      });

      if (!careerPath) {
        console.log(`   ❌ Career path not found: ${pathName}\n`);
        continue;
      }

      console.log(`   📊 Currently has ${careerPath.learningResources.length} resources`);

      let removedCount = 0;

      for (const courseTitle of projectManagementCourses) {
        console.log(`   🔍 Looking for: ${courseTitle}`);
        
        // Find the resource in this career path
        const resource = careerPath.learningResources.find(r => r.title === courseTitle);
        
        if (resource) {
          console.log(`     Found: ${resource.title} (${resource.category})`);
          console.log(`     Author: ${resource.author}`);

          // Assess relevance to the specific role
          const relevanceAssessment = assessProjectManagementRelevance(pathName, resource);
          console.log(`     Relevance: ${relevanceAssessment.relevant ? 'RELEVANT' : 'NOT RELEVANT'} - ${relevanceAssessment.reason}`);

          if (!relevanceAssessment.relevant) {
            try {
              // Remove the connection between this resource and the career path
              await prisma.careerPath.update({
                where: { id: careerPath.id },
                data: {
                  learningResources: {
                    disconnect: { id: resource.id }
                  }
                }
              });

              console.log(`     ✅ Disconnected from ${pathName}`);
              removedCount++;

              removalLog.push({
                resourceId: resource.id,
                resourceTitle: resource.title,
                category: resource.category,
                author: resource.author,
                url: resource.url,
                careerPath: pathName,
                action: 'DISCONNECTED',
                reason: relevanceAssessment.reason,
                removedAt: new Date().toISOString(),
                status: 'SUCCESS'
              });

            } catch (error) {
              console.log(`     ❌ Failed to disconnect: ${error.message}`);
              
              removalLog.push({
                resourceId: resource.id,
                resourceTitle: resource.title,
                category: resource.category,
                author: resource.author,
                url: resource.url,
                careerPath: pathName,
                action: 'DISCONNECTED',
                reason: relevanceAssessment.reason,
                removedAt: new Date().toISOString(),
                status: 'FAILED',
                error: error.message
              });
            }
          } else {
            console.log(`     ⚠️ Keeping resource (relevant to role)`);
            
            removalLog.push({
              resourceId: resource.id,
              resourceTitle: resource.title,
              category: resource.category,
              author: resource.author,
              url: resource.url,
              careerPath: pathName,
              action: 'KEPT',
              reason: relevanceAssessment.reason,
              removedAt: new Date().toISOString(),
              status: 'KEPT'
            });
          }
        } else {
          console.log(`     ⚠️ Not found (may have been already removed)`);
        }
      }

      // Check final state for this path
      const updatedPath = await prisma.careerPath.findFirst({
        where: { name: pathName },
        include: {
          learningResources: {
            select: {
              id: true,
              title: true,
              category: true
            }
          }
        }
      });

      console.log(`   📊 Now has ${updatedPath.learningResources.length} resources (removed ${removedCount})`);
      
      if (updatedPath.learningResources.length > 0) {
        console.log(`   📚 Remaining resources by category:`);
        const byCategory = {};
        updatedPath.learningResources.forEach(resource => {
          byCategory[resource.category] = (byCategory[resource.category] || 0) + 1;
        });
        
        Object.entries(byCategory).forEach(([category, count]) => {
          console.log(`     ${category}: ${count} resources`);
        });
      }
      
      console.log('');
    }

    // Save removal log
    const fs = require('fs');
    const outputPath = 'docs/cleanup/project-management-removal-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(removalLog, null, 2));

    // Summary
    const successful = removalLog.filter(log => log.status === 'SUCCESS').length;
    const kept = removalLog.filter(log => log.status === 'KEPT').length;
    const failed = removalLog.filter(log => log.status === 'FAILED').length;

    console.log(`📊 PROJECT MANAGEMENT REMOVAL SUMMARY:`);
    console.log(`Total processed: ${removalLog.length}`);
    console.log(`Successfully disconnected: ${successful}`);
    console.log(`Kept (relevant): ${kept}`);
    console.log(`Failed operations: ${failed}`);

    // Summary by career path
    console.log('\n📈 Actions by Career Path:');
    const byPath = {};
    removalLog.forEach(log => {
      const key = `${log.careerPath}_${log.status}`;
      byPath[key] = (byPath[key] || 0) + 1;
    });
    
    Object.entries(byPath).forEach(([key, count]) => {
      const [path, status] = key.split('_');
      console.log(`   ${path}: ${count} ${status.toLowerCase()}`);
    });

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      removalLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle} from ${log.careerPath}: ${log.error}`);
      });
    }

    console.log(`\n💾 Removal log saved to: ${outputPath}`);

    return removalLog;

  } catch (error) {
    console.error('❌ Error removing project management resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function assessProjectManagementRelevance(careerPath, resource) {
  // Assess if project management is relevant to specific career paths
  
  if (careerPath === 'UX/UI Designer') {
    // UX/UI designers often work on projects and need some PM skills
    // But generic PM courses are usually not core to the role
    return {
      relevant: false,
      reason: 'Generic project management not core to UX/UI design role - focus should be on design-specific skills'
    };
  }
  
  if (careerPath === 'Freelance Web Developer') {
    // Freelancers do need project management skills to manage client projects
    // But they need freelance-specific PM skills, not generic corporate PM
    return {
      relevant: false,
      reason: 'Generic corporate project management not relevant to freelance web development - freelancers need client management and project delivery skills instead'
    };
  }
  
  // Default to not relevant for other paths
  return {
    relevant: false,
    reason: 'Generic project management not core to this career path'
  };
}

if (require.main === module) {
  removeProjectManagementFromNonManagementPaths();
}

module.exports = { removeProjectManagementFromNonManagementPaths };
