const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanOrphanedRelationships() {
  console.log('🧹 CLEANING UP ORPHANED RELATIONSHIPS');
  console.log('====================================\n');

  try {
    const cleanupLog = [];

    // 1. Check for orphaned UserLearningProgress records
    console.log('🔍 Checking for orphaned UserLearningProgress records...');

    // Find progress records that reference non-existent resources
    const allResources = await prisma.learningResource.findMany({
      select: { id: true }
    });
    const resourceIds = new Set(allResources.map(r => r.id));

    const allUserProgress = await prisma.userLearningProgress.findMany({
      include: {
        user: {
          select: { email: true }
        }
      }
    });

    const orphanedUserProgress = allUserProgress.filter(p => !resourceIds.has(p.resourceId));

    console.log(`   Found ${orphanedUserProgress.length} orphaned UserLearningProgress records`);

    if (orphanedUserProgress.length > 0) {
      console.log('   Deleting orphaned UserLearningProgress records...');

      for (const progress of orphanedUserProgress) {
        try {
          await prisma.userLearningProgress.delete({
            where: { id: progress.id }
          });

          cleanupLog.push({
            type: 'UserLearningProgress',
            id: progress.id,
            resourceId: progress.resourceId,
            userId: progress.userId,
            userEmail: progress.user?.email,
            status: 'DELETED',
            deletedAt: new Date().toISOString()
          });

          console.log(`     ✅ Deleted progress record for resource ${progress.resourceId}`);
        } catch (error) {
          cleanupLog.push({
            type: 'UserLearningProgress',
            id: progress.id,
            resourceId: progress.resourceId,
            userId: progress.userId,
            userEmail: progress.user?.email,
            status: 'FAILED',
            error: error.message,
            deletedAt: new Date().toISOString()
          });

          console.log(`     ❌ Failed to delete progress record: ${error.message}`);
        }
      }
    }

    // 2. Check for orphaned ResourceRating records
    console.log('\n🔍 Checking for orphaned ResourceRating records...');

    const allRatings = await prisma.resourceRating.findMany({
      include: {
        user: {
          select: { email: true }
        }
      }
    });

    const orphanedRatings = allRatings.filter(r => !resourceIds.has(r.resourceId));

    console.log(`   Found ${orphanedRatings.length} orphaned ResourceRating records`);

    if (orphanedRatings.length > 0) {
      console.log('   Deleting orphaned ResourceRating records...');

      for (const rating of orphanedRatings) {
        try {
          await prisma.resourceRating.delete({
            where: { id: rating.id }
          });

          cleanupLog.push({
            type: 'ResourceRating',
            id: rating.id,
            resourceId: rating.resourceId,
            userId: rating.userId,
            userEmail: rating.user?.email,
            rating: rating.rating,
            status: 'DELETED',
            deletedAt: new Date().toISOString()
          });

          console.log(`     ✅ Deleted rating record for resource ${rating.resourceId}`);
        } catch (error) {
          cleanupLog.push({
            type: 'ResourceRating',
            id: rating.id,
            resourceId: rating.resourceId,
            userId: rating.userId,
            userEmail: rating.user?.email,
            rating: rating.rating,
            status: 'FAILED',
            error: error.message,
            deletedAt: new Date().toISOString()
          });

          console.log(`     ❌ Failed to delete rating record: ${error.message}`);
        }
      }
    }

    // 3. Check for orphaned LearningPathStep records
    console.log('\n🔍 Checking for orphaned LearningPathStep records...');

    const allPathSteps = await prisma.learningPathStep.findMany();
    const orphanedPathSteps = allPathSteps.filter(s => s.resourceId && !resourceIds.has(s.resourceId));

    console.log(`   Found ${orphanedPathSteps.length} orphaned LearningPathStep records`);

    if (orphanedPathSteps.length > 0) {
      console.log('   Deleting orphaned LearningPathStep records...');

      for (const step of orphanedPathSteps) {
        try {
          await prisma.learningPathStep.delete({
            where: { id: step.id }
          });

          cleanupLog.push({
            type: 'LearningPathStep',
            id: step.id,
            resourceId: step.resourceId,
            learningPathId: step.learningPathId,
            stepNumber: step.stepNumber,
            status: 'DELETED',
            deletedAt: new Date().toISOString()
          });

          console.log(`     ✅ Deleted path step for resource ${step.resourceId}`);
        } catch (error) {
          cleanupLog.push({
            type: 'LearningPathStep',
            id: step.id,
            resourceId: step.resourceId,
            learningPathId: step.learningPathId,
            stepNumber: step.stepNumber,
            status: 'FAILED',
            error: error.message,
            deletedAt: new Date().toISOString()
          });

          console.log(`     ❌ Failed to delete path step: ${error.message}`);
        }
      }
    }

    // 4. Verify database integrity
    console.log('\n🔍 Verifying database integrity...');

    // Re-check for any remaining orphaned records after cleanup
    const remainingUserProgress = await prisma.userLearningProgress.findMany({
      select: { resourceId: true }
    });
    const remainingRatings = await prisma.resourceRating.findMany({
      select: { resourceId: true }
    });
    const remainingPathSteps = await prisma.learningPathStep.findMany({
      select: { resourceId: true }
    });

    const invalidProgressRefs = remainingUserProgress.filter(p => !resourceIds.has(p.resourceId));
    const invalidRatingRefs = remainingRatings.filter(r => !resourceIds.has(r.resourceId));
    const invalidStepRefs = remainingPathSteps.filter(s => s.resourceId && !resourceIds.has(s.resourceId));

    console.log(`   UserLearningProgress with invalid refs: ${invalidProgressRefs.length}`);
    console.log(`   ResourceRating with invalid refs: ${invalidRatingRefs.length}`);
    console.log(`   LearningPathStep with invalid refs: ${invalidStepRefs.length}`);

    // Save cleanup log
    const fs = require('fs');
    const outputPath = 'docs/cleanup/orphaned-cleanup-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(cleanupLog, null, 2));

    // Summary
    const deletedProgress = cleanupLog.filter(log => log.type === 'UserLearningProgress' && log.status === 'DELETED').length;
    const deletedRatings = cleanupLog.filter(log => log.type === 'ResourceRating' && log.status === 'DELETED').length;
    const deletedSteps = cleanupLog.filter(log => log.type === 'LearningPathStep' && log.status === 'DELETED').length;
    const failed = cleanupLog.filter(log => log.status === 'FAILED').length;

    console.log(`\n📊 ORPHANED RELATIONSHIP CLEANUP SUMMARY:`);
    console.log(`UserLearningProgress deleted: ${deletedProgress}`);
    console.log(`ResourceRating deleted: ${deletedRatings}`);
    console.log(`LearningPathStep deleted: ${deletedSteps}`);
    console.log(`Failed operations: ${failed}`);
    console.log(`Total orphaned records cleaned: ${deletedProgress + deletedRatings + deletedSteps}`);

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      cleanupLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.type} ${log.id}: ${log.error}`);
      });
    }

    console.log(`\n💾 Cleanup log saved to: ${outputPath}`);

    if (invalidProgressRefs.length === 0 && invalidRatingRefs.length === 0 && invalidStepRefs.length === 0) {
      console.log('\n✅ Database integrity verified - no orphaned relationships found');
    } else {
      console.log('\n⚠️ Database integrity issues detected - manual review may be needed');
    }

    return cleanupLog;

  } catch (error) {
    console.error('❌ Error cleaning orphaned relationships:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  cleanOrphanedRelationships();
}

module.exports = { cleanOrphanedRelationships };
