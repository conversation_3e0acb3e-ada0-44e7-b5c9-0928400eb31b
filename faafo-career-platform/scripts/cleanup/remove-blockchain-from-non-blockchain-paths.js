const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function removeBlockchainFromNonBlockchainPaths() {
  console.log('⛓️ REMOVING BLOCKCHAIN RESOURCES FROM NON-BLOCKCHAIN PATHS');
  console.log('=========================================================\n');

  try {
    // Define career paths that should not have blockchain resources
    const nonBlockchainPaths = [
      'Freelance Web Developer',
      'Simple Online Business Owner'
    ];

    // Define blockchain courses to remove
    const blockchainCourses = [
      'Blockchain Basics',
      'Blockchain Fundamentals'
    ];

    const removalLog = [];

    for (const pathName of nonBlockchainPaths) {
      console.log(`💼 Processing: ${pathName}`);
      
      // Get the career path with its resources
      const careerPath = await prisma.careerPath.findFirst({
        where: { name: pathName },
        include: {
          learningResources: {
            select: {
              id: true,
              title: true,
              category: true,
              url: true,
              author: true
            }
          }
        }
      });

      if (!careerPath) {
        console.log(`   ❌ Career path not found: ${pathName}\n`);
        continue;
      }

      console.log(`   📊 Currently has ${careerPath.learningResources.length} resources`);

      let removedCount = 0;

      for (const courseTitle of blockchainCourses) {
        console.log(`   🔍 Looking for: ${courseTitle}`);
        
        // Find the resource in this career path
        const resource = careerPath.learningResources.find(r => r.title === courseTitle);
        
        if (resource) {
          console.log(`     Found: ${resource.title} (${resource.category})`);
          console.log(`     Author: ${resource.author}`);

          try {
            // Remove the connection between this resource and the career path
            await prisma.careerPath.update({
              where: { id: careerPath.id },
              data: {
                learningResources: {
                  disconnect: { id: resource.id }
                }
              }
            });

            console.log(`     ✅ Disconnected from ${pathName}`);
            removedCount++;

            removalLog.push({
              resourceId: resource.id,
              resourceTitle: resource.title,
              category: resource.category,
              author: resource.author,
              url: resource.url,
              careerPath: pathName,
              action: 'DISCONNECTED',
              reason: 'Blockchain technology not core requirement for this career path',
              removedAt: new Date().toISOString(),
              status: 'SUCCESS'
            });

          } catch (error) {
            console.log(`     ❌ Failed to disconnect: ${error.message}`);
            
            removalLog.push({
              resourceId: resource.id,
              resourceTitle: resource.title,
              category: resource.category,
              author: resource.author,
              url: resource.url,
              careerPath: pathName,
              action: 'DISCONNECTED',
              reason: 'Blockchain technology not core requirement for this career path',
              removedAt: new Date().toISOString(),
              status: 'FAILED',
              error: error.message
            });
          }
        } else {
          console.log(`     ⚠️ Not found (may have been already removed)`);
        }
      }

      // Check final state for this path
      const updatedPath = await prisma.careerPath.findFirst({
        where: { name: pathName },
        include: {
          learningResources: {
            select: {
              id: true,
              title: true,
              category: true
            }
          }
        }
      });

      console.log(`   📊 Now has ${updatedPath.learningResources.length} resources (removed ${removedCount})`);
      
      if (updatedPath.learningResources.length > 0) {
        console.log(`   📚 Remaining resources by category:`);
        const byCategory = {};
        updatedPath.learningResources.forEach(resource => {
          byCategory[resource.category] = (byCategory[resource.category] || 0) + 1;
        });
        
        Object.entries(byCategory).forEach(([category, count]) => {
          console.log(`     ${category}: ${count} resources`);
        });
      }
      
      console.log('');
    }

    // Save removal log
    const fs = require('fs');
    const outputPath = 'docs/cleanup/blockchain-removal-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(removalLog, null, 2));

    // Summary
    const successful = removalLog.filter(log => log.status === 'SUCCESS').length;
    const failed = removalLog.filter(log => log.status === 'FAILED').length;

    console.log(`📊 BLOCKCHAIN RESOURCES REMOVAL SUMMARY:`);
    console.log(`Total processed: ${removalLog.length}`);
    console.log(`Successfully disconnected: ${successful}`);
    console.log(`Failed operations: ${failed}`);

    // Summary by career path
    console.log('\n📈 Removals by Career Path:');
    const byPath = {};
    removalLog.filter(log => log.status === 'SUCCESS').forEach(log => {
      byPath[log.careerPath] = (byPath[log.careerPath] || 0) + 1;
    });
    
    Object.entries(byPath).forEach(([path, count]) => {
      console.log(`   ${path}: ${count} blockchain courses removed`);
    });

    // Summary by course type
    console.log('\n⛓️ Blockchain Courses Removed:');
    const byCourse = {};
    removalLog.filter(log => log.status === 'SUCCESS').forEach(log => {
      byCourse[log.resourceTitle] = (byCourse[log.resourceTitle] || 0) + 1;
    });
    
    Object.entries(byCourse).forEach(([course, count]) => {
      console.log(`   ${course}: removed from ${count} path(s)`);
    });

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      removalLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle} from ${log.careerPath}: ${log.error}`);
      });
    }

    console.log(`\n💾 Removal log saved to: ${outputPath}`);

    // Check if blockchain resources are now orphaned (not connected to any career path)
    console.log('\n🔍 Checking for orphaned blockchain resources...');
    
    for (const courseTitle of blockchainCourses) {
      const resource = await prisma.learningResource.findFirst({
        where: { title: courseTitle },
        include: {
          careerPaths: {
            select: { name: true }
          }
        }
      });

      if (resource) {
        if (resource.careerPaths.length === 0) {
          console.log(`   ⚠️ ${courseTitle} is now orphaned (not connected to any career path)`);
        } else {
          console.log(`   ✅ ${courseTitle} still connected to: ${resource.careerPaths.map(cp => cp.name).join(', ')}`);
        }
      }
    }

    return removalLog;

  } catch (error) {
    console.error('❌ Error removing blockchain resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  removeBlockchainFromNonBlockchainPaths();
}

module.exports = { removeBlockchainFromNonBlockchainPaths };
