const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function remove404Resources() {
  console.log('🗑️ REMOVING RESOURCES WITH 404 ERRORS');
  console.log('=====================================\n');

  try {
    // Load broken URLs data from analysis
    const fs = require('fs');
    const brokenUrls = JSON.parse(fs.readFileSync('docs/cleanup/broken-urls-inventory.json', 'utf8'));

    // Filter for 404 errors only
    const resources404 = brokenUrls.filter(resource => resource.errorStatus === '404');

    console.log(`📊 Found ${resources404.length} resources with 404 errors to remove\n`);

    const deletionLog = [];

    for (const resource of resources404) {
      console.log(`🗑️ Removing: ${resource.resourceTitle}`);
      console.log(`   ID: ${resource.resourceId}`);
      console.log(`   URL: ${resource.url}`);
      console.log(`   Career Paths: ${resource.careerPaths}`);
      console.log(`   Author: ${resource.author}`);

      try {
        // Check if resource still exists (might have been deleted in previous steps)
        const existingResource = await prisma.learningResource.findUnique({
          where: { id: resource.resourceId }
        });

        if (!existingResource) {
          console.log(`   ⚠️ Resource already deleted, skipping`);
          
          deletionLog.push({
            resourceId: resource.resourceId,
            resourceTitle: resource.resourceTitle,
            careerPaths: resource.careerPaths,
            url: resource.url,
            author: resource.author,
            category: resource.category,
            errorStatus: resource.errorStatus,
            reason: '404 Not Found',
            deletedAt: new Date().toISOString(),
            status: 'ALREADY_DELETED'
          });
          
          console.log('');
          continue;
        }

        // Check for user progress records that would prevent deletion
        const userProgress = await prisma.userLearningProgress.findMany({
          where: { resourceId: resource.resourceId }
        });

        if (userProgress.length > 0) {
          console.log(`   ⚠️ Resource has ${userProgress.length} user progress records, marking as inactive instead`);
          
          // Mark as inactive instead of deleting
          await prisma.learningResource.update({
            where: { id: resource.resourceId },
            data: { isActive: false }
          });

          deletionLog.push({
            resourceId: resource.resourceId,
            resourceTitle: resource.resourceTitle,
            careerPaths: resource.careerPaths,
            url: resource.url,
            author: resource.author,
            category: resource.category,
            errorStatus: resource.errorStatus,
            reason: '404 Not Found - marked inactive due to user progress',
            deletedAt: new Date().toISOString(),
            status: 'MARKED_INACTIVE',
            userProgressCount: userProgress.length
          });

        } else {
          // Safe to delete - no user progress records
          await prisma.learningResource.delete({
            where: { id: resource.resourceId }
          });

          console.log(`   ✅ Successfully deleted`);

          deletionLog.push({
            resourceId: resource.resourceId,
            resourceTitle: resource.resourceTitle,
            careerPaths: resource.careerPaths,
            url: resource.url,
            author: resource.author,
            category: resource.category,
            errorStatus: resource.errorStatus,
            reason: '404 Not Found',
            deletedAt: new Date().toISOString(),
            status: 'DELETED'
          });
        }

      } catch (error) {
        console.log(`   ❌ Failed to process: ${error.message}`);
        
        deletionLog.push({
          resourceId: resource.resourceId,
          resourceTitle: resource.resourceTitle,
          careerPaths: resource.careerPaths,
          url: resource.url,
          author: resource.author,
          category: resource.category,
          errorStatus: resource.errorStatus,
          reason: '404 Not Found',
          deletedAt: new Date().toISOString(),
          status: 'FAILED',
          error: error.message
        });
      }

      console.log('');
    }

    // Save deletion log
    const outputPath = 'docs/cleanup/404-deletion-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(deletionLog, null, 2));

    // Summary
    const deleted = deletionLog.filter(log => log.status === 'DELETED').length;
    const markedInactive = deletionLog.filter(log => log.status === 'MARKED_INACTIVE').length;
    const alreadyDeleted = deletionLog.filter(log => log.status === 'ALREADY_DELETED').length;
    const failed = deletionLog.filter(log => log.status === 'FAILED').length;

    console.log(`📊 404 RESOURCE REMOVAL SUMMARY:`);
    console.log(`Total processed: ${deletionLog.length}`);
    console.log(`Successfully deleted: ${deleted}`);
    console.log(`Marked inactive: ${markedInactive}`);
    console.log(`Already deleted: ${alreadyDeleted}`);
    console.log(`Failed operations: ${failed}`);

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      deletionLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle}: ${log.error}`);
      });
    }

    console.log(`\n💾 Deletion log saved to: ${outputPath}`);

    return deletionLog;

  } catch (error) {
    console.error('❌ Error removing 404 resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  remove404Resources();
}

module.exports = { remove404Resources };
