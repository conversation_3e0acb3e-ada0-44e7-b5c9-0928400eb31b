const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function performanceTest() {
  console.log('🚀 PERFORMANCE TESTING WITH CLEANED DATA');
  console.log('========================================\n');

  const performanceResults = {
    timestamp: new Date().toISOString(),
    databaseQueries: {},
    apiSimulation: {},
    resourceCounts: {},
    summary: {
      overallStatus: 'PENDING',
      recommendations: []
    }
  };

  try {
    // 1. Test Database Query Performance
    console.log('📊 Testing Database Query Performance...\n');

    // Test career paths query
    const careerPathsStart = Date.now();
    const careerPaths = await prisma.careerPath.findMany({
      include: {
        learningResources: {
          where: { isActive: true },
          select: {
            id: true,
            title: true,
            url: true,
            category: true,
            skillLevel: true,
            cost: true
          }
        }
      }
    });
    const careerPathsTime = Date.now() - careerPathsStart;

    performanceResults.databaseQueries.careerPathsWithResources = {
      queryTime: careerPathsTime,
      recordCount: careerPaths.length,
      totalResources: careerPaths.reduce((sum, path) => sum + path.learningResources.length, 0),
      status: careerPathsTime < 1000 ? 'EXCELLENT' : careerPathsTime < 2000 ? 'GOOD' : 'NEEDS_IMPROVEMENT'
    };

    console.log(`✅ Career Paths Query: ${careerPathsTime}ms (${careerPaths.length} paths, ${performanceResults.databaseQueries.careerPathsWithResources.totalResources} resources)`);

    // Test individual career path query (simulating page load)
    const singlePathStart = Date.now();
    const singlePath = await prisma.careerPath.findFirst({
      where: { name: 'Cloud Solutions Architect' },
      include: {
        learningResources: {
          where: { isActive: true }
        }
      }
    });
    const singlePathTime = Date.now() - singlePathStart;

    performanceResults.databaseQueries.singleCareerPath = {
      queryTime: singlePathTime,
      resourceCount: singlePath ? singlePath.learningResources.length : 0,
      status: singlePathTime < 500 ? 'EXCELLENT' : singlePathTime < 1000 ? 'GOOD' : 'NEEDS_IMPROVEMENT'
    };

    console.log(`✅ Single Career Path Query: ${singlePathTime}ms (${singlePath ? singlePath.learningResources.length : 0} resources)`);

    // Test learning resources query
    const resourcesStart = Date.now();
    const allResources = await prisma.learningResource.findMany({
      where: { isActive: true },
      include: {
        careerPaths: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });
    const resourcesTime = Date.now() - resourcesStart;

    performanceResults.databaseQueries.allLearningResources = {
      queryTime: resourcesTime,
      recordCount: allResources.length,
      status: resourcesTime < 1000 ? 'EXCELLENT' : resourcesTime < 2000 ? 'GOOD' : 'NEEDS_IMPROVEMENT'
    };

    console.log(`✅ All Learning Resources Query: ${resourcesTime}ms (${allResources.length} resources)`);

    // 2. Analyze Resource Distribution Performance Impact
    console.log('\n📈 Analyzing Resource Distribution...\n');

    const resourceDistribution = {};
    careerPaths.forEach(path => {
      resourceDistribution[path.name] = {
        resourceCount: path.learningResources.length,
        categories: {},
        skillLevels: {}
      };

      path.learningResources.forEach(resource => {
        // Count by category
        resourceDistribution[path.name].categories[resource.category] = 
          (resourceDistribution[path.name].categories[resource.category] || 0) + 1;
        
        // Count by skill level
        resourceDistribution[path.name].skillLevels[resource.skillLevel] = 
          (resourceDistribution[path.name].skillLevels[resource.skillLevel] || 0) + 1;
      });
    });

    performanceResults.resourceCounts = resourceDistribution;

    // Display distribution
    Object.entries(resourceDistribution).forEach(([pathName, data]) => {
      const status = data.resourceCount >= 5 ? '✅' : data.resourceCount >= 3 ? '⚠️' : '❌';
      console.log(`${status} ${pathName}: ${data.resourceCount} resources`);
    });

    // 3. Simulate API Response Times
    console.log('\n🌐 Simulating API Response Performance...\n');

    // Simulate career paths API
    const apiCareerPathsStart = Date.now();
    const apiCareerPaths = await prisma.careerPath.findMany({
      select: {
        id: true,
        name: true,
        overview: true,
        _count: {
          select: {
            learningResources: {
              where: { isActive: true }
            }
          }
        }
      }
    });
    const apiCareerPathsTime = Date.now() - apiCareerPathsStart;

    performanceResults.apiSimulation.careerPathsList = {
      responseTime: apiCareerPathsTime,
      recordCount: apiCareerPaths.length,
      status: apiCareerPathsTime < 500 ? 'EXCELLENT' : apiCareerPathsTime < 1000 ? 'GOOD' : 'NEEDS_IMPROVEMENT'
    };

    console.log(`✅ Career Paths API Simulation: ${apiCareerPathsTime}ms`);

    // Simulate resources API with filtering
    const apiResourcesStart = Date.now();
    const apiResources = await prisma.learningResource.findMany({
      where: {
        isActive: true,
        category: 'DEVOPS'
      },
      select: {
        id: true,
        title: true,
        description: true,
        url: true,
        category: true,
        skillLevel: true,
        cost: true,
        author: true
      },
      take: 20
    });
    const apiResourcesTime = Date.now() - apiResourcesStart;

    performanceResults.apiSimulation.filteredResources = {
      responseTime: apiResourcesTime,
      recordCount: apiResources.length,
      status: apiResourcesTime < 300 ? 'EXCELLENT' : apiResourcesTime < 600 ? 'GOOD' : 'NEEDS_IMPROVEMENT'
    };

    console.log(`✅ Filtered Resources API Simulation: ${apiResourcesTime}ms (${apiResources.length} DevOps resources)`);

    // 4. Test Complex Queries (User Dashboard Simulation)
    console.log('\n🎯 Testing Complex Queries...\n');

    const complexQueryStart = Date.now();
    const userDashboardData = await prisma.careerPath.findMany({
      where: {
        learningResources: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        learningResources: {
          where: { isActive: true },
          take: 3,
          orderBy: { createdAt: 'desc' }
        }
      },
      take: 5
    });
    const complexQueryTime = Date.now() - complexQueryStart;

    performanceResults.apiSimulation.complexDashboardQuery = {
      responseTime: complexQueryTime,
      pathsReturned: userDashboardData.length,
      totalResources: userDashboardData.reduce((sum, path) => sum + path.learningResources.length, 0),
      status: complexQueryTime < 800 ? 'EXCELLENT' : complexQueryTime < 1500 ? 'GOOD' : 'NEEDS_IMPROVEMENT'
    };

    console.log(`✅ Complex Dashboard Query: ${complexQueryTime}ms (${userDashboardData.length} paths, ${performanceResults.apiSimulation.complexDashboardQuery.totalResources} resources)`);

    // 5. Performance Analysis
    console.log('\n📊 Performance Analysis...\n');

    const allQueryTimes = [
      performanceResults.databaseQueries.careerPathsWithResources.queryTime,
      performanceResults.databaseQueries.singleCareerPath.queryTime,
      performanceResults.databaseQueries.allLearningResources.queryTime,
      performanceResults.apiSimulation.careerPathsList.responseTime,
      performanceResults.apiSimulation.filteredResources.responseTime,
      performanceResults.apiSimulation.complexDashboardQuery.responseTime
    ];

    const averageQueryTime = allQueryTimes.reduce((sum, time) => sum + time, 0) / allQueryTimes.length;
    const maxQueryTime = Math.max(...allQueryTimes);

    performanceResults.summary.averageQueryTime = averageQueryTime;
    performanceResults.summary.maxQueryTime = maxQueryTime;

    // Determine overall status
    if (averageQueryTime < 500 && maxQueryTime < 1000) {
      performanceResults.summary.overallStatus = 'EXCELLENT';
    } else if (averageQueryTime < 1000 && maxQueryTime < 2000) {
      performanceResults.summary.overallStatus = 'GOOD';
    } else {
      performanceResults.summary.overallStatus = 'NEEDS_IMPROVEMENT';
    }

    // Generate recommendations
    if (maxQueryTime > 2000) {
      performanceResults.summary.recommendations.push('Consider adding database indexes for slow queries');
    }
    if (performanceResults.databaseQueries.careerPathsWithResources.totalResources > 100) {
      performanceResults.summary.recommendations.push('Consider implementing pagination for large resource sets');
    }
    if (averageQueryTime > 800) {
      performanceResults.summary.recommendations.push('Consider implementing query caching for frequently accessed data');
    }

    // Check for empty career paths (performance impact)
    const emptyPaths = careerPaths.filter(path => path.learningResources.length === 0);
    if (emptyPaths.length > 0) {
      performanceResults.summary.recommendations.push(`${emptyPaths.length} career paths have no resources - consider adding resources or hiding empty paths`);
    }

    // 6. Display Results
    console.log('📈 PERFORMANCE SUMMARY:');
    console.log('======================');
    console.log(`Average Query Time: ${averageQueryTime.toFixed(2)}ms`);
    console.log(`Maximum Query Time: ${maxQueryTime}ms`);
    console.log(`Overall Status: ${performanceResults.summary.overallStatus}`);
    console.log(`Total Active Resources: ${allResources.length}`);
    console.log(`Total Career Paths: ${careerPaths.length}`);
    console.log(`Career Paths with Resources: ${careerPaths.filter(p => p.learningResources.length > 0).length}`);

    if (performanceResults.summary.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      performanceResults.summary.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    } else {
      console.log('\n🎉 No performance recommendations - System is performing optimally!');
    }

    // Save results
    const fs = require('fs');
    const outputPath = 'docs/cleanup/performance-test-results.json';
    fs.writeFileSync(outputPath, JSON.stringify(performanceResults, null, 2));

    console.log(`\n💾 Performance results saved to: ${outputPath}`);

    return performanceResults;

  } catch (error) {
    console.error('❌ Error during performance testing:', error);
    performanceResults.summary.overallStatus = 'ERROR';
    performanceResults.error = error.message;
    return performanceResults;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  performanceTest();
}

module.exports = { performanceTest };
