const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addProductManagerResources() {
  console.log('📋 ADDING HIGH-QUALITY PRODUCT MANAGER RESOURCES');
  console.log('===============================================\n');

  try {
    // Get Product Manager career path
    const productManagerPath = await prisma.careerPath.findFirst({
      where: { name: 'Product Manager' },
      include: {
        learningResources: true
      }
    });

    if (!productManagerPath) {
      console.log('❌ Product Manager career path not found');
      return;
    }

    console.log(`📊 Product Manager path currently has ${productManagerPath.learningResources.length} resources\n`);

    // Define high-quality Product Management resources to add
    const productManagerResources = [
      {
        title: 'Google Product Management Certificate',
        description: 'Comprehensive product management program covering strategy, user research, design thinking, and data analysis',
        url: 'https://www.coursera.org/professional-certificates/google-product-management',
        author: 'Google',
        category: 'PRODUCT_MANAGEMENT',
        type: 'CERTIFICATION',
        skillLevel: 'BEGINNER',
        cost: 'SUBSCRIPTION',
        format: 'INSTRUCTOR_LED',
        isActive: true
      },
      {
        title: 'Product Management Fundamentals',
        description: 'Essential product management concepts including product strategy, roadmapping, and stakeholder management',
        url: 'https://www.edx.org/course/product-management',
        author: 'University of Virginia',
        category: 'PRODUCT_MANAGEMENT',
        type: 'COURSE',
        skillLevel: 'BEGINNER',
        cost: 'FREE',
        format: 'INSTRUCTOR_LED',
        isActive: true
      },
      {
        title: 'Agile Product Management with Scrum',
        description: 'Learn agile methodologies and Scrum framework for effective product development and team collaboration',
        url: 'https://www.scrum.org/resources/what-is-a-product-owner',
        author: 'Scrum.org',
        category: 'PRODUCT_MANAGEMENT',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'User Research and Customer Discovery',
        description: 'Comprehensive guide to conducting user research, interviews, and customer discovery for product validation',
        url: 'https://www.nngroup.com/articles/which-ux-research-methods/',
        author: 'Nielsen Norman Group',
        category: 'UX_UI_DESIGN',
        type: 'ARTICLE',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'THEORETICAL',
        isActive: true
      },
      {
        title: 'Product Analytics and Metrics',
        description: 'Learn to define, measure, and analyze key product metrics including KPIs, cohort analysis, and A/B testing',
        url: 'https://amplitude.com/blog/product-analytics',
        author: 'Amplitude',
        category: 'DATA_SCIENCE',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'Product Strategy and Roadmapping',
        description: 'Strategic product planning including market analysis, competitive research, and roadmap development',
        url: 'https://www.productplan.com/learn/product-strategy/',
        author: 'ProductPlan',
        category: 'PRODUCT_MANAGEMENT',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'Product-Led Growth Strategies',
        description: 'Learn product-led growth principles, user onboarding, and retention strategies for sustainable growth',
        url: 'https://www.productled.org/foundations',
        author: 'Product-Led Institute',
        category: 'PRODUCT_MANAGEMENT',
        type: 'COURSE',
        skillLevel: 'ADVANCED',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'Technical Product Management',
        description: 'Bridge the gap between technical and business teams with technical product management skills',
        url: 'https://www.coursera.org/learn/technical-product-management',
        author: 'University of Alberta',
        category: 'PRODUCT_MANAGEMENT',
        type: 'COURSE',
        skillLevel: 'ADVANCED',
        cost: 'FREE',
        format: 'INSTRUCTOR_LED',
        isActive: true
      },
      {
        title: 'Product Design and UX Principles',
        description: 'Essential design thinking and UX principles for product managers to collaborate effectively with design teams',
        url: 'https://www.interaction-design.org/literature/topics/product-design',
        author: 'Interaction Design Foundation',
        category: 'UX_UI_DESIGN',
        type: 'TUTORIAL',
        skillLevel: 'BEGINNER',
        cost: 'FREEMIUM',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'Product Marketing and Go-to-Market',
        description: 'Learn product positioning, messaging, and go-to-market strategies for successful product launches',
        url: 'https://blog.hubspot.com/marketing/go-to-market-strategy',
        author: 'HubSpot',
        category: 'DIGITAL_MARKETING',
        type: 'ARTICLE',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'THEORETICAL',
        isActive: true
      }
    ];

    const additionLog = [];

    for (const resourceData of productManagerResources) {
      console.log(`➕ Adding: ${resourceData.title}`);
      console.log(`   Author: ${resourceData.author}`);
      console.log(`   URL: ${resourceData.url}`);
      console.log(`   Skill Level: ${resourceData.skillLevel}`);
      console.log(`   Category: ${resourceData.category}`);

      try {
        // Check if resource already exists
        const existingResource = await prisma.learningResource.findFirst({
          where: {
            OR: [
              { title: resourceData.title },
              { url: resourceData.url }
            ]
          }
        });

        if (existingResource) {
          console.log(`   ⚠️ Resource already exists, connecting to Product Manager path`);
          
          // Connect existing resource to Product Manager path
          await prisma.careerPath.update({
            where: { id: productManagerPath.id },
            data: {
              learningResources: {
                connect: { id: existingResource.id }
              }
            }
          });

          additionLog.push({
            resourceId: existingResource.id,
            resourceTitle: resourceData.title,
            author: resourceData.author,
            url: resourceData.url,
            category: resourceData.category,
            skillLevel: resourceData.skillLevel,
            cost: resourceData.cost,
            careerPath: 'Product Manager',
            action: 'CONNECTED_EXISTING',
            addedAt: new Date().toISOString(),
            status: 'SUCCESS'
          });

        } else {
          // Create new resource
          const newResource = await prisma.learningResource.create({
            data: {
              ...resourceData,
              careerPaths: {
                connect: { id: productManagerPath.id }
              }
            }
          });

          console.log(`   ✅ Created and connected new resource (ID: ${newResource.id})`);

          additionLog.push({
            resourceId: newResource.id,
            resourceTitle: resourceData.title,
            author: resourceData.author,
            url: resourceData.url,
            category: resourceData.category,
            skillLevel: resourceData.skillLevel,
            cost: resourceData.cost,
            careerPath: 'Product Manager',
            action: 'CREATED_NEW',
            addedAt: new Date().toISOString(),
            status: 'SUCCESS'
          });
        }

      } catch (error) {
        console.log(`   ❌ Failed to add: ${error.message}`);
        
        additionLog.push({
          resourceId: null,
          resourceTitle: resourceData.title,
          author: resourceData.author,
          url: resourceData.url,
          category: resourceData.category,
          skillLevel: resourceData.skillLevel,
          cost: resourceData.cost,
          careerPath: 'Product Manager',
          action: 'FAILED',
          addedAt: new Date().toISOString(),
          status: 'FAILED',
          error: error.message
        });
      }

      console.log('');
    }

    // Check final state
    const updatedProductManagerPath = await prisma.careerPath.findFirst({
      where: { name: 'Product Manager' },
      include: {
        learningResources: {
          select: {
            id: true,
            title: true,
            author: true,
            skillLevel: true,
            cost: true,
            category: true
          }
        }
      }
    });

    console.log(`📊 Product Manager path now has ${updatedProductManagerPath.learningResources.length} resources\n`);
    
    console.log('📋 Product Manager resources:');
    updatedProductManagerPath.learningResources.forEach(resource => {
      console.log(`   • ${resource.title} (${resource.skillLevel}) - ${resource.author}`);
    });

    // Save addition log
    const fs = require('fs');
    const outputPath = 'docs/cleanup/product-manager-resources-addition-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(additionLog, null, 2));

    // Summary
    const created = additionLog.filter(log => log.status === 'SUCCESS' && log.action === 'CREATED_NEW').length;
    const connected = additionLog.filter(log => log.status === 'SUCCESS' && log.action === 'CONNECTED_EXISTING').length;
    const failed = additionLog.filter(log => log.status === 'FAILED').length;

    console.log(`\n📊 PRODUCT MANAGER RESOURCES ADDITION SUMMARY:`);
    console.log(`Total resources processed: ${additionLog.length}`);
    console.log(`New resources created: ${created}`);
    console.log(`Existing resources connected: ${connected}`);
    console.log(`Failed operations: ${failed}`);
    console.log(`Resources before: ${productManagerPath.learningResources.length}`);
    console.log(`Resources after: ${updatedProductManagerPath.learningResources.length}`);

    // Summary by skill level
    console.log('\n📈 Resources by Skill Level:');
    const bySkillLevel = {};
    updatedProductManagerPath.learningResources.forEach(resource => {
      bySkillLevel[resource.skillLevel] = (bySkillLevel[resource.skillLevel] || 0) + 1;
    });
    
    Object.entries(bySkillLevel).forEach(([level, count]) => {
      console.log(`   ${level}: ${count} resources`);
    });

    // Summary by category
    console.log('\n📂 Resources by Category:');
    const byCategory = {};
    updatedProductManagerPath.learningResources.forEach(resource => {
      byCategory[resource.category] = (byCategory[resource.category] || 0) + 1;
    });
    
    Object.entries(byCategory).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} resources`);
    });

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      additionLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle}: ${log.error}`);
      });
    }

    console.log(`\n💾 Addition log saved to: ${outputPath}`);

    return additionLog;

  } catch (error) {
    console.error('❌ Error adding Product Manager resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  addProductManagerResources();
}

module.exports = { addProductManagerResources };
