const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function removeTimeoutResources() {
  console.log('🗑️ REMOVING RESOURCES WITH TIMEOUT/CONNECTION ERRORS');
  console.log('===================================================\n');

  try {
    // Load broken URLs data from analysis
    const fs = require('fs');
    const brokenUrls = JSON.parse(fs.readFileSync('docs/cleanup/broken-urls-inventory.json', 'utf8'));

    // Filter for timeout errors only
    const resourcesTimeout = brokenUrls.filter(resource => resource.errorStatus === 'TIMEOUT');

    console.log(`📊 Found ${resourcesTimeout.length} resources with timeout errors to remove\n`);

    const deletionLog = [];

    for (const resource of resourcesTimeout) {
      console.log(`🗑️ Analyzing: ${resource.resourceTitle}`);
      console.log(`   ID: ${resource.resourceId}`);
      console.log(`   URL: ${resource.url}`);
      console.log(`   Career Paths: ${resource.careerPaths}`);
      console.log(`   Author: ${resource.author}`);

      // Analyze if this is likely a consistent timeout or temporary issue
      const timeoutAnalysis = analyzeTimeoutError(resource.url, resource.author);
      console.log(`   Timeout Analysis: ${timeoutAnalysis.type} - ${timeoutAnalysis.reason}`);

      try {
        // Check if resource still exists (might have been deleted in previous steps)
        const existingResource = await prisma.learningResource.findUnique({
          where: { id: resource.resourceId }
        });

        if (!existingResource) {
          console.log(`   ⚠️ Resource already deleted, skipping`);
          
          deletionLog.push({
            resourceId: resource.resourceId,
            resourceTitle: resource.resourceTitle,
            careerPaths: resource.careerPaths,
            url: resource.url,
            author: resource.author,
            category: resource.category,
            errorStatus: resource.errorStatus,
            timeoutAnalysis: timeoutAnalysis,
            reason: 'Timeout/Connection Error',
            deletedAt: new Date().toISOString(),
            status: 'ALREADY_DELETED'
          });
          
          console.log('');
          continue;
        }

        // Check for user progress records that would prevent deletion
        const userProgress = await prisma.userLearningProgress.findMany({
          where: { resourceId: resource.resourceId }
        });

        if (userProgress.length > 0) {
          console.log(`   ⚠️ Resource has ${userProgress.length} user progress records, marking as inactive instead`);
          
          // Mark as inactive instead of deleting
          await prisma.learningResource.update({
            where: { id: resource.resourceId },
            data: { isActive: false }
          });

          deletionLog.push({
            resourceId: resource.resourceId,
            resourceTitle: resource.resourceTitle,
            careerPaths: resource.careerPaths,
            url: resource.url,
            author: resource.author,
            category: resource.category,
            errorStatus: resource.errorStatus,
            timeoutAnalysis: timeoutAnalysis,
            reason: 'Timeout/Connection Error - marked inactive due to user progress',
            deletedAt: new Date().toISOString(),
            status: 'MARKED_INACTIVE',
            userProgressCount: userProgress.length
          });

        } else if (timeoutAnalysis.type === 'CONSISTENT_FAILURE') {
          // Safe to delete - consistent failure and no user progress
          await prisma.learningResource.delete({
            where: { id: resource.resourceId }
          });

          console.log(`   ✅ Successfully deleted (consistent failure)`);

          deletionLog.push({
            resourceId: resource.resourceId,
            resourceTitle: resource.resourceTitle,
            careerPaths: resource.careerPaths,
            url: resource.url,
            author: resource.author,
            category: resource.category,
            errorStatus: resource.errorStatus,
            timeoutAnalysis: timeoutAnalysis,
            reason: 'Timeout/Connection Error - consistent failure',
            deletedAt: new Date().toISOString(),
            status: 'DELETED'
          });

        } else {
          // Temporary issue - mark as inactive for review
          await prisma.learningResource.update({
            where: { id: resource.resourceId },
            data: { isActive: false }
          });

          console.log(`   ⚠️ Marked as inactive (temporary issue - needs review)`);

          deletionLog.push({
            resourceId: resource.resourceId,
            resourceTitle: resource.resourceTitle,
            careerPaths: resource.careerPaths,
            url: resource.url,
            author: resource.author,
            category: resource.category,
            errorStatus: resource.errorStatus,
            timeoutAnalysis: timeoutAnalysis,
            reason: 'Timeout/Connection Error - temporary issue, marked inactive for review',
            deletedAt: new Date().toISOString(),
            status: 'MARKED_INACTIVE_REVIEW'
          });
        }

      } catch (error) {
        console.log(`   ❌ Failed to process: ${error.message}`);
        
        deletionLog.push({
          resourceId: resource.resourceId,
          resourceTitle: resource.resourceTitle,
          careerPaths: resource.careerPaths,
          url: resource.url,
          author: resource.author,
          category: resource.category,
          errorStatus: resource.errorStatus,
          timeoutAnalysis: timeoutAnalysis,
          reason: 'Timeout/Connection Error',
          deletedAt: new Date().toISOString(),
          status: 'FAILED',
          error: error.message
        });
      }

      console.log('');
    }

    // Save deletion log
    const outputPath = 'docs/cleanup/timeout-deletion-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(deletionLog, null, 2));

    // Summary
    const deleted = deletionLog.filter(log => log.status === 'DELETED').length;
    const markedInactive = deletionLog.filter(log => log.status === 'MARKED_INACTIVE').length;
    const markedInactiveReview = deletionLog.filter(log => log.status === 'MARKED_INACTIVE_REVIEW').length;
    const alreadyDeleted = deletionLog.filter(log => log.status === 'ALREADY_DELETED').length;
    const failed = deletionLog.filter(log => log.status === 'FAILED').length;

    console.log(`📊 TIMEOUT RESOURCE REMOVAL SUMMARY:`);
    console.log(`Total processed: ${deletionLog.length}`);
    console.log(`Successfully deleted: ${deleted}`);
    console.log(`Marked inactive (user progress): ${markedInactive}`);
    console.log(`Marked inactive (review needed): ${markedInactiveReview}`);
    console.log(`Already deleted: ${alreadyDeleted}`);
    console.log(`Failed operations: ${failed}`);

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      deletionLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle}: ${log.error}`);
      });
    }

    console.log(`\n💾 Deletion log saved to: ${outputPath}`);

    return deletionLog;

  } catch (error) {
    console.error('❌ Error removing timeout resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function analyzeTimeoutError(url, author) {
  // Analyze the URL and author to determine if this is likely a consistent failure or temporary issue
  
  // Adobe XD Tutorials - known to have issues
  if (url.includes('helpx.adobe.com/xd/tutorials.html')) {
    return {
      type: 'CONSISTENT_FAILURE',
      reason: 'Adobe XD Tutorials page has been consistently timing out'
    };
  }
  
  // Example.com URLs are clearly placeholder/test data
  if (url.includes('example.com')) {
    return {
      type: 'CONSISTENT_FAILURE',
      reason: 'Example.com is a placeholder domain that will never work'
    };
  }
  
  // Adobe sites might have temporary issues
  if (url.includes('adobe.com')) {
    return {
      type: 'TEMPORARY_ISSUE',
      reason: 'Adobe sites may have temporary connectivity issues'
    };
  }
  
  // Default to temporary for review
  return {
    type: 'TEMPORARY_ISSUE',
    reason: 'Unknown timeout cause - needs manual review'
  };
}

if (require.main === module) {
  removeTimeoutResources();
}

module.exports = { removeTimeoutResources };
