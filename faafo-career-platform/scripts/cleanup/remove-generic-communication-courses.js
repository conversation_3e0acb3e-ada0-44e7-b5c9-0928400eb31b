const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function removeGenericCommunicationCourses() {
  console.log('🗣️ REMOVING GENERIC COMMUNICATION COURSES FROM TECHNICAL PATHS');
  console.log('==============================================================\n');

  try {
    // Define technical career paths that should not have generic communication courses
    const technicalPaths = [
      'AI/Machine Learning Engineer',
      'Data Scientist', 
      'Cybersecurity Specialist'
    ];

    // Define generic communication courses to remove
    const genericCommunicationCourses = [
      'Business English for International Careers',
      'Technical Communication Skills',
      'Cross-Cultural Communication',
      'Professional Presentation Skills'
    ];

    const removalLog = [];

    for (const pathName of technicalPaths) {
      console.log(`🔧 Processing: ${pathName}`);
      
      // Get the career path with its resources
      const careerPath = await prisma.careerPath.findFirst({
        where: { name: pathName },
        include: {
          learningResources: {
            select: {
              id: true,
              title: true,
              category: true,
              url: true,
              author: true
            }
          }
        }
      });

      if (!careerPath) {
        console.log(`   ❌ Career path not found: ${pathName}\n`);
        continue;
      }

      console.log(`   📊 Currently has ${careerPath.learningResources.length} resources`);

      let removedCount = 0;

      for (const courseTitle of genericCommunicationCourses) {
        console.log(`   🔍 Looking for: ${courseTitle}`);
        
        // Find the resource in this career path
        const resource = careerPath.learningResources.find(r => r.title === courseTitle);
        
        if (resource) {
          console.log(`     Found: ${resource.title} (${resource.category})`);

          try {
            // Remove the connection between this resource and the career path
            await prisma.careerPath.update({
              where: { id: careerPath.id },
              data: {
                learningResources: {
                  disconnect: { id: resource.id }
                }
              }
            });

            console.log(`     ✅ Disconnected from ${pathName}`);
            removedCount++;

            removalLog.push({
              resourceId: resource.id,
              resourceTitle: resource.title,
              category: resource.category,
              author: resource.author,
              url: resource.url,
              careerPath: pathName,
              action: 'DISCONNECTED',
              reason: 'Generic communication course not core to technical role',
              removedAt: new Date().toISOString(),
              status: 'SUCCESS'
            });

          } catch (error) {
            console.log(`     ❌ Failed to disconnect: ${error.message}`);
            
            removalLog.push({
              resourceId: resource.id,
              resourceTitle: resource.title,
              category: resource.category,
              author: resource.author,
              url: resource.url,
              careerPath: pathName,
              action: 'DISCONNECTED',
              reason: 'Generic communication course not core to technical role',
              removedAt: new Date().toISOString(),
              status: 'FAILED',
              error: error.message
            });
          }
        } else {
          console.log(`     ⚠️ Not found (may have been already removed)`);
        }
      }

      // Check final state for this path
      const updatedPath = await prisma.careerPath.findFirst({
        where: { name: pathName },
        include: {
          learningResources: {
            select: {
              id: true,
              title: true,
              category: true
            }
          }
        }
      });

      console.log(`   📊 Now has ${updatedPath.learningResources.length} resources (removed ${removedCount})`);
      
      if (updatedPath.learningResources.length > 0) {
        console.log(`   📚 Remaining resources:`);
        updatedPath.learningResources.forEach(resource => {
          console.log(`     • ${resource.title} (${resource.category})`);
        });
      }
      
      console.log('');
    }

    // Save removal log
    const fs = require('fs');
    const outputPath = 'docs/cleanup/communication-courses-removal-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(removalLog, null, 2));

    // Summary
    const successful = removalLog.filter(log => log.status === 'SUCCESS').length;
    const failed = removalLog.filter(log => log.status === 'FAILED').length;

    console.log(`📊 GENERIC COMMUNICATION COURSES REMOVAL SUMMARY:`);
    console.log(`Total processed: ${removalLog.length}`);
    console.log(`Successfully disconnected: ${successful}`);
    console.log(`Failed operations: ${failed}`);

    // Summary by career path
    console.log('\n📈 Removals by Career Path:');
    const byPath = {};
    removalLog.filter(log => log.status === 'SUCCESS').forEach(log => {
      byPath[log.careerPath] = (byPath[log.careerPath] || 0) + 1;
    });
    
    Object.entries(byPath).forEach(([path, count]) => {
      console.log(`   ${path}: ${count} courses removed`);
    });

    // Summary by course type
    console.log('\n📚 Courses Removed:');
    const byCourse = {};
    removalLog.filter(log => log.status === 'SUCCESS').forEach(log => {
      byCourse[log.resourceTitle] = (byCourse[log.resourceTitle] || 0) + 1;
    });
    
    Object.entries(byCourse).forEach(([course, count]) => {
      console.log(`   ${course}: removed from ${count} path(s)`);
    });

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      removalLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle} from ${log.careerPath}: ${log.error}`);
      });
    }

    console.log(`\n💾 Removal log saved to: ${outputPath}`);

    return removalLog;

  } catch (error) {
    console.error('❌ Error removing generic communication courses:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  removeGenericCommunicationCourses();
}

module.exports = { removeGenericCommunicationCourses };
