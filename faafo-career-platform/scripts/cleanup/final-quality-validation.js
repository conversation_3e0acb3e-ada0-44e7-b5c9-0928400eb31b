const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function finalQualityValidation() {
  console.log('🔍 FINAL QUALITY ASSURANCE & VALIDATION');
  console.log('======================================\n');

  try {
    const validationResults = {
      timestamp: new Date().toISOString(),
      overallStatus: 'PENDING',
      metrics: {},
      careerPathAnalysis: {},
      qualityChecks: {},
      recommendations: []
    };

    // 1. Get comprehensive database state
    console.log('📊 Analyzing current database state...\n');

    const totalResources = await prisma.learningResource.count({
      where: { isActive: true }
    });

    const careerPaths = await prisma.careerPath.findMany({
      include: {
        learningResources: {
          where: { isActive: true },
          select: {
            id: true,
            title: true,
            category: true,
            skillLevel: true,
            cost: true,
            author: true,
            url: true
          }
        }
      },
      orderBy: { name: 'asc' }
    });

    // 2. Career Path Coverage Analysis
    console.log('🛤️ Career Path Coverage Analysis:');
    console.log('=================================');

    let totalConnectedResources = 0;
    let emptyPaths = 0;
    let wellPopulatedPaths = 0;

    for (const path of careerPaths) {
      const resourceCount = path.learningResources.length;
      totalConnectedResources += resourceCount;

      console.log(`${path.name}: ${resourceCount} resources`);

      if (resourceCount === 0) {
        emptyPaths++;
        console.log(`   ❌ EMPTY - Needs immediate attention`);
      } else if (resourceCount >= 5) {
        wellPopulatedPaths++;
        console.log(`   ✅ Well populated`);
      } else {
        console.log(`   ⚠️ Needs more resources`);
      }

      // Analyze skill level distribution
      const skillLevels = {};
      path.learningResources.forEach(resource => {
        skillLevels[resource.skillLevel] = (skillLevels[resource.skillLevel] || 0) + 1;
      });

      if (Object.keys(skillLevels).length > 0) {
        console.log(`   Skill levels: ${Object.entries(skillLevels).map(([level, count]) => `${level}(${count})`).join(', ')}`);
      }

      validationResults.careerPathAnalysis[path.name] = {
        resourceCount,
        skillLevelDistribution: skillLevels,
        status: resourceCount === 0 ? 'EMPTY' : resourceCount >= 5 ? 'WELL_POPULATED' : 'NEEDS_MORE'
      };

      console.log('');
    }

    // 3. Quality Metrics Calculation
    console.log('📈 Quality Metrics:');
    console.log('==================');

    const freeResources = await prisma.learningResource.count({
      where: { isActive: true, cost: 'FREE' }
    });

    const authoritySourcesCount = await prisma.learningResource.count({
      where: {
        isActive: true,
        author: {
          in: [
            'Amazon Web Services', 'Google', 'Microsoft', 'Meta', 'Apple Developer',
            'IBM', 'Oracle', 'HashiCorp', 'Docker Inc.', 'Kubernetes Community',
            'Linux Foundation', 'Mozilla', 'GitHub', 'GitLab', 'Jenkins Community',
            'Harvard University', 'MIT', 'Stanford University', 'UC Berkeley',
            'Coursera', 'edX', 'Khan Academy', 'freeCodeCamp'
          ]
        }
      }
    });

    const skillLevelDistribution = await prisma.learningResource.groupBy({
      by: ['skillLevel'],
      where: { isActive: true },
      _count: { skillLevel: true }
    });

    const categoryDistribution = await prisma.learningResource.groupBy({
      by: ['category'],
      where: { isActive: true },
      _count: { category: true }
    });

    validationResults.metrics = {
      totalResources,
      totalConnectedResources,
      emptyCareerPaths: emptyPaths,
      wellPopulatedPaths,
      freeResourcesCount: freeResources,
      freeResourcesPercentage: ((freeResources / totalResources) * 100).toFixed(1),
      authoritySourcesCount,
      authoritySourcesPercentage: ((authoritySourcesCount / totalResources) * 100).toFixed(1),
      skillLevelDistribution: skillLevelDistribution.reduce((acc, item) => {
        acc[item.skillLevel] = item._count.skillLevel;
        return acc;
      }, {}),
      categoryDistribution: categoryDistribution.reduce((acc, item) => {
        acc[item.category] = item._count.category;
        return acc;
      }, {})
    };

    console.log(`Total Resources: ${totalResources}`);
    console.log(`Connected Resources: ${totalConnectedResources}`);
    console.log(`Empty Career Paths: ${emptyPaths}/10`);
    console.log(`Well Populated Paths: ${wellPopulatedPaths}/10`);
    console.log(`Free Resources: ${freeResources} (${validationResults.metrics.freeResourcesPercentage}%)`);
    console.log(`Authority Sources: ${authoritySourcesCount} (${validationResults.metrics.authoritySourcesPercentage}%)`);

    // 4. Quality Checks
    console.log('\n🔍 Quality Checks:');
    console.log('==================');

    const qualityChecks = {
      noEmptyCareerPaths: emptyPaths === 0,
      highFreeResourcePercentage: (freeResources / totalResources) >= 0.75,
      goodAuthoritySourcePercentage: (authoritySourcesCount / totalResources) >= 0.20,
      balancedSkillLevels: true, // Will calculate below
      comprehensiveCoverage: wellPopulatedPaths >= 8
    };

    // Check skill level balance
    const beginnerCount = validationResults.metrics.skillLevelDistribution.BEGINNER || 0;
    const intermediateCount = validationResults.metrics.skillLevelDistribution.INTERMEDIATE || 0;
    const advancedCount = validationResults.metrics.skillLevelDistribution.ADVANCED || 0;

    const beginnerPercentage = (beginnerCount / totalResources) * 100;
    const intermediatePercentage = (intermediateCount / totalResources) * 100;
    const advancedPercentage = (advancedCount / totalResources) * 100;

    qualityChecks.balancedSkillLevels = beginnerPercentage >= 40 && beginnerPercentage <= 60 &&
                                       intermediatePercentage >= 30 && intermediatePercentage <= 50 &&
                                       advancedPercentage >= 5 && advancedPercentage <= 20;

    validationResults.qualityChecks = qualityChecks;

    console.log(`✅ No Empty Career Paths: ${qualityChecks.noEmptyCareerPaths ? 'PASS' : 'FAIL'}`);
    console.log(`✅ High Free Resource %: ${qualityChecks.highFreeResourcePercentage ? 'PASS' : 'FAIL'} (${validationResults.metrics.freeResourcesPercentage}%)`);
    console.log(`✅ Good Authority Sources: ${qualityChecks.goodAuthoritySourcePercentage ? 'PASS' : 'FAIL'} (${validationResults.metrics.authoritySourcesPercentage}%)`);
    console.log(`✅ Balanced Skill Levels: ${qualityChecks.balancedSkillLevels ? 'PASS' : 'FAIL'}`);
    console.log(`   - Beginner: ${beginnerPercentage.toFixed(1)}%`);
    console.log(`   - Intermediate: ${intermediatePercentage.toFixed(1)}%`);
    console.log(`   - Advanced: ${advancedPercentage.toFixed(1)}%`);
    console.log(`✅ Comprehensive Coverage: ${qualityChecks.comprehensiveCoverage ? 'PASS' : 'FAIL'} (${wellPopulatedPaths}/10 well populated)`);

    // 5. Calculate Overall Quality Score
    const qualityScore = calculateQualityScore(validationResults.metrics, qualityChecks);
    validationResults.metrics.qualityScore = qualityScore;

    console.log(`\n🏆 OVERALL QUALITY SCORE: ${qualityScore}/100`);

    // 6. Generate Recommendations
    const recommendations = generateRecommendations(validationResults.metrics, qualityChecks, validationResults.careerPathAnalysis);
    validationResults.recommendations = recommendations;

    console.log('\n💡 Recommendations:');
    console.log('==================');
    if (recommendations.length === 0) {
      console.log('🎉 No recommendations - Platform is in excellent condition!');
    } else {
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec.priority}: ${rec.description}`);
      });
    }

    // 7. Determine Overall Status
    if (qualityScore >= 90) {
      validationResults.overallStatus = 'EXCELLENT';
    } else if (qualityScore >= 75) {
      validationResults.overallStatus = 'GOOD';
    } else if (qualityScore >= 60) {
      validationResults.overallStatus = 'NEEDS_IMPROVEMENT';
    } else {
      validationResults.overallStatus = 'POOR';
    }

    console.log(`\n🎯 OVERALL STATUS: ${validationResults.overallStatus}`);

    // Save validation results
    const fs = require('fs');
    const outputPath = 'docs/cleanup/final-quality-validation.json';
    fs.writeFileSync(outputPath, JSON.stringify(validationResults, null, 2));

    console.log(`\n💾 Validation results saved to: ${outputPath}`);

    return validationResults;

  } catch (error) {
    console.error('❌ Error during quality validation:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function calculateQualityScore(metrics, checks) {
  let score = 0;

  // Base score from metrics
  score += Math.min(metrics.freeResourcesPercentage * 0.3, 30); // Max 30 points for free resources
  score += Math.min(metrics.authoritySourcesPercentage * 1.5, 30); // Max 30 points for authority sources
  
  // Bonus points for quality checks
  if (checks.noEmptyCareerPaths) score += 15;
  if (checks.balancedSkillLevels) score += 10;
  if (checks.comprehensiveCoverage) score += 10;
  if (checks.highFreeResourcePercentage) score += 5;

  return Math.round(Math.min(score, 100));
}

function generateRecommendations(metrics, checks, careerPathAnalysis) {
  const recommendations = [];

  if (!checks.noEmptyCareerPaths) {
    recommendations.push({
      priority: 'HIGH',
      description: 'Add resources to empty career paths to ensure complete coverage'
    });
  }

  if (!checks.goodAuthoritySourcePercentage) {
    recommendations.push({
      priority: 'MEDIUM',
      description: 'Increase percentage of resources from authoritative sources (target: 25%+)'
    });
  }

  if (!checks.balancedSkillLevels) {
    recommendations.push({
      priority: 'MEDIUM',
      description: 'Rebalance skill level distribution (target: 40-60% beginner, 30-50% intermediate, 5-20% advanced)'
    });
  }

  if (!checks.comprehensiveCoverage) {
    recommendations.push({
      priority: 'LOW',
      description: 'Add more resources to career paths with fewer than 5 resources'
    });
  }

  // Check for paths that need more resources
  const pathsNeedingResources = Object.entries(careerPathAnalysis)
    .filter(([name, analysis]) => analysis.status === 'NEEDS_MORE')
    .map(([name]) => name);

  if (pathsNeedingResources.length > 0) {
    recommendations.push({
      priority: 'LOW',
      description: `Add more resources to: ${pathsNeedingResources.join(', ')}`
    });
  }

  return recommendations;
}

if (require.main === module) {
  finalQualityValidation();
}

module.exports = { finalQualityValidation };
