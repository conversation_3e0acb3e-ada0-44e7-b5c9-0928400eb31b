const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addDevOpsResources() {
  console.log('🔧 ADDING HIGH-QUALITY DEVOPS RESOURCES');
  console.log('======================================\n');

  try {
    // Get DevOps Engineer career path
    const devOpsPath = await prisma.careerPath.findFirst({
      where: { name: '<PERSON>Ops Engineer' },
      include: {
        learningResources: true
      }
    });

    if (!devOpsPath) {
      console.log('❌ DevOps Engineer career path not found');
      return;
    }

    console.log(`📊 DevOps Engineer path currently has ${devOpsPath.learningResources.length} resources\n`);

    // Define high-quality DevOps resources to add
    const devOpsResources = [
      {
        title: 'Docker Official Documentation',
        description: 'Comprehensive guide to containerization with Docker, including best practices and real-world examples',
        url: 'https://docs.docker.com/',
        author: 'Docker Inc.',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'BEGINNER',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'Kubernetes Official Tutorials',
        description: 'Learn container orchestration with Kubernetes through hands-on tutorials and examples',
        url: 'https://kubernetes.io/docs/tutorials/',
        author: 'Kubernetes Community',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'HANDS_ON',
        isActive: true
      },
      {
        title: 'AWS DevOps Learning Path',
        description: 'Complete AWS DevOps learning path covering CI/CD, infrastructure as code, and monitoring',
        url: 'https://aws.amazon.com/training/learning-paths/devops/',
        author: 'Amazon Web Services',
        category: 'DEVOPS',
        type: 'COURSE',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true
      },
      {
        title: 'Terraform Documentation',
        description: 'Learn Infrastructure as Code with Terraform for multi-cloud deployments',
        url: 'https://developer.hashicorp.com/terraform/docs',
        author: 'HashiCorp',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'HANDS_ON',
        isActive: true
      },
      {
        title: 'Jenkins User Documentation',
        description: 'Complete guide to CI/CD automation with Jenkins, including pipeline creation and best practices',
        url: 'https://www.jenkins.io/doc/',
        author: 'Jenkins Community',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'BEGINNER',
        cost: 'FREE',
        format: 'HANDS_ON',
        isActive: true
      },
      {
        title: 'Prometheus Monitoring Guide',
        description: 'Learn application and infrastructure monitoring with Prometheus and Grafana',
        url: 'https://prometheus.io/docs/introduction/overview/',
        author: 'Prometheus Community',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREE',
        format: 'HANDS_ON',
        isActive: true
      },
      {
        title: 'GitLab CI/CD Documentation',
        description: 'Comprehensive guide to continuous integration and deployment with GitLab',
        url: 'https://docs.gitlab.com/ee/ci/',
        author: 'GitLab',
        category: 'DEVOPS',
        type: 'TUTORIAL',
        skillLevel: 'BEGINNER',
        cost: 'FREE',
        format: 'HANDS_ON',
        isActive: true
      },
      {
        title: 'Linux System Administration',
        description: 'Essential Linux skills for DevOps engineers including command line, scripting, and system management',
        url: 'https://www.edx.org/course/introduction-to-linux',
        author: 'Linux Foundation',
        category: 'DEVOPS',
        type: 'COURSE',
        skillLevel: 'BEGINNER',
        cost: 'FREE',
        format: 'INSTRUCTOR_LED',
        isActive: true
      }
    ];

    const additionLog = [];

    for (const resourceData of devOpsResources) {
      console.log(`➕ Adding: ${resourceData.title}`);
      console.log(`   Author: ${resourceData.author}`);
      console.log(`   URL: ${resourceData.url}`);
      console.log(`   Skill Level: ${resourceData.skillLevel}`);

      try {
        // Check if resource already exists
        const existingResource = await prisma.learningResource.findFirst({
          where: {
            OR: [
              { title: resourceData.title },
              { url: resourceData.url }
            ]
          }
        });

        if (existingResource) {
          console.log(`   ⚠️ Resource already exists, connecting to DevOps path`);
          
          // Connect existing resource to DevOps path
          await prisma.careerPath.update({
            where: { id: devOpsPath.id },
            data: {
              learningResources: {
                connect: { id: existingResource.id }
              }
            }
          });

          additionLog.push({
            resourceId: existingResource.id,
            resourceTitle: resourceData.title,
            author: resourceData.author,
            url: resourceData.url,
            category: resourceData.category,
            skillLevel: resourceData.skillLevel,
            cost: resourceData.cost,
            careerPath: 'DevOps Engineer',
            action: 'CONNECTED_EXISTING',
            addedAt: new Date().toISOString(),
            status: 'SUCCESS'
          });

        } else {
          // Create new resource
          const newResource = await prisma.learningResource.create({
            data: {
              ...resourceData,
              careerPaths: {
                connect: { id: devOpsPath.id }
              }
            }
          });

          console.log(`   ✅ Created and connected new resource (ID: ${newResource.id})`);

          additionLog.push({
            resourceId: newResource.id,
            resourceTitle: resourceData.title,
            author: resourceData.author,
            url: resourceData.url,
            category: resourceData.category,
            skillLevel: resourceData.skillLevel,
            cost: resourceData.cost,
            careerPath: 'DevOps Engineer',
            action: 'CREATED_NEW',
            addedAt: new Date().toISOString(),
            status: 'SUCCESS'
          });
        }

      } catch (error) {
        console.log(`   ❌ Failed to add: ${error.message}`);
        
        additionLog.push({
          resourceId: null,
          resourceTitle: resourceData.title,
          author: resourceData.author,
          url: resourceData.url,
          category: resourceData.category,
          skillLevel: resourceData.skillLevel,
          cost: resourceData.cost,
          careerPath: 'DevOps Engineer',
          action: 'FAILED',
          addedAt: new Date().toISOString(),
          status: 'FAILED',
          error: error.message
        });
      }

      console.log('');
    }

    // Check final state
    const updatedDevOpsPath = await prisma.careerPath.findFirst({
      where: { name: 'DevOps Engineer' },
      include: {
        learningResources: {
          select: {
            id: true,
            title: true,
            author: true,
            skillLevel: true,
            cost: true
          }
        }
      }
    });

    console.log(`📊 DevOps Engineer path now has ${updatedDevOpsPath.learningResources.length} resources\n`);
    
    console.log('🔧 DevOps Engineer resources:');
    updatedDevOpsPath.learningResources.forEach(resource => {
      console.log(`   • ${resource.title} (${resource.skillLevel}) - ${resource.author}`);
    });

    // Save addition log
    const fs = require('fs');
    const outputPath = 'docs/cleanup/devops-resources-addition-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(additionLog, null, 2));

    // Summary
    const created = additionLog.filter(log => log.status === 'SUCCESS' && log.action === 'CREATED_NEW').length;
    const connected = additionLog.filter(log => log.status === 'SUCCESS' && log.action === 'CONNECTED_EXISTING').length;
    const failed = additionLog.filter(log => log.status === 'FAILED').length;

    console.log(`\n📊 DEVOPS RESOURCES ADDITION SUMMARY:`);
    console.log(`Total resources processed: ${additionLog.length}`);
    console.log(`New resources created: ${created}`);
    console.log(`Existing resources connected: ${connected}`);
    console.log(`Failed operations: ${failed}`);
    console.log(`Resources before: ${devOpsPath.learningResources.length}`);
    console.log(`Resources after: ${updatedDevOpsPath.learningResources.length}`);

    // Summary by skill level
    console.log('\n📈 Resources by Skill Level:');
    const bySkillLevel = {};
    updatedDevOpsPath.learningResources.forEach(resource => {
      bySkillLevel[resource.skillLevel] = (bySkillLevel[resource.skillLevel] || 0) + 1;
    });
    
    Object.entries(bySkillLevel).forEach(([level, count]) => {
      console.log(`   ${level}: ${count} resources`);
    });

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      additionLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle}: ${log.error}`);
      });
    }

    console.log(`\n💾 Addition log saved to: ${outputPath}`);

    return additionLog;

  } catch (error) {
    console.error('❌ Error adding DevOps resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  addDevOpsResources();
}

module.exports = { addDevOpsResources };
