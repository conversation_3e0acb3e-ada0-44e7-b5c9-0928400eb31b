const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function removeIrrelevantUXResources() {
  console.log('🎨 REMOVING IRRELEVANT RESOURCES FROM UX/UI DESIGNER PATH');
  console.log('======================================================\n');

  try {
    // Get UX/UI Designer career path
    const uxCareerPath = await prisma.careerPath.findFirst({
      where: { name: 'UX/UI Designer' },
      include: {
        learningResources: {
          select: {
            id: true,
            title: true,
            category: true,
            url: true,
            author: true
          }
        }
      }
    });

    if (!uxCareerPath) {
      console.log('❌ UX/UI Designer career path not found');
      return;
    }

    console.log(`📊 UX/UI Designer path currently has ${uxCareerPath.learningResources.length} resources\n`);

    // Define irrelevant resources to remove (web development resources)
    const irrelevantResources = [
      'freeCodeCamp Full Stack Development',
      'React Official Tutorial', 
      'The Odin Project',
      'MDN Web Docs',
      'Node.js Developer Roadmap',
      'Project Management Foundations',
      'Introduction to Project Management',
      'Business English for International Careers',
      'Technical Communication Skills',
      'Cross-Cultural Communication',
      'Professional Presentation Skills',
      'Test Project Resource'
    ];

    const removalLog = [];

    for (const resourceTitle of irrelevantResources) {
      console.log(`🔍 Looking for: ${resourceTitle}`);
      
      // Find the resource in the UX/UI Designer path
      const resource = uxCareerPath.learningResources.find(r => r.title === resourceTitle);
      
      if (resource) {
        console.log(`   Found: ${resource.title} (${resource.id})`);
        console.log(`   Category: ${resource.category}`);
        console.log(`   Author: ${resource.author}`);

        try {
          // Remove the connection between this resource and UX/UI Designer career path
          await prisma.careerPath.update({
            where: { id: uxCareerPath.id },
            data: {
              learningResources: {
                disconnect: { id: resource.id }
              }
            }
          });

          console.log(`   ✅ Disconnected from UX/UI Designer path`);

          removalLog.push({
            resourceId: resource.id,
            resourceTitle: resource.title,
            category: resource.category,
            author: resource.author,
            url: resource.url,
            careerPath: 'UX/UI Designer',
            action: 'DISCONNECTED',
            reason: 'Web development resource not relevant to UX/UI design',
            removedAt: new Date().toISOString(),
            status: 'SUCCESS'
          });

        } catch (error) {
          console.log(`   ❌ Failed to disconnect: ${error.message}`);
          
          removalLog.push({
            resourceId: resource.id,
            resourceTitle: resource.title,
            category: resource.category,
            author: resource.author,
            url: resource.url,
            careerPath: 'UX/UI Designer',
            action: 'DISCONNECTED',
            reason: 'Web development resource not relevant to UX/UI design',
            removedAt: new Date().toISOString(),
            status: 'FAILED',
            error: error.message
          });
        }
      } else {
        console.log(`   ⚠️ Not found in UX/UI Designer path (may have been already removed)`);
        
        removalLog.push({
          resourceId: null,
          resourceTitle: resourceTitle,
          category: 'Unknown',
          author: 'Unknown',
          url: 'Unknown',
          careerPath: 'UX/UI Designer',
          action: 'DISCONNECTED',
          reason: 'Web development resource not relevant to UX/UI design',
          removedAt: new Date().toISOString(),
          status: 'NOT_FOUND'
        });
      }

      console.log('');
    }

    // Check final state
    const updatedUXPath = await prisma.careerPath.findFirst({
      where: { name: 'UX/UI Designer' },
      include: {
        learningResources: {
          select: {
            id: true,
            title: true,
            category: true
          }
        }
      }
    });

    console.log(`📊 UX/UI Designer path now has ${updatedUXPath.learningResources.length} resources\n`);
    
    console.log('🎨 Remaining UX/UI Designer resources:');
    updatedUXPath.learningResources.forEach(resource => {
      console.log(`   • ${resource.title} (${resource.category})`);
    });

    // Save removal log
    const fs = require('fs');
    const outputPath = 'docs/cleanup/ux-irrelevant-removal-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(removalLog, null, 2));

    // Summary
    const successful = removalLog.filter(log => log.status === 'SUCCESS').length;
    const notFound = removalLog.filter(log => log.status === 'NOT_FOUND').length;
    const failed = removalLog.filter(log => log.status === 'FAILED').length;

    console.log(`\n📊 UX/UI DESIGNER CLEANUP SUMMARY:`);
    console.log(`Total processed: ${removalLog.length}`);
    console.log(`Successfully disconnected: ${successful}`);
    console.log(`Not found (already removed): ${notFound}`);
    console.log(`Failed operations: ${failed}`);
    console.log(`Resources before: ${uxCareerPath.learningResources.length}`);
    console.log(`Resources after: ${updatedUXPath.learningResources.length}`);
    console.log(`Resources removed: ${uxCareerPath.learningResources.length - updatedUXPath.learningResources.length}`);

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      removalLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle}: ${log.error}`);
      });
    }

    console.log(`\n💾 Removal log saved to: ${outputPath}`);

    return removalLog;

  } catch (error) {
    console.error('❌ Error removing irrelevant UX resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  removeIrrelevantUXResources();
}

module.exports = { removeIrrelevantUXResources };
