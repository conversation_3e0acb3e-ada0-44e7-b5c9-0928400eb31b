const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function replaceBrokenResources() {
  console.log('🔄 REPLACING BROKEN RESOURCES WITH WORKING ALTERNATIVES');
  console.log('=====================================================\n');

  try {
    // Load the deletion logs to identify what was removed and needs replacement
    const fs = require('fs');
    
    // Load consolidated deletion log
    const deletionLog = JSON.parse(fs.readFileSync('docs/cleanup/consolidated-deletion-log.json', 'utf8'));
    
    console.log(`📊 Found ${deletionLog.deletions.length} deleted resources to analyze for replacement\n`);

    // Define high-quality replacements for key deleted resources
    const replacementResources = [
      // Replace Kaggle Learn (was 404)
      {
        title: 'Kaggle Learn Courses',
        description: 'Free micro-courses covering practical data science skills including Python, machine learning, and data visualization',
        url: 'https://www.kaggle.com/learn',
        author: 'Kaggle',
        category: 'DATA_SCIENCE',
        type: 'COURSE',
        skillLevel: 'BEGINNER',
        cost: 'FREE',
        format: 'HANDS_ON',
        isActive: true,
        replaces: 'Kaggle Learn',
        targetCareerPaths: ['Data Scientist', 'AI/Machine Learning Engineer']
      },
      // Replace Adobe XD Tutorials (was timeout)
      {
        title: 'Adobe XD User Guide',
        description: 'Complete guide to Adobe XD for UI/UX design including prototyping, collaboration, and design systems',
        url: 'https://helpx.adobe.com/xd/user-guide.html',
        author: 'Adobe',
        category: 'UX_UI_DESIGN',
        type: 'TUTORIAL',
        skillLevel: 'BEGINNER',
        cost: 'FREE',
        format: 'SELF_PACED',
        isActive: true,
        replaces: 'Adobe XD Tutorials',
        targetCareerPaths: ['UX/UI Designer']
      },
      // Replace broken LinkedIn Learning courses
      {
        title: 'LinkedIn Learning Tech Skills',
        description: 'Professional development courses covering leadership, communication, and technical skills for career advancement',
        url: 'https://www.linkedin.com/learning/',
        author: 'LinkedIn Learning',
        category: 'PROJECT_MANAGEMENT',
        type: 'COURSE',
        skillLevel: 'INTERMEDIATE',
        cost: 'SUBSCRIPTION',
        format: 'INSTRUCTOR_LED',
        isActive: true,
        replaces: 'Building and Leading Teams',
        targetCareerPaths: ['Product Manager']
      },
      // Replace broken Coursera courses with working alternatives
      {
        title: 'Coursera Personal Finance Courses',
        description: 'Comprehensive personal finance education covering budgeting, investing, and financial planning',
        url: 'https://www.coursera.org/browse/personal-development/personal-finance',
        author: 'Coursera',
        category: 'FINANCIAL_LITERACY',
        type: 'COURSE',
        skillLevel: 'BEGINNER',
        cost: 'FREEMIUM',
        format: 'INSTRUCTOR_LED',
        isActive: true,
        replaces: 'Personal Finance Course',
        targetCareerPaths: ['Simple Online Business Owner']
      },
      // Replace broken edX courses
      {
        title: 'edX Computer Science Courses',
        description: 'University-level computer science courses covering algorithms, data structures, and software engineering',
        url: 'https://www.edx.org/learn/computer-science',
        author: 'edX',
        category: 'WEB_DEVELOPMENT',
        type: 'COURSE',
        skillLevel: 'INTERMEDIATE',
        cost: 'FREEMIUM',
        format: 'INSTRUCTOR_LED',
        isActive: true,
        replaces: 'Entrepreneurship MicroMaster',
        targetCareerPaths: ['Freelance Web Developer']
      }
    ];

    const replacementLog = [];

    for (const replacement of replacementResources) {
      console.log(`🔄 Adding replacement: ${replacement.title}`);
      console.log(`   Replaces: ${replacement.replaces}`);
      console.log(`   Author: ${replacement.author}`);
      console.log(`   URL: ${replacement.url}`);
      console.log(`   Target Career Paths: ${replacement.targetCareerPaths.join(', ')}`);

      try {
        // Check if replacement already exists
        const existingResource = await prisma.learningResource.findFirst({
          where: {
            OR: [
              { title: replacement.title },
              { url: replacement.url }
            ]
          }
        });

        if (existingResource) {
          console.log(`   ⚠️ Replacement already exists, connecting to target career paths`);
          
          // Connect to target career paths
          for (const pathName of replacement.targetCareerPaths) {
            const careerPath = await prisma.careerPath.findFirst({
              where: { name: pathName }
            });
            
            if (careerPath) {
              await prisma.careerPath.update({
                where: { id: careerPath.id },
                data: {
                  learningResources: {
                    connect: { id: existingResource.id }
                  }
                }
              });
              console.log(`     ✅ Connected to ${pathName}`);
            }
          }

          replacementLog.push({
            resourceId: existingResource.id,
            resourceTitle: replacement.title,
            author: replacement.author,
            url: replacement.url,
            category: replacement.category,
            skillLevel: replacement.skillLevel,
            cost: replacement.cost,
            replaces: replacement.replaces,
            targetCareerPaths: replacement.targetCareerPaths,
            action: 'CONNECTED_EXISTING',
            addedAt: new Date().toISOString(),
            status: 'SUCCESS'
          });

        } else {
          // Create new replacement resource
          const { targetCareerPaths, replaces, ...resourceData } = replacement;
          
          // Get career path IDs
          const careerPathIds = [];
          for (const pathName of targetCareerPaths) {
            const careerPath = await prisma.careerPath.findFirst({
              where: { name: pathName }
            });
            if (careerPath) {
              careerPathIds.push({ id: careerPath.id });
            }
          }

          const newResource = await prisma.learningResource.create({
            data: {
              ...resourceData,
              careerPaths: {
                connect: careerPathIds
              }
            }
          });

          console.log(`   ✅ Created replacement resource (ID: ${newResource.id})`);
          console.log(`   ✅ Connected to ${targetCareerPaths.length} career path(s)`);

          replacementLog.push({
            resourceId: newResource.id,
            resourceTitle: replacement.title,
            author: replacement.author,
            url: replacement.url,
            category: replacement.category,
            skillLevel: replacement.skillLevel,
            cost: replacement.cost,
            replaces: replacement.replaces,
            targetCareerPaths: replacement.targetCareerPaths,
            action: 'CREATED_NEW',
            addedAt: new Date().toISOString(),
            status: 'SUCCESS'
          });
        }

      } catch (error) {
        console.log(`   ❌ Failed to add replacement: ${error.message}`);
        
        replacementLog.push({
          resourceId: null,
          resourceTitle: replacement.title,
          author: replacement.author,
          url: replacement.url,
          category: replacement.category,
          skillLevel: replacement.skillLevel,
          cost: replacement.cost,
          replaces: replacement.replaces,
          targetCareerPaths: replacement.targetCareerPaths,
          action: 'FAILED',
          addedAt: new Date().toISOString(),
          status: 'FAILED',
          error: error.message
        });
      }

      console.log('');
    }

    // Save replacement log
    const outputPath = 'docs/cleanup/replacement-resources-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(replacementLog, null, 2));

    // Summary
    const created = replacementLog.filter(log => log.status === 'SUCCESS' && log.action === 'CREATED_NEW').length;
    const connected = replacementLog.filter(log => log.status === 'SUCCESS' && log.action === 'CONNECTED_EXISTING').length;
    const failed = replacementLog.filter(log => log.status === 'FAILED').length;

    console.log(`📊 REPLACEMENT RESOURCES SUMMARY:`);
    console.log(`Total replacements processed: ${replacementLog.length}`);
    console.log(`New replacement resources created: ${created}`);
    console.log(`Existing resources connected: ${connected}`);
    console.log(`Failed operations: ${failed}`);

    // Summary by category
    console.log('\n📂 Replacements by Category:');
    const byCategory = {};
    replacementLog.filter(log => log.status === 'SUCCESS').forEach(log => {
      byCategory[log.category] = (byCategory[log.category] || 0) + 1;
    });
    
    Object.entries(byCategory).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} replacements`);
    });

    // Summary by career path
    console.log('\n🛤️ Replacements by Career Path:');
    const byCareerPath = {};
    replacementLog.filter(log => log.status === 'SUCCESS').forEach(log => {
      log.targetCareerPaths.forEach(path => {
        byCareerPath[path] = (byCareerPath[path] || 0) + 1;
      });
    });
    
    Object.entries(byCareerPath).forEach(([path, count]) => {
      console.log(`   ${path}: ${count} replacements`);
    });

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      replacementLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle}: ${log.error}`);
      });
    }

    console.log(`\n💾 Replacement log saved to: ${outputPath}`);

    return replacementLog;

  } catch (error) {
    console.error('❌ Error replacing broken resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  replaceBrokenResources();
}

module.exports = { replaceBrokenResources };
