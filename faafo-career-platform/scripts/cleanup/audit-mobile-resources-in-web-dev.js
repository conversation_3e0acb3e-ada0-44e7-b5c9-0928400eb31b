const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function auditMobileResourcesInWebDev() {
  console.log('📱 AUDITING MOBILE DEVELOPMENT RESOURCES IN WEB DEVELOPER PATH');
  console.log('=============================================================\n');

  try {
    // Get Freelance Web Developer career path
    const webDevPath = await prisma.careerPath.findFirst({
      where: { name: 'Freelance Web Developer' },
      include: {
        learningResources: {
          where: {
            category: 'MOBILE_DEVELOPMENT'
          },
          select: {
            id: true,
            title: true,
            category: true,
            url: true,
            author: true,
            description: true
          }
        }
      }
    });

    if (!webDevPath) {
      console.log('❌ Freelance Web Developer career path not found');
      return;
    }

    console.log(`📊 Found ${webDevPath.learningResources.length} mobile development resources in Freelance Web Developer path\n`);

    const auditLog = [];

    for (const resource of webDevPath.learningResources) {
      console.log(`📱 Auditing: ${resource.title}`);
      console.log(`   Category: ${resource.category}`);
      console.log(`   Author: ${resource.author}`);
      console.log(`   Description: ${resource.description || 'No description'}`);

      // Assess relevance to freelance web developers
      const relevanceAssessment = assessMobileResourceRelevance(resource);
      console.log(`   Assessment: ${relevanceAssessment.relevant ? 'KEEP' : 'REMOVE'}`);
      console.log(`   Reason: ${relevanceAssessment.reason}`);
      console.log(`   Priority: ${relevanceAssessment.priority}`);

      if (!relevanceAssessment.relevant) {
        try {
          // Remove the connection between this resource and the career path
          await prisma.careerPath.update({
            where: { id: webDevPath.id },
            data: {
              learningResources: {
                disconnect: { id: resource.id }
              }
            }
          });

          console.log(`   ✅ Removed from Freelance Web Developer path`);

          auditLog.push({
            resourceId: resource.id,
            resourceTitle: resource.title,
            category: resource.category,
            author: resource.author,
            url: resource.url,
            description: resource.description,
            careerPath: 'Freelance Web Developer',
            action: 'REMOVED',
            reason: relevanceAssessment.reason,
            priority: relevanceAssessment.priority,
            removedAt: new Date().toISOString(),
            status: 'SUCCESS'
          });

        } catch (error) {
          console.log(`   ❌ Failed to remove: ${error.message}`);
          
          auditLog.push({
            resourceId: resource.id,
            resourceTitle: resource.title,
            category: resource.category,
            author: resource.author,
            url: resource.url,
            description: resource.description,
            careerPath: 'Freelance Web Developer',
            action: 'REMOVED',
            reason: relevanceAssessment.reason,
            priority: relevanceAssessment.priority,
            removedAt: new Date().toISOString(),
            status: 'FAILED',
            error: error.message
          });
        }
      } else {
        console.log(`   ⚠️ Keeping resource (essential for modern web developers)`);
        
        auditLog.push({
          resourceId: resource.id,
          resourceTitle: resource.title,
          category: resource.category,
          author: resource.author,
          url: resource.url,
          description: resource.description,
          careerPath: 'Freelance Web Developer',
          action: 'KEPT',
          reason: relevanceAssessment.reason,
          priority: relevanceAssessment.priority,
          removedAt: new Date().toISOString(),
          status: 'KEPT'
        });
      }

      console.log('');
    }

    // Check final state
    const updatedWebDevPath = await prisma.careerPath.findFirst({
      where: { name: 'Freelance Web Developer' },
      include: {
        learningResources: {
          select: {
            id: true,
            title: true,
            category: true
          }
        }
      }
    });

    console.log(`📊 Freelance Web Developer path now has ${updatedWebDevPath.learningResources.length} total resources\n`);
    
    console.log('📚 Remaining resources by category:');
    const byCategory = {};
    updatedWebDevPath.learningResources.forEach(resource => {
      byCategory[resource.category] = (byCategory[resource.category] || 0) + 1;
    });
    
    Object.entries(byCategory).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} resources`);
    });

    // Show remaining mobile resources specifically
    const remainingMobileResources = updatedWebDevPath.learningResources.filter(r => r.category === 'MOBILE_DEVELOPMENT');
    if (remainingMobileResources.length > 0) {
      console.log('\n📱 Remaining mobile development resources:');
      remainingMobileResources.forEach(resource => {
        console.log(`   • ${resource.title}`);
      });
    } else {
      console.log('\n📱 No mobile development resources remaining (focused on web development)');
    }

    // Save audit log
    const fs = require('fs');
    const outputPath = 'docs/cleanup/mobile-resources-audit-log.json';
    fs.writeFileSync(outputPath, JSON.stringify(auditLog, null, 2));

    // Summary
    const removed = auditLog.filter(log => log.status === 'SUCCESS').length;
    const kept = auditLog.filter(log => log.status === 'KEPT').length;
    const failed = auditLog.filter(log => log.status === 'FAILED').length;

    console.log(`\n📊 MOBILE RESOURCES AUDIT SUMMARY:`);
    console.log(`Total mobile resources audited: ${auditLog.length}`);
    console.log(`Removed (too specialized): ${removed}`);
    console.log(`Kept (essential for web devs): ${kept}`);
    console.log(`Failed operations: ${failed}`);
    console.log(`Resources before: ${webDevPath.learningResources.length} mobile`);
    console.log(`Resources after: ${remainingMobileResources.length} mobile`);

    // Summary by priority
    console.log('\n📈 Decisions by Priority:');
    const byPriority = {};
    auditLog.forEach(log => {
      const key = `${log.priority}_${log.action}`;
      byPriority[key] = (byPriority[key] || 0) + 1;
    });
    
    Object.entries(byPriority).forEach(([key, count]) => {
      const [priority, action] = key.split('_');
      console.log(`   ${priority}: ${count} ${action.toLowerCase()}`);
    });

    if (failed > 0) {
      console.log('\n❌ Failed operations:');
      auditLog.filter(log => log.status === 'FAILED').forEach(log => {
        console.log(`  - ${log.resourceTitle}: ${log.error}`);
      });
    }

    console.log(`\n💾 Audit log saved to: ${outputPath}`);

    return auditLog;

  } catch (error) {
    console.error('❌ Error auditing mobile resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function assessMobileResourceRelevance(resource) {
  const title = resource.title.toLowerCase();
  
  // React Native - KEEP (essential for web developers expanding to mobile)
  if (title.includes('react native')) {
    return {
      relevant: true,
      reason: 'React Native allows web developers to leverage React skills for mobile development',
      priority: 'HIGH'
    };
  }
  
  // Native iOS development - REMOVE (too specialized)
  if (title.includes('ios') && !title.includes('react native') && !title.includes('web')) {
    return {
      relevant: false,
      reason: 'Native iOS development requires specialized skills not core to web development',
      priority: 'LOW'
    };
  }
  
  // Native Android development - REMOVE (too specialized)
  if (title.includes('android') && !title.includes('react native') && !title.includes('web')) {
    return {
      relevant: false,
      reason: 'Native Android development requires specialized skills not core to web development',
      priority: 'LOW'
    };
  }
  
  // Flutter - REMOVE (different framework, not web-based)
  if (title.includes('flutter')) {
    return {
      relevant: false,
      reason: 'Flutter uses Dart language and different paradigms from web development',
      priority: 'LOW'
    };
  }
  
  // Mobile UI/UX - KEEP (relevant for responsive web design)
  if (title.includes('mobile ui') || title.includes('mobile ux') || title.includes('mobile design')) {
    return {
      relevant: true,
      reason: 'Mobile UI/UX principles are essential for responsive web design',
      priority: 'MEDIUM'
    };
  }
  
  // App Store / Play Store - REMOVE (not relevant to web developers)
  if (title.includes('app store') || title.includes('play store') || title.includes('aso')) {
    return {
      relevant: false,
      reason: 'App store optimization not relevant to web developers',
      priority: 'LOW'
    };
  }
  
  // Mobile testing and deployment - REMOVE (too specialized)
  if (title.includes('mobile app testing') || title.includes('mobile deployment')) {
    return {
      relevant: false,
      reason: 'Mobile app testing and deployment not core to web development',
      priority: 'LOW'
    };
  }
  
  // Default assessment for other mobile resources
  return {
    relevant: false,
    reason: 'Mobile development resource too specialized for general web development',
    priority: 'MEDIUM'
  };
}

if (require.main === module) {
  auditMobileResourcesInWebDev();
}

module.exports = { auditMobileResourcesInWebDev };
