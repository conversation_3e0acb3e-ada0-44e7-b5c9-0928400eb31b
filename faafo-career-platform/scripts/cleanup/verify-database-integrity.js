const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyDatabaseIntegrity() {
  console.log('🔍 DATABASE INTEGRITY VERIFICATION');
  console.log('==================================\n');

  const integrityReport = {
    timestamp: new Date().toISOString(),
    checks: {},
    issues: [],
    summary: {
      totalChecks: 0,
      passedChecks: 0,
      failedChecks: 0,
      overallStatus: 'PENDING'
    }
  };

  try {
    // 1. Check for orphaned UserLearningProgress records
    console.log('1. 🔗 Checking for orphaned UserLearningProgress records...');
    const allProgress = await prisma.userLearningProgress.findMany({
      include: {
        resource: true
      }
    });
    const orphanedProgress = allProgress.filter(p => !p.resource);

    integrityReport.checks.orphanedProgress = {
      name: 'Orphaned UserLearningProgress Records',
      passed: orphanedProgress.length === 0,
      count: orphanedProgress.length,
      details: orphanedProgress.length > 0 ? `Found ${orphanedProgress.length} orphaned progress records` : 'No orphaned progress records found'
    };

    if (orphanedProgress.length > 0) {
      integrityReport.issues.push({
        type: 'ORPHANED_RECORDS',
        severity: 'MEDIUM',
        description: `${orphanedProgress.length} UserLearningProgress records reference non-existent resources`,
        affectedRecords: orphanedProgress.map(p => p.id)
      });
    }

    console.log(`   ${orphanedProgress.length === 0 ? '✅' : '❌'} ${integrityReport.checks.orphanedProgress.details}\n`);

    // 2. Check for duplicate resources (same URL)
    console.log('2. 🔄 Checking for duplicate resources...');
    const duplicateUrls = await prisma.learningResource.groupBy({
      by: ['url'],
      having: {
        url: {
          _count: {
            gt: 1
          }
        }
      },
      _count: {
        url: true
      }
    });

    integrityReport.checks.duplicateResources = {
      name: 'Duplicate Resource URLs',
      passed: duplicateUrls.length === 0,
      count: duplicateUrls.length,
      details: duplicateUrls.length > 0 ? `Found ${duplicateUrls.length} duplicate URLs` : 'No duplicate URLs found'
    };

    if (duplicateUrls.length > 0) {
      integrityReport.issues.push({
        type: 'DUPLICATE_RESOURCES',
        severity: 'HIGH',
        description: `${duplicateUrls.length} URLs are used by multiple resources`,
        affectedUrls: duplicateUrls.map(d => d.url)
      });
    }

    console.log(`   ${duplicateUrls.length === 0 ? '✅' : '❌'} ${integrityReport.checks.duplicateResources.details}\n`);

    // 3. Check for resources with missing required fields
    console.log('3. 📝 Checking for missing required fields...');
    const allResources = await prisma.learningResource.findMany();
    const resourcesWithMissingFields = allResources.filter(r =>
      !r.title || r.title.trim() === '' ||
      !r.url || r.url.trim() === '' ||
      !r.category ||
      !r.type ||
      !r.skillLevel ||
      !r.cost
    );

    integrityReport.checks.missingFields = {
      name: 'Missing Required Fields',
      passed: resourcesWithMissingFields.length === 0,
      count: resourcesWithMissingFields.length,
      details: resourcesWithMissingFields.length > 0 ? `Found ${resourcesWithMissingFields.length} resources with missing fields` : 'All resources have required fields'
    };

    if (resourcesWithMissingFields.length > 0) {
      integrityReport.issues.push({
        type: 'MISSING_FIELDS',
        severity: 'HIGH',
        description: `${resourcesWithMissingFields.length} resources have missing required fields`,
        affectedResources: resourcesWithMissingFields.map(r => ({ id: r.id, title: r.title }))
      });
    }

    console.log(`   ${resourcesWithMissingFields.length === 0 ? '✅' : '❌'} ${integrityReport.checks.missingFields.details}\n`);

    // 4. Check for inactive resources that are still connected to career paths
    console.log('4. 🚫 Checking for inactive resources in career paths...');
    const inactiveConnectedResources = await prisma.learningResource.findMany({
      where: {
        isActive: false,
        careerPaths: {
          some: {}
        }
      },
      include: {
        careerPaths: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    integrityReport.checks.inactiveConnected = {
      name: 'Inactive Resources Connected to Career Paths',
      passed: inactiveConnectedResources.length === 0,
      count: inactiveConnectedResources.length,
      details: inactiveConnectedResources.length > 0 ? `Found ${inactiveConnectedResources.length} inactive resources still connected` : 'No inactive resources connected to career paths'
    };

    if (inactiveConnectedResources.length > 0) {
      integrityReport.issues.push({
        type: 'INACTIVE_CONNECTED',
        severity: 'MEDIUM',
        description: `${inactiveConnectedResources.length} inactive resources are still connected to career paths`,
        affectedResources: inactiveConnectedResources.map(r => ({ 
          id: r.id, 
          title: r.title, 
          careerPaths: r.careerPaths.map(cp => cp.name) 
        }))
      });
    }

    console.log(`   ${inactiveConnectedResources.length === 0 ? '✅' : '❌'} ${integrityReport.checks.inactiveConnected.details}\n`);

    // 5. Check for career paths with no resources
    console.log('5. 📚 Checking for empty career paths...');
    const emptyCareerPaths = await prisma.careerPath.findMany({
      where: {
        learningResources: {
          none: {
            isActive: true
          }
        }
      }
    });

    integrityReport.checks.emptyCareerPaths = {
      name: 'Empty Career Paths',
      passed: emptyCareerPaths.length === 0,
      count: emptyCareerPaths.length,
      details: emptyCareerPaths.length > 0 ? `Found ${emptyCareerPaths.length} empty career paths` : 'All career paths have resources'
    };

    if (emptyCareerPaths.length > 0) {
      integrityReport.issues.push({
        type: 'EMPTY_CAREER_PATHS',
        severity: 'HIGH',
        description: `${emptyCareerPaths.length} career paths have no active resources`,
        affectedPaths: emptyCareerPaths.map(cp => ({ id: cp.id, name: cp.name }))
      });
    }

    console.log(`   ${emptyCareerPaths.length === 0 ? '✅' : '❌'} ${integrityReport.checks.emptyCareerPaths.details}\n`);

    // 6. Check for resources with invalid enum values
    console.log('6. 🔤 Checking for invalid enum values...');
    const validCategories = ['WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'DATA_SCIENCE', 'ARTIFICIAL_INTELLIGENCE', 'CYBERSECURITY', 'DEVOPS', 'UX_UI_DESIGN', 'DIGITAL_MARKETING', 'PROJECT_MANAGEMENT', 'ENTREPRENEURSHIP', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'BLOCKCHAIN', 'PRODUCT_MANAGEMENT'];
    const validTypes = ['COURSE', 'TUTORIAL', 'ARTICLE', 'VIDEO', 'BOOK', 'CERTIFICATION', 'TOOL', 'TEMPLATE'];
    const validSkillLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'];
    const validCosts = ['FREE', 'FREEMIUM', 'PAID', 'SUBSCRIPTION'];

    const allResourcesForEnum = await prisma.learningResource.findMany();
    const invalidEnumResources = allResourcesForEnum.filter(r =>
      !validCategories.includes(r.category) ||
      !validTypes.includes(r.type) ||
      !validSkillLevels.includes(r.skillLevel) ||
      !validCosts.includes(r.cost)
    );

    integrityReport.checks.invalidEnums = {
      name: 'Invalid Enum Values',
      passed: invalidEnumResources.length === 0,
      count: invalidEnumResources.length,
      details: invalidEnumResources.length > 0 ? `Found ${invalidEnumResources.length} resources with invalid enum values` : 'All enum values are valid'
    };

    if (invalidEnumResources.length > 0) {
      integrityReport.issues.push({
        type: 'INVALID_ENUMS',
        severity: 'HIGH',
        description: `${invalidEnumResources.length} resources have invalid enum values`,
        affectedResources: invalidEnumResources.map(r => ({ 
          id: r.id, 
          title: r.title,
          category: r.category,
          type: r.type,
          skillLevel: r.skillLevel,
          cost: r.cost
        }))
      });
    }

    console.log(`   ${invalidEnumResources.length === 0 ? '✅' : '❌'} ${integrityReport.checks.invalidEnums.details}\n`);

    // 7. Check database connection and basic queries
    console.log('7. 🔌 Testing database connectivity...');
    try {
      const resourceCount = await prisma.learningResource.count();
      const careerPathCount = await prisma.careerPath.count();
      
      integrityReport.checks.connectivity = {
        name: 'Database Connectivity',
        passed: true,
        details: `Successfully connected. Found ${resourceCount} resources and ${careerPathCount} career paths`
      };

      console.log(`   ✅ ${integrityReport.checks.connectivity.details}\n`);
    } catch (error) {
      integrityReport.checks.connectivity = {
        name: 'Database Connectivity',
        passed: false,
        details: `Connection failed: ${error.message}`
      };

      integrityReport.issues.push({
        type: 'CONNECTIVITY',
        severity: 'CRITICAL',
        description: 'Database connectivity issues detected',
        error: error.message
      });

      console.log(`   ❌ ${integrityReport.checks.connectivity.details}\n`);
    }

    // Calculate summary
    const checkNames = Object.keys(integrityReport.checks);
    integrityReport.summary.totalChecks = checkNames.length;
    integrityReport.summary.passedChecks = checkNames.filter(name => integrityReport.checks[name].passed).length;
    integrityReport.summary.failedChecks = integrityReport.summary.totalChecks - integrityReport.summary.passedChecks;

    if (integrityReport.summary.failedChecks === 0) {
      integrityReport.summary.overallStatus = 'EXCELLENT';
    } else if (integrityReport.summary.failedChecks <= 2) {
      integrityReport.summary.overallStatus = 'GOOD';
    } else {
      integrityReport.summary.overallStatus = 'NEEDS_ATTENTION';
    }

    // Display summary
    console.log('📊 INTEGRITY VERIFICATION SUMMARY');
    console.log('=================================');
    console.log(`Total Checks: ${integrityReport.summary.totalChecks}`);
    console.log(`Passed: ${integrityReport.summary.passedChecks}`);
    console.log(`Failed: ${integrityReport.summary.failedChecks}`);
    console.log(`Overall Status: ${integrityReport.summary.overallStatus}`);

    if (integrityReport.issues.length > 0) {
      console.log('\n🚨 ISSUES FOUND:');
      integrityReport.issues.forEach((issue, index) => {
        console.log(`${index + 1}. [${issue.severity}] ${issue.description}`);
      });
    } else {
      console.log('\n🎉 No integrity issues found! Database is in excellent condition.');
    }

    // Save report
    const fs = require('fs');
    const outputPath = 'docs/cleanup/database-integrity-report.json';
    fs.writeFileSync(outputPath, JSON.stringify(integrityReport, null, 2));

    console.log(`\n💾 Integrity report saved to: ${outputPath}`);

    return integrityReport;

  } catch (error) {
    console.error('❌ Error during integrity verification:', error);
    integrityReport.summary.overallStatus = 'ERROR';
    integrityReport.issues.push({
      type: 'VERIFICATION_ERROR',
      severity: 'CRITICAL',
      description: 'Error occurred during integrity verification',
      error: error.message
    });
    return integrityReport;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  verifyDatabaseIntegrity();
}

module.exports = { verifyDatabaseIntegrity };
