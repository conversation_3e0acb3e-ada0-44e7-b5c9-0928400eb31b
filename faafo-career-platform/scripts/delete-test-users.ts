import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function deleteTestUsers() {
  console.log('🧹 Starting test user cleanup...');

  try {
    // First, let's see what users we have
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true,
        _count: {
          select: {
            forumPosts: true,
            assessments: true,
            sessions: true
          }
        }
      },
      orderBy: { createdAt: 'asc' }
    });

    console.log('📋 Current users in database:');
    allUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.email} (${user.name}) - Posts: ${user._count.forumPosts}, Assessments: ${user._count.assessments}, Sessions: ${user._count.sessions}`);
    });

    // Identify users to keep (admin/system users)
    const usersToKeep = allUsers.filter(user => 
      user.email === '<EMAIL>' || 
      user.email.includes('admin') ||
      user.name === 'FAAFO Team'
    );

    // Identify test users to delete (everyone else)
    const testUsersToDelete = allUsers.filter(user => 
      user.email !== '<EMAIL>' && 
      !user.email.includes('admin') &&
      user.name !== 'FAAFO Team'
    );

    console.log(`\n🔒 Users to KEEP (${usersToKeep.length}):`);
    usersToKeep.forEach(user => {
      console.log(`  ✅ ${user.email} (${user.name})`);
    });

    console.log(`\n🗑️  Test users to DELETE (${testUsersToDelete.length}):`);
    testUsersToDelete.forEach(user => {
      console.log(`  ❌ ${user.email} (${user.name})`);
    });

    if (testUsersToDelete.length === 0) {
      console.log('\n✨ No test users found to delete!');
      return;
    }

    // Confirm deletion
    console.log(`\n⚠️  About to delete ${testUsersToDelete.length} test users...`);
    
    // Get user IDs to delete
    const userIdsToDelete = testUsersToDelete.map(user => user.id);

    // Delete in the correct order due to foreign key constraints
    console.log('🔄 Deleting user-related data...');

    // Delete user sessions first
    const deletedSessions = await prisma.session.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedSessions.count} sessions`);

    // Delete user accounts (OAuth)
    const deletedAccounts = await prisma.account.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedAccounts.count} OAuth accounts`);

    // Delete user profiles
    const deletedProfiles = await prisma.profile.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedProfiles.count} user profiles`);

    // Delete assessments
    const deletedAssessments = await prisma.assessment.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedAssessments.count} assessments`);

    // Delete freedom fund records
    const deletedFreedomFunds = await prisma.freedomFund.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedFreedomFunds.count} freedom fund records`);

    // Delete bookmarks
    const deletedCareerBookmarks = await prisma.careerPathBookmark.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedCareerBookmarks.count} career path bookmarks`);

    const deletedForumBookmarks = await prisma.forumBookmark.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedForumBookmarks.count} forum bookmarks`);

    // Delete forum reactions
    const deletedPostReactions = await prisma.forumPostReaction.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedPostReactions.count} forum post reactions`);

    const deletedReplyReactions = await prisma.forumReplyReaction.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedReplyReactions.count} forum reply reactions`);

    // Delete forum reports
    const deletedReports = await prisma.forumReport.deleteMany({
      where: { reporterId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedReports.count} forum reports`);

    // Delete forum replies
    const deletedReplies = await prisma.forumReply.deleteMany({
      where: { authorId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedReplies.count} forum replies`);

    // Delete forum posts (but keep the welcome messages from FAAFO Team)
    const deletedPosts = await prisma.forumPost.deleteMany({
      where: { 
        authorId: { in: userIdsToDelete }
      }
    });
    console.log(`  ✅ Deleted ${deletedPosts.count} forum posts`);

    // Delete any other user-related records
    const deletedPayments = await prisma.payment.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedPayments.count} payment records`);

    const deletedSubscriptions = await prisma.subscription.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedSubscriptions.count} subscription records`);

    // Delete learning-related records
    const deletedLearningProgress = await prisma.userLearningProgress.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedLearningProgress.count} learning progress records`);

    const deletedLearningPaths = await prisma.userLearningPath.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedLearningPaths.count} learning path records`);

    const deletedLearningPathProgress = await prisma.userLearningPathProgress.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedLearningPathProgress.count} learning path progress records`);

    const deletedSkillProgress = await prisma.userSkillProgress.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedSkillProgress.count} skill progress records`);

    const deletedGoals = await prisma.userGoal.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedGoals.count} user goal records`);

    const deletedAchievements = await prisma.userAchievement.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedAchievements.count} user achievement records`);

    const deletedLearningAnalytics = await prisma.learningAnalytics.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedLearningAnalytics.count} learning analytics records`);

    const deletedSkillAssessments = await prisma.skillAssessment.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedSkillAssessments.count} skill assessment records`);

    const deletedSkillGapAnalyses = await prisma.skillGapAnalysis.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedSkillGapAnalyses.count} skill gap analysis records`);

    const deletedResumes = await prisma.resume.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedResumes.count} resume records`);

    const deletedResourceRatings = await prisma.resourceRating.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedResourceRatings.count} resource rating records`);

    // Delete interview-related records
    const deletedInterviewSessions = await prisma.interviewSession.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedInterviewSessions.count} interview session records`);

    const deletedInterviewResponses = await prisma.interviewResponse.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedInterviewResponses.count} interview response records`);

    const deletedInterviewProgress = await prisma.interviewProgress.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedInterviewProgress.count} interview progress records`);

    // Delete audit-related records
    const deletedAuditRuns = await prisma.auditRun.deleteMany({
      where: { triggeredBy: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedAuditRuns.count} audit run records`);

    const deletedAssignedIssues = await prisma.auditIssue.deleteMany({
      where: { assignedToId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedAssignedIssues.count} assigned audit issue records`);

    const deletedIssueComments = await prisma.issueComment.deleteMany({
      where: { userId: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedIssueComments.count} issue comment records`);

    // Finally, delete the users themselves
    const deletedUsers = await prisma.user.deleteMany({
      where: { id: { in: userIdsToDelete } }
    });
    console.log(`  ✅ Deleted ${deletedUsers.count} test users`);

    console.log('\n🎉 Test user cleanup complete!');
    console.log(`Deleted ${testUsersToDelete.length} test users and all their associated data`);
    console.log(`Kept ${usersToKeep.length} admin/system users`);

  } catch (error) {
    console.error('❌ Error during test user cleanup:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
deleteTestUsers()
  .then(() => {
    console.log('✨ All done!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
