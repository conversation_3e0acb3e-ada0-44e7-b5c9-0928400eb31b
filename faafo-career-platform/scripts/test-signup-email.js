/**
 * Test the complete signup email flow
 */

const React = require('react');
const { Resend } = require('resend');
const { render } = require('@react-email/render');
require('dotenv').config({ path: '.env.local' });

// Simple email sending function for testing
async function sendTestEmail({ to, subject, html }) {
  const resend = new Resend(process.env.RESEND_API_KEY);

  const { data, error } = await resend.emails.send({
    from: process.env.EMAIL_FROM || '<EMAIL>',
    to,
    subject,
    html
  });

  if (error) {
    throw error;
  }

  return { success: true, data };
}

async function testSignupEmail() {
  console.log('🧪 Testing complete signup email flow...');
  
  const testEmail = '<EMAIL>';
  const verificationToken = 'test-token-123';
  const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(testEmail)}`;
  
  console.log('📧 Test email:', testEmail);
  console.log('🔗 Verification URL:', verificationUrl);
  console.log('📤 From address:', process.env.EMAIL_FROM);
  
  try {
    console.log('📧 Sending verification email...');
    
    const result = await sendTestEmail({
      to: testEmail,
      subject: '🚀 Welcome to FAAFO! Time to verify and start the chaos',
      html: `
        <h1>Welcome to FAAFO Career Platform!</h1>
        <p>Hi ${testEmail},</p>
        <p>Welcome to FAAFO Career Platform! To get started, please verify your email address by clicking the button below:</p>
        <a href="${verificationUrl}" style="background-color: #374151; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">Verify Email</a>
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p>${verificationUrl}</p>
        <p>Thanks,<br>The FAAFO Team</p>
      `
    });
    
    console.log('✅ Email sent successfully!');
    console.log('📧 Result:', result);
    
  } catch (error) {
    console.error('❌ Error sending email:', error);
  }
}

testSignupEmail();
