import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function addGeneralWelcomeMessage() {
  console.log('🔍 Adding welcome message for General Discussion...');

  try {
    // Get admin user
    const adminUser = await prisma.user.findFirst({
      where: { 
        email: '<EMAIL>'
      }
    });

    if (!adminUser) {
      console.log('❌ Admin user not found');
      return;
    }

    // Find the general category
    const generalCategory = await prisma.forumCategory.findFirst({
      where: { slug: 'general' }
    });

    if (!generalCategory) {
      console.log('❌ General category not found');
      return;
    }

    if (generalCategory.postCount > 0) {
      console.log('⏭️  General category already has posts');
      return;
    }

    // Create welcome message for General Discussion
    await prisma.forumPost.create({
      data: {
        title: '💬 Welcome to General Discussion!',
        content: `Welcome to General Discussion! 💬

This is our catch-all space for:
• General career and learning discussions
• Random thoughts and observations
• Off-topic conversations (career-related)
• Community building and getting to know each other

Sometimes the best conversations happen when we're not trying too hard. Share whatever's on your mind related to careers, work, or life!

Let's chat! 🗣️`,
        authorId: adminUser.id,
        categoryId: generalCategory.id,
        isPinned: true,
        createdAt: new Date()
      }
    });

    // Update category counts
    await prisma.forumCategory.update({
      where: { id: generalCategory.id },
      data: {
        postCount: 1,
        lastPostAt: new Date(),
        lastPostBy: adminUser.id
      }
    });

    // Update admin user's post count
    await prisma.profile.update({
      where: { userId: adminUser.id },
      data: {
        forumPostCount: { increment: 1 }
      }
    });

    console.log('✅ Created welcome message for General Discussion');

  } catch (error) {
    console.error('❌ Error adding welcome message:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addGeneralWelcomeMessage()
  .then(() => {
    console.log('✨ All done!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
