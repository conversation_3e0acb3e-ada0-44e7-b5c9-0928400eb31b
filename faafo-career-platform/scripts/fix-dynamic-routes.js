#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Adding dynamic route configuration to complex API routes...');

// Get all files that use consolidatedCache or optimizedAIService
const result = execSync('grep -r "consolidatedCache\\|optimizedAIService" src/app/api/ --include="*.ts" -l', { 
  encoding: 'utf8',
  cwd: process.cwd()
});

const files = result.trim().split('\n').filter(f => f.length > 0);

console.log(`📁 Found ${files.length} files that need dynamic route configuration`);

const dynamicConfig = `
// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const revalidate = false;`;

let fixedCount = 0;
let errorCount = 0;
let skippedCount = 0;

files.forEach(filePath => {
  try {
    console.log(`🔄 Processing: ${filePath}`);
    
    // Read file content
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if already has dynamic configuration
    if (content.includes("export const dynamic = 'force-dynamic'")) {
      console.log(`⚠️  Already configured: ${filePath}`);
      skippedCount++;
      return;
    }
    
    // Find the first import statement
    const lines = content.split('\n');
    let insertIndex = -1;
    
    // Find the last import statement
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '' && insertIndex > -1) {
        // Found empty line after imports
        break;
      } else if (!lines[i].startsWith('import ') && !lines[i].startsWith('//') && lines[i].trim() !== '' && insertIndex > -1) {
        // Found non-import, non-comment line
        break;
      }
    }
    
    if (insertIndex === -1) {
      console.log(`⚠️  Could not find import section: ${filePath}`);
      skippedCount++;
      return;
    }
    
    // Insert dynamic configuration after imports
    lines.splice(insertIndex, 0, dynamicConfig);
    
    const fixedContent = lines.join('\n');
    
    // Write back the fixed content
    fs.writeFileSync(filePath, fixedContent, 'utf8');
    console.log(`✅ Fixed: ${filePath}`);
    fixedCount++;
    
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    errorCount++;
  }
});

console.log('\n📊 Summary:');
console.log(`✅ Fixed: ${fixedCount} files`);
console.log(`⚠️  Skipped: ${skippedCount} files (already configured)`);
console.log(`❌ Errors: ${errorCount} files`);
console.log(`📁 Total processed: ${files.length} files`);

if (fixedCount > 0) {
  console.log('\n🎉 Dynamic route configurations have been added!');
  console.log('💡 You should now run: npm run build');
} else {
  console.log('\n🤔 No files needed fixing.');
}
