#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing all AI routes with dynamic imports...');

const routesToFix = [
  'src/app/api/ai/resume-analysis/route.ts',
  'src/app/api/ai/skills-analysis/comprehensive/route.ts',
  'src/app/api/ai/skills-analysis/route.ts'
];

const problematicImports = [
  'import { consolidatedCache } from \'@/lib/services/consolidated-cache-service\';',
  'import { optimizedAIService } from \'@/lib/optimized-ai-service\';',
  'import { geminiService } from \'@/lib/services/geminiService\';',
  'import { withUnifiedErrorHandling } from \'@/lib/unified-api-error-handler\';',
  'import { withRateLimit, rateLimiters } from \'@/lib/rate-limit\';',
  'import prisma from \'@/lib/prisma\';'
];

function fixRoute(filePath) {
  console.log(`🔄 Processing: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if already has dynamic configuration
  if (content.includes("export const dynamic = 'force-dynamic'")) {
    console.log(`⚠️  Already has dynamic config: ${filePath}`);
  } else {
    // Add dynamic configuration after imports
    const lines = content.split('\n');
    let insertIndex = -1;
    
    // Find the last import statement
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '' && insertIndex > -1) {
        break;
      } else if (!lines[i].startsWith('import ') && !lines[i].startsWith('//') && lines[i].trim() !== '' && insertIndex > -1) {
        break;
      }
    }
    
    if (insertIndex > -1) {
      const dynamicConfig = `
// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const revalidate = false;`;
      lines.splice(insertIndex, 0, dynamicConfig);
      content = lines.join('\n');
    }
  }
  
  // Remove problematic imports
  problematicImports.forEach(importLine => {
    content = content.replace(importLine, '');
  });
  
  // Replace export handlers with dynamic import versions
  content = content.replace(
    /export const (GET|POST|PUT|DELETE) = withUnifiedErrorHandling\(/g,
    'export async function $1(request: NextRequest): Promise<NextResponse<any>> {\n  try {\n    // Dynamic imports to prevent build-time analysis\n    const [\n      { consolidatedCache },\n      { optimizedAIService },\n      { geminiService },\n      { withRateLimit, rateLimiters },\n      prisma\n    ] = await Promise.all([\n      import(\'@/lib/services/consolidated-cache-service\'),\n      import(\'@/lib/optimized-ai-service\'),\n      import(\'@/lib/services/geminiService\'),\n      import(\'@/lib/rate-limit\'),\n      import(\'@/lib/prisma\').then(m => m.default)\n    ]);\n\n    const originalHandler = ('
  );
  
  // Close the handlers properly
  content = content.replace(
    /\}\s*\);\s*$/gm,
    '    );\n    return await originalHandler(request);\n  } catch (error: any) {\n    console.error(\'API error:\', error);\n    return NextResponse.json(\n      { error: error.message || \'Internal server error\' },\n      { status: error.statusCode || 500 }\n    );\n  }\n}'
  );
  
  // Write back the fixed content
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`✅ Fixed: ${filePath}`);
  return true;
}

let fixedCount = 0;
let errorCount = 0;

routesToFix.forEach(filePath => {
  try {
    if (fixRoute(filePath)) {
      fixedCount++;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    errorCount++;
  }
});

console.log('\n📊 Summary:');
console.log(`✅ Fixed: ${fixedCount} files`);
console.log(`❌ Errors: ${errorCount} files`);

if (fixedCount > 0) {
  console.log('\n🎉 AI routes have been fixed with dynamic imports!');
  console.log('💡 You should now run: npm run build');
} else {
  console.log('\n🤔 No files needed fixing.');
}
