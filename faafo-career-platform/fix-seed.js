const fs = require('fs');

// Read the seed file
let content = fs.readFileSync('prisma/seed.ts', 'utf8');

// More specific fixes for the problematic patterns
content = content.replace(/(\s+)pros: \[\s*\n/g, '$1pros: JSON.stringify([\n');
content = content.replace(/(\s+)\],\s*\n(\s+)cons: \[\s*\n/g, '$1]),\n$2cons: JSON.stringify([\n');
content = content.replace(/(\s+)\],\s*\n(\s+)actionableSteps:/g, '$1]),\n$2actionableSteps:');

// Write back the fixed content
fs.writeFileSync('prisma/seed.ts', content);

console.log('Fixed seed.ts file with improved patterns');
