const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function auditDatabaseIntegrity() {
  console.log('🔍 CRITICAL AUDIT: Database Integrity Check');
  console.log('==========================================\n');

  try {
    // 1. Verify total active resources claim (should be 90)
    console.log('1. CHECKING TOTAL ACTIVE RESOURCES:');
    const totalResources = await prisma.learningResource.count({
      where: { isActive: true }
    });
    console.log(`   Total active resources: ${totalResources}`);
    console.log(`   Previous agent claimed: 90`);
    console.log(`   ✓ Match: ${totalResources === 90 ? 'YES' : 'NO'}\n`);

    // 2. Check for empty career paths (should be 0)
    console.log('2. CHECKING FOR EMPTY CAREER PATHS:');
    const emptyCareerPaths = await prisma.careerPath.findMany({
      include: {
        learningResources: {
          where: { isActive: true }
        }
      }
    });

    const pathsWithoutResources = emptyCareerPaths.filter(path =>
      path.learningResources.length === 0
    );

    console.log(`   Career paths without resources: ${pathsWithoutResources.length}`);
    console.log(`   Previous agent claimed: 0`);
    console.log(`   ✓ Match: ${pathsWithoutResources.length === 0 ? 'YES' : 'NO'}`);
    
    if (pathsWithoutResources.length > 0) {
      console.log('   🚨 EMPTY CAREER PATHS FOUND:');
      pathsWithoutResources.forEach(path => {
        console.log(`      - ${path.title} (ID: ${path.id})`);
      });
    }
    console.log('');

    // 3. Check for broken URLs (should be 0)
    console.log('3. CHECKING FOR BROKEN/MISSING URLS:');
    const brokenUrls = await prisma.learningResource.count({
      where: {
        isActive: true,
        url: {
          contains: 'example.com'
        }
      }
    });

    const emptyUrls = await prisma.learningResource.count({
      where: {
        isActive: true,
        url: ''
      }
    });

    const totalBrokenUrls = brokenUrls + emptyUrls;
    console.log(`   Resources with broken/missing URLs: ${totalBrokenUrls}`);
    console.log(`   Previous agent claimed: 0`);
    console.log(`   ✓ Match: ${totalBrokenUrls === 0 ? 'YES' : 'NO'}\n`);

    // 4. Check for orphaned relationships (this is handled by the many-to-many relation)
    console.log('4. CHECKING FOR ORPHANED RELATIONSHIPS:');
    // Since this is a direct many-to-many relationship, orphaned records are less likely
    // But we can check for any inconsistencies
    console.log(`   Using direct many-to-many relationship - checking consistency...`);
    console.log(`   ✓ Relationship integrity maintained by Prisma\n`);

    // 5. Detailed career path resource counts
    console.log('5. DETAILED CAREER PATH RESOURCE COUNTS:');
    const careerPathCounts = await prisma.careerPath.findMany({
      include: {
        learningResources: {
          where: { isActive: true }
        }
      }
    });

    careerPathCounts.forEach(path => {
      const resourceCount = path.learningResources.length;
      console.log(`   ${path.name}: ${resourceCount} resources`);
    });

    // 6. Check for placeholder/test data
    console.log('\n6. CHECKING FOR PLACEHOLDER/TEST DATA:');
    const placeholderData = await prisma.learningResource.findMany({
      where: {
        isActive: true,
        OR: [
          { title: { contains: 'test' } },
          { title: { contains: 'Test' } },
          { title: { contains: 'placeholder' } },
          { title: { contains: 'TODO' } },
          { url: { contains: 'example.com' } },
          { author: 'N/A' }
        ]
      }
    });

    console.log(`   Placeholder/test resources found: ${placeholderData.length}`);
    console.log(`   Expected: 0`);
    console.log(`   ✓ Clean: ${placeholderData.length === 0 ? 'YES' : 'NO'}`);
    
    if (placeholderData.length > 0) {
      console.log('   🚨 PLACEHOLDER DATA FOUND:');
      placeholderData.forEach(resource => {
        console.log(`      - ${resource.title} (${resource.url})`);
      });
    }

    console.log('\n==========================================');
    console.log('🎯 AUDIT SUMMARY:');
    console.log(`Total Resources: ${totalResources} (claimed: 90)`);
    console.log(`Empty Career Paths: ${pathsWithoutResources.length} (claimed: 0)`);
    console.log(`Broken URLs: ${totalBrokenUrls} (claimed: 0)`);
    console.log(`Orphaned Records: N/A (direct relationship)`);
    console.log(`Placeholder Data: ${placeholderData.length} (claimed: 0)`);
    console.log('==========================================');

  } catch (error) {
    console.error('❌ Database audit failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

auditDatabaseIntegrity();
